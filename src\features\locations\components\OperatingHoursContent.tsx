"use client";
import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { OperatingHours } from "@/components/common/OperatingHours";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { weeklyScheduleSchema, type WeeklyScheduleData } from "@/lib/utils/validation";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import {
	useOrganizationOperatingHours,
	useUpdateOrganizationOperatingHours,
} from "@/features/organizations/hooks/useOrganizations";
import {
	useLocationOperatingHours,
	useUpdateLocationOperatingHours,
} from "@/features/locations/hooks/useLocations";
import {
	useStationOperatingHours,
	useUpdateStationOperatingHours,
} from "@/features/locations/hooks/useStations";
import type { OrganizationOperatingHour, TimeSlot, LocationOperatingHoursResponse, UpdateOperatingHoursRequest } from "@/features/organizations/api/organizationsApi";
import Loader from "@/components/Loader";
import { toast } from "sonner";

// Form schema that includes the weekly schedule
const formSchema = z.object({
	schedule: weeklyScheduleSchema,
	slotDuration: z.number().min(5).max(120),
	appliesTo: z.object({
		schedule: z.boolean(),
		waitlist: z.boolean(),
	}),
});

type FormData = z.infer<typeof formSchema>;

// Props to accept context for location/station selection
interface OperatingHoursContentProps {
	selectedLocationId?: string | null;
	selectedStationId?: string | null;
}

// Helper function to convert API response to OperatingHours component format
const convertApiDataToScheduleFormat = (
	apiData: OrganizationOperatingHour[]
): WeeklyScheduleData => {
	const dayMap: Record<number, keyof WeeklyScheduleData> = {
		0: "monday",
		1: "tuesday",
		2: "wednesday",
		3: "thursday",
		4: "friday",
		5: "saturday",
		6: "sunday",
	};

	const defaultSchedule: WeeklyScheduleData = {
		monday: { enabled: false, slots: [] },
		tuesday: { enabled: false, slots: [] },
		wednesday: { enabled: false, slots: [] },
		thursday: { enabled: false, slots: [] },
		friday: { enabled: false, slots: [] },
		saturday: { enabled: false, slots: [] },
		sunday: { enabled: false, slots: [] },
	};

	apiData.forEach((dayData) => {
		const dayKey = dayMap[dayData.weekday];
		if (dayKey) {
			// Handle days with empty time slots - they should still be editable
			const timeSlots = Array.isArray(dayData.time_slot) ? dayData.time_slot : [];

			defaultSchedule[dayKey] = {
				enabled: dayData.is_enabled,
				slots: timeSlots.length > 0
					? timeSlots
						.filter(slot => slot.is_active !== false) // Only include active slots
						.map((slot, index) => ({
							id: `${dayData.weekday}-${index}`,
							startTime: convertTo12HourFormat(slot.start_time),
							endTime: convertTo12HourFormat(slot.end_time),
						}))
					: [], // Empty slots array - user can still add time slots
			};
		}
	});

	return defaultSchedule;
};

// Helper function to convert 24-hour format to 12-hour format
const convertTo12HourFormat = (time: string): string => {
	const [hours, minutes] = time.split(':');
	const hour = parseInt(hours, 10);
	const ampm = hour >= 12 ? 'PM' : 'AM';
	const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
	return `${displayHour.toString().padStart(2, '0')}:${minutes} ${ampm}`;
};

// Helper function to convert 12-hour format to 24-hour format
const convertTo24HourFormat = (time: string): string => {
	const [timePart, ampm] = time.split(' ');
	const [hours, minutes] = timePart.split(':');
	let hour = parseInt(hours, 10);

	if (ampm === 'PM' && hour !== 12) {
		hour += 12;
	} else if (ampm === 'AM' && hour === 12) {
		hour = 0;
	}

	return `${hour.toString().padStart(2, '0')}:${minutes}:00`;
};

// Helper function to convert schedule format to API format
// This handles all scenarios: existing slots, newly added slots, empty slots, etc.
const convertScheduleToApiFormat = (schedule: WeeklyScheduleData): UpdateOperatingHoursRequest => {
	const dayMap: Record<keyof WeeklyScheduleData, { weekday: number; name: string }> = {
		monday: { weekday: 0, name: 'Monday' },
		tuesday: { weekday: 1, name: 'Tuesday' },
		wednesday: { weekday: 2, name: 'Wednesday' },
		thursday: { weekday: 3, name: 'Thursday' },
		friday: { weekday: 4, name: 'Friday' },
		saturday: { weekday: 5, name: 'Saturday' },
		sunday: { weekday: 6, name: 'Sunday' },
	};

	return Object.entries(schedule).map(([dayKey, dayData]) => ({
		weekday: dayMap[dayKey as keyof WeeklyScheduleData].weekday,
		week_day_name: dayMap[dayKey as keyof WeeklyScheduleData].name,
		is_enabled: dayData.enabled,
		// Use 'time_slot' (not 'time_slots') to match API format
		time_slot: dayData.slots.map((slot) => ({
			start_time: convertTo24HourFormat(slot.startTime),
			end_time: convertTo24HourFormat(slot.endTime),
		})),
	}));
};

export const OperatingHoursContent = ({
	selectedLocationId = null,
	selectedStationId = null
}: OperatingHoursContentProps = {}) => {
	const [validationErrors, setValidationErrors] = React.useState<
		Record<string, string[]>
	>({});

	const { organizationId } = useOrganizationContext();

	// Determine which level we're operating at
	const isStationLevel = selectedLocationId && selectedStationId;
	const isLocationLevel = selectedLocationId && !selectedStationId;
	const isOrganizationLevel = !selectedLocationId && !selectedStationId;

	// Context-aware data fetching
	const {
		data: organizationOperatingHours,
		isLoading: isLoadingOrganization,
		error: organizationError,
	} = useOrganizationOperatingHours(organizationId, !!isOrganizationLevel);

	const {
		data: locationOperatingHours,
		isLoading: isLoadingLocation,
		error: locationError,
	} = useLocationOperatingHours(selectedLocationId, organizationId, !!isLocationLevel);

	const {
		data: stationOperatingHours,
		isLoading: isLoadingStation,
		error: stationError,
	} = useStationOperatingHours(
		selectedLocationId,
		selectedStationId,
		organizationId,
		!!isStationLevel
	);

	// Context-aware mutations
	const updateOrganizationMutation = useUpdateOrganizationOperatingHours();
	const updateLocationMutation = useUpdateLocationOperatingHours();
	const updateStationMutation = useUpdateStationOperatingHours();

	// Get the appropriate data and loading state
	// Organization returns OrganizationOperatingHour[], Location/Station returns LocationOperatingHoursResponse["data"]
	const operatingHoursData = isStationLevel
		? (stationOperatingHours as LocationOperatingHoursResponse["data"])?.hours
		: isLocationLevel
			? (locationOperatingHours as LocationOperatingHoursResponse["data"])?.hours
			: (organizationOperatingHours as OrganizationOperatingHour[]);

	// Extract inheritance info for location/station (only available for location/station)
	const inheritanceInfo = isStationLevel
		? (stationOperatingHours as LocationOperatingHoursResponse["data"])?.inheritance_info
		: isLocationLevel
			? (locationOperatingHours as LocationOperatingHoursResponse["data"])?.inheritance_info
			: null;

	const isLoading = isStationLevel
		? isLoadingStation
		: isLocationLevel
			? isLoadingLocation
			: isLoadingOrganization;

	const error = isStationLevel
		? stationError
		: isLocationLevel
			? locationError
			: organizationError;

	const form = useForm<FormData>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			slotDuration: 30,
			appliesTo: {
				schedule: true,
				waitlist: false,
			},
			schedule: {
				monday: { enabled: false, slots: [] },
				tuesday: { enabled: false, slots: [] },
				wednesday: { enabled: false, slots: [] },
				thursday: { enabled: false, slots: [] },
				friday: { enabled: false, slots: [] },
				saturday: { enabled: false, slots: [] },
				sunday: { enabled: false, slots: [] },
			},
		},
	});

	// Update form when data is loaded
	React.useEffect(() => {
		if (operatingHoursData && operatingHoursData.length > 0) {
			const scheduleData = convertApiDataToScheduleFormat(operatingHoursData);
			form.setValue('schedule', scheduleData);
		}
	}, [operatingHoursData, form]);

	const handleSubmit = async (data: FormData) => {
		// Check if there are any validation errors
		const hasValidationErrors = Object.values(validationErrors).some(
			(errors) => errors.length > 0
		);

		if (hasValidationErrors) {
			toast.error("Please fix validation errors before saving");
			return;
		}

		if (!organizationId) {
			toast.error("Organization ID is required");
			return;
		}

		try {
			const apiData = convertScheduleToApiFormat(data.schedule);

			if (isStationLevel && selectedLocationId && selectedStationId) {
				await updateStationMutation.mutateAsync({
					locationId: selectedLocationId,
					stationId: selectedStationId,
					organizationId,
					data: apiData, // Send the array directly
				});
			} else if (isLocationLevel && selectedLocationId) {
				await updateLocationMutation.mutateAsync({
					locationId: selectedLocationId,
					organizationId,
					data: apiData, // Send the array directly
				});
			} else {
				await updateOrganizationMutation.mutateAsync({
					organizationId,
					data: apiData, // Send the array directly
				});
			}
		} catch (error) {
			console.error("Failed to save operating hours:", error);
		}
	};

	// Get context-specific title and description
	const getTitle = () => {
		if (isStationLevel) return "Station Operating Hours";
		if (isLocationLevel) return "Location Operating Hours";
		return "Organization Operating Hours";
	};

	const getDescription = () => {
		if (isStationLevel) return "Set the operating hours for this specific station.";
		if (isLocationLevel) return "Set the operating hours for this location. These will override organization defaults.";
		return "Set the default operating hours for your organization. These will apply to all locations unless overridden.";
	};

	if (isLoading) {
		return (
			<div className="flex items-center justify-center p-8">
				<Loader />
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-center p-8">
				<p className="text-red-600">
					Failed to load operating hours.
				</p>
				<Button
					variant="outline"
					className="mt-4"
					onClick={() => window.location.reload()}
				>
					Try Again
				</Button>
			</div>
		);
	}

	// Get inheritance message
	const getInheritanceMessage = () => {
		if (!inheritanceInfo) return null;

		if (inheritanceInfo.has_own_hours) {
			return {
				type: "custom" as const,
				message: isStationLevel
					? "This station has custom operating hours"
					: "This location has custom operating hours"
			};
		} else {
			const source = inheritanceInfo.inheriting_from === "organization" ? "organization" : "location";
			return {
				type: "inherited" as const,
				message: isStationLevel
					? `This station is inheriting operating hours from the ${source}`
					: `This location is inheriting operating hours from the ${source}`
			};
		}
	};

	const inheritanceMessage = getInheritanceMessage();

	const isSubmitting = updateOrganizationMutation.isPending ||
		updateLocationMutation.isPending ||
		updateStationMutation.isPending;

	return (
		<div className="mx-auto mt-2.5 max-w-4xl space-y-6">
			{/* Inheritance Info Banner */}
			{inheritanceMessage && (
				<div className={`p-3 rounded-lg border ${inheritanceMessage.type === "inherited"
					? "bg-blue-50 border-blue-200 text-blue-800"
					: "bg-green-50 border-green-200 text-green-800"
					}`}>
					<div className="flex items-center gap-2">
						{inheritanceMessage.type === "inherited" ? (
							<svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
								<path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
							</svg>
						) : (
							<svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
								<path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
							</svg>
						)}
						<span className="font-medium text-sm">
							{inheritanceMessage.message}
						</span>
					</div>
					{inheritanceMessage.type === "inherited" && (
						<p className="mt-2 text-sm opacity-80">
							Any changes you make here will create custom operating hours for this {isStationLevel ? 'station' : 'location'}.
						</p>
					)}
				</div>
			)}

			<Card className="border-none p-0 shadow-none">
				<CardHeader className="px-0">
					<CardTitle>{getTitle()}</CardTitle>
					<CardDescription>{getDescription()}</CardDescription>
				</CardHeader>
				<CardContent className="p-0">
					<Form {...form}>
						<form
							onSubmit={form.handleSubmit(handleSubmit)}
							className="space-y-6"
						>
							{/* Weekly Schedule Component with controlled props */}
							<FormField
								control={form.control}
								name="schedule"
								render={({ field }) => (
									<FormItem>
										<FormControl>
											<OperatingHours
												value={field.value}
												onChange={field.onChange}
												onValidationChange={
													setValidationErrors
												}
												slotDuration={form.watch(
													"slotDuration"
												)}
												onSlotDurationChange={(
													duration
												) =>
													form.setValue(
														"slotDuration",
														duration
													)
												}
												appliesTo={form.watch(
													"appliesTo"
												)}
												onAppliesToChange={(
													appliesTo
												) =>
													form.setValue(
														"appliesTo",
														appliesTo
													)
												}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Submit Button */}
							<div className="flex justify-end">
								<Button
									type="submit"
									className="cursor-pointer"
									disabled={isSubmitting}
								>
									{isSubmitting
										? "Saving..."
										: "Save Operating Hours"}
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
};
