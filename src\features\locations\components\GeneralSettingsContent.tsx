import { useState } from "react";
import * as React from "react";
import { User, Video, Volume2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import { cn } from "@/lib/utils";
import { ExpirationSettings } from "@/components/common/ExpirationSettings";
import { DualRangeSlider } from "@/components/ui/dual-range-slider";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import {
	useOrganizationScheduleSettings,
	useUpdateOrganizationScheduleSettings,
} from "@/features/organizations/hooks/useOrganizations";
import {
	useLocationScheduleSettings,
	useUpdateLocationScheduleSettings,
} from "@/features/locations/hooks/useLocations";
import {
	useStationScheduleSettings,
	useUpdateStationScheduleSettings,
} from "@/features/locations/hooks/useStations";
import type {
	ScheduleSettings,
	UpdateScheduleSettingsRequest,
	LocationScheduleSettingsResponse
} from "@/features/organizations/api/organizationsApi";
import Loader from "@/components/Loader";
import { toast } from "sonner";

interface GeneralSettings {
	scheduleVisibility: boolean;
	appointmentMethods: {
		inPerson: boolean;
		video: boolean;
		audio: boolean;
	};
	autoApprove: boolean;
	chat: boolean;
	chatMethod: "one-way" | "both-parties";
	scheduleBlock: boolean;
	scheduleBlockDate: Date | null;
	scheduleBlockWeeks: number;
	scheduleBlockSpecificDates: boolean;
	scheduleBlockTillSpecificDates: boolean;
}

// Props to accept context for location/station selection
interface GeneralSettingsContentProps {
	selectedLocationId?: string | null;
	selectedStationId?: string | null;
}

export const GeneralSettingsContent = ({
	selectedLocationId = null,
	selectedStationId = null
}: GeneralSettingsContentProps = {}) => {
	const { organizationId } = useOrganizationContext();

	// Determine which level we're operating at
	const isStationLevel = selectedLocationId && selectedStationId;
	const isLocationLevel = selectedLocationId && !selectedStationId;
	const isOrganizationLevel = !selectedLocationId && !selectedStationId;

	// Context-aware data fetching
	const {
		data: organizationScheduleSettings,
		isLoading: isLoadingOrganization,
		error: organizationError,
	} = useOrganizationScheduleSettings(organizationId!, !!isOrganizationLevel && !!organizationId);

	const {
		data: locationScheduleSettings,
		isLoading: isLoadingLocation,
		error: locationError,
	} = useLocationScheduleSettings(selectedLocationId, organizationId!, !!isLocationLevel && !!organizationId);

	const {
		data: stationScheduleSettings,
		isLoading: isLoadingStation,
		error: stationError,
	} = useStationScheduleSettings(
		selectedLocationId,
		selectedStationId,
		organizationId!,
		!!isStationLevel && !!organizationId
	);

	// Context-aware mutations
	const updateOrganizationMutation = useUpdateOrganizationScheduleSettings();
	const updateLocationMutation = useUpdateLocationScheduleSettings();
	const updateStationMutation = useUpdateStationScheduleSettings();

	// Get the appropriate data and loading state
	const scheduleSettingsData = isStationLevel
		? (stationScheduleSettings as LocationScheduleSettingsResponse["data"])
		: isLocationLevel
			? (locationScheduleSettings as LocationScheduleSettingsResponse["data"])
			: (organizationScheduleSettings as ScheduleSettings);

	// Extract inheritance info for location/station
	const inheritanceInfo = isStationLevel
		? (stationScheduleSettings as LocationScheduleSettingsResponse["data"])?.inheritance_info
		: isLocationLevel
			? (locationScheduleSettings as LocationScheduleSettingsResponse["data"])?.inheritance_info
			: null;

	const isLoading = isStationLevel
		? isLoadingStation
		: isLocationLevel
			? isLoadingLocation
			: isLoadingOrganization;

	const error = isStationLevel
		? stationError
		: isLocationLevel
			? locationError
			: organizationError;
	const [settings, setSettings] = useState<GeneralSettings>({
		scheduleVisibility: true,
		appointmentMethods: {
			inPerson: true,
			video: false,
			audio: false,
		},
		autoApprove: true,
		chat: true,
		chatMethod: "both-parties",
		scheduleBlock: true,
		scheduleBlockDate: new Date("2025-08-04"),
		scheduleBlockWeeks: 4,
		scheduleBlockSpecificDates: true,
		scheduleBlockTillSpecificDates: false,
	});

	// Update local state when API data loads
	React.useEffect(() => {
		if (scheduleSettingsData) {
			setSettings({
				scheduleVisibility: scheduleSettingsData.schedule_visibility,
				appointmentMethods: {
					inPerson: scheduleSettingsData.method.in_person,
					video: scheduleSettingsData.method.video,
					audio: scheduleSettingsData.method.audio,
				},
				autoApprove: scheduleSettingsData.auto_approve,
				chat: scheduleSettingsData.chat.is_enabled,
				chatMethod: scheduleSettingsData.chat.type === "both_parties" ? "both-parties" : "one-way",
				scheduleBlock: scheduleSettingsData.block.is_enabled,
				scheduleBlockDate: scheduleSettingsData.block.after.date ? new Date(scheduleSettingsData.block.after.date) : null,
				scheduleBlockWeeks: scheduleSettingsData.block.till.value,
				scheduleBlockSpecificDates: scheduleSettingsData.block.after.is_enabled,
				scheduleBlockTillSpecificDates: scheduleSettingsData.block.till.is_enabled,
			});
		}
	}, [scheduleSettingsData]);

	const updateSettings = (updates: Partial<GeneralSettings>) => {
		setSettings((prev) => ({ ...prev, ...updates }));
	};

	const updateAppointmentMethod = (
		method: keyof GeneralSettings["appointmentMethods"],
		value: boolean
	) => {
		setSettings((prev) => ({
			...prev,
			appointmentMethods: {
				...prev.appointmentMethods,
				[method]: value,
			},
		}));
	};

	const handleSave = async () => {
		try {
			const updateData: UpdateScheduleSettingsRequest = {
				schedule_visibility: settings.scheduleVisibility,
				auto_approve: settings.autoApprove,
				estimated_wait_time: 15, // Default value
				timezone: "America/New_York", // Default value
				method: {
					in_person: settings.appointmentMethods.inPerson,
					video: settings.appointmentMethods.video,
					audio: settings.appointmentMethods.audio,
				},
				chat: {
					is_enabled: settings.chat,
					type: settings.chatMethod === "both-parties" ? "both_parties" : "provider_only",
				},
				block: {
					is_enabled: settings.scheduleBlock,
					after: {
						is_enabled: settings.scheduleBlockSpecificDates,
						date: settings.scheduleBlockDate?.toISOString() || null,
					},
					till: {
						is_enabled: settings.scheduleBlockTillSpecificDates,
						value: settings.scheduleBlockWeeks,
						unit: "weeks",
					},
				},
			};

			if (isStationLevel && selectedLocationId && selectedStationId) {
				await updateStationMutation.mutateAsync({
					locationId: selectedLocationId,
					stationId: selectedStationId,
					organizationId: organizationId!,
					data: updateData,
				});
			} else if (isLocationLevel && selectedLocationId) {
				await updateLocationMutation.mutateAsync({
					locationId: selectedLocationId,
					organizationId: organizationId!,
					data: updateData,
				});
			} else {
				await updateOrganizationMutation.mutateAsync({
					organizationId: organizationId!,
					data: updateData,
				});
			}
		} catch (error) {
			console.error("Failed to save schedule settings:", error);
		}
	};

	// Get inheritance message
	const getInheritanceMessage = () => {
		if (!inheritanceInfo) return null;

		if (inheritanceInfo.has_own_settings) {
			return {
				type: "custom" as const,
				message: isStationLevel
					? "This station has custom schedule settings"
					: "This location has custom schedule settings"
			};
		} else {
			const source = inheritanceInfo.inheriting_from === "organization" ? "organization" : "location";
			return {
				type: "inherited" as const,
				message: isStationLevel
					? `This station is inheriting schedule settings from the ${source}`
					: `This location is inheriting schedule settings from the ${source}`
			};
		}
	};

	const inheritanceMessage = getInheritanceMessage();

	if (isLoading) {
		return (
			<div className="flex items-center justify-center py-8">
				<Loader />
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-red-600 text-center py-4">
				Failed to load schedule settings. Please try again.
			</div>
		);
	}

	return (
		<div className="space-y-4">
			{/* Inheritance Info Banner */}
			{inheritanceMessage && (
				<div className={`p-3 rounded-lg border ${inheritanceMessage.type === "inherited"
					? "bg-blue-50 border-blue-200 text-blue-800"
					: "bg-green-50 border-green-200 text-green-800"
					}`}>
					<div className="flex items-center gap-2">
						{inheritanceMessage.type === "inherited" ? (
							<svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
								<path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
							</svg>
						) : (
							<svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
								<path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
							</svg>
						)}
						<span className="font-medium text-sm">
							{inheritanceMessage.message}
						</span>
					</div>
					{inheritanceMessage.type === "inherited" && (
						<p className="mt-2 text-sm opacity-80">
							Any changes you make here will create custom schedule settings for this {isStationLevel ? 'station' : 'location'}.
						</p>
					)}
				</div>
			)}
			{/* Schedule Visibility */}
			<div className="space-y-3 border-b border-gray-200 py-4">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">
							Schedule Visibility
						</h3>
						<p className="text-sm text-gray-600">
							This enables people to view the availability of this
							business and book appointments
						</p>
					</div>
					<div className="flex items-center gap-1.5">
						<Switch
							checked={settings.scheduleVisibility}
							onCheckedChange={(checked) =>
								updateSettings({ scheduleVisibility: checked })
							}
						/>
						<span className="text-muted">
							{settings.scheduleVisibility ? "On" : "Off"}
						</span>
					</div>
				</div>
			</div>

			{/* Method of Appointments */}
			<div className="space-y-4 border-b border-gray-200 pb-4">
				<h3 className="text-lg font-medium">Method of Appointments</h3>
				<div className="flex gap-6">
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="in-person"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<User className="h-4 w-4" />
								In Person
							</Label>
							<Checkbox
								id="in-person"
								checked={settings.appointmentMethods.inPerson}
								onCheckedChange={(checked) =>
									updateAppointmentMethod(
										"inPerson",
										!!checked
									)
								}
							/>
						</div>
					</Button>
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="video"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<Video className="h-4 w-4" />
								Video
							</Label>
							<Checkbox
								id="video"
								checked={settings.appointmentMethods.video}
								onCheckedChange={(checked) =>
									updateAppointmentMethod("video", !!checked)
								}
							/>
						</div>
					</Button>
					<Button asChild variant="outline">
						<div className="flex items-center space-x-2">
							<Label
								htmlFor="audio"
								className="flex items-center gap-2 text-sm font-medium"
							>
								<Volume2 className="h-4 w-4" />
								Audio
							</Label>
							<Checkbox
								id="audio"
								checked={settings.appointmentMethods.audio}
								onCheckedChange={(checked) =>
									updateAppointmentMethod("audio", !!checked)
								}
							/>
						</div>
					</Button>
				</div>
			</div>

			{/* Auto Approve */}
			<div className="space-y-3 border-b border-gray-200 pb-4">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">Auto Approve</h3>
						<p className="text-sm text-gray-600">
							If there is no conflict, appointments are
							automatically approved
						</p>
					</div>
					<div className="flex items-center gap-1.5">
						<Switch
							checked={settings.autoApprove}
							onCheckedChange={(checked) =>
								updateSettings({ autoApprove: checked })
							}
						/>
						<span className="text-muted">
							{settings.autoApprove ? "On" : "Off"}
						</span>
					</div>
				</div>
			</div>

			{/* Chat */}
			<div className="space-y-4 border-b border-gray-200 pb-4">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">Chat</h3>
						<p className="text-sm text-gray-600">
							If there is no conflict, appointments are
							automatically approved
						</p>
					</div>

					<div className="flex items-center gap-1.5">
						<Switch
							checked={settings.chat}
							onCheckedChange={(checked) =>
								updateSettings({ chat: checked })
							}
						/>
						<span className="text-muted">
							{settings.chat ? "On" : "Off"}
						</span>
					</div>
				</div>

				{settings.chat && (
					<div className="ml-0">
						<RadioGroup
							value={settings.chatMethod}
							onValueChange={(value) =>
								updateSettings({
									chatMethod: value as
										| "one-way"
										| "both-parties",
								})
							}
							className="flex w-full gap-12.5 space-y-4"
						>
							<div className="flex items-start space-x-3">
								<RadioGroupItem
									value="one-way"
									id="one-way"
									className="mt-1"
								/>
								<div className="space-y-1">
									<Label
										htmlFor="one-way"
										className="text-sm font-medium"
									>
										One Way Chat
									</Label>
									<p className="text-sm text-gray-600">
										Only Admins can send messages to
										patients.
									</p>
								</div>
							</div>
							<div className="flex items-start space-x-3">
								<RadioGroupItem
									value="both-parties"
									id="both-parties"
									className="mt-1"
								/>
								<div className="space-y-1">
									<Label
										htmlFor="both-parties"
										className="text-sm font-medium"
									>
										Allow Both Parties to Chat
									</Label>
									<p className="text-sm text-gray-600">
										Both patients and admins can chat
									</p>
								</div>
							</div>
						</RadioGroup>
					</div>
				)}
			</div>

			{/* Schedule Block */}
			<div className="space-y-4 pb-4">
				<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h3 className="text-lg font-medium">Schedule Block</h3>
						<p className="text-sm text-gray-600">
							This allows a buffer time and restricts how far out
							patients can book
						</p>
					</div>
					<div className="flex items-center gap-1.5">
						<Switch
							checked={settings.scheduleBlock}
							onCheckedChange={(checked) =>
								updateSettings({ scheduleBlock: checked })
							}
						/>
						<span className="text-muted">
							{settings.scheduleBlock ? "On" : "Off"}
						</span>
					</div>
				</div>

				{settings.scheduleBlock && (
					<div className="ml-0 space-y-6">
						{/* Date Range Selection */}
						<div className="flex items-center justify-between gap-4">
							<div className="flex flex-col items-center gap-2.5">
								<div className="flex items-center gap-2.5">
									<span className="text-sm font-medium">
										After
									</span>
									<DatePicker
										value={
											settings.scheduleBlockDate ||
											undefined
										}
										onChange={(date) =>
											updateSettings({
												scheduleBlockDate:
													date as Date | null,
											})
										}
										variant="default"
										className="w-auto"
									/>
								</div>
								<div className="flex items-center space-x-2">
									<Checkbox
										id="specific-dates-before"
										checked={
											settings.scheduleBlockSpecificDates
										}
										onCheckedChange={(checked) =>
											updateSettings({
												scheduleBlockSpecificDates:
													!!checked,
											})
										}
									/>
									<Label
										htmlFor="specific-dates-before"
										className="text-sm"
									>
										Select Specific Date(s)
									</Label>
								</div>
							</div>
							{/* Slider for Weeks */}
							<div className="flex-1 space-y-3">
								<DualRangeSlider
									value={[settings.scheduleBlockWeeks, settings.scheduleBlockWeeks]}
									onValueChange={(value: number[]) =>
										updateSettings({
											scheduleBlockWeeks: value[0],
										})
									}
									min={1}
									max={52}
									className="w-full"
								/>
							</div>
							<div className="flex flex-col items-center gap-2.5">
								<div className="flex items-center gap-2.5">
									<span className="text-sm font-medium">
										Till
									</span>
									<ExpirationSettings
										value={settings.scheduleBlockWeeks.toString()}
										onValueChange={(value) =>
											updateSettings({
												scheduleBlockWeeks:
													parseInt(value),
											})
										}
										onUnitChange={() => { }}
										unit="weeks"
										useSpecificDate={false}
										onUseSpecificDateChange={() => { }}
										label=""
										units={[
											{ value: "hours", label: "Hours" },
											{
												value: "minutes",
												label: "Minutes",
											},
											{ value: "weeks", label: "Weeks" },
											{ value: "days", label: "Days" },
										]}
										showAlternativeOption={false}
										containerWidth="w-[145px]"
									/>
								</div>
								<div className="flex items-center space-x-2">
									<Checkbox
										id="specific-dates-after"
										checked={settings.scheduleBlockTillSpecificDates}
										onCheckedChange={(checked) =>
											updateSettings({
												scheduleBlockTillSpecificDates: !!checked,
											})
										}
									/>
									<Label
										htmlFor="specific-dates-after"
										className="text-sm"
									>
										Select Specific Date(s)
									</Label>
								</div>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* Action Buttons */}
			<div className="flex justify-end gap-3 border-t border-gray-200 pt-6">
				<Button variant="outline">Cancel</Button>
				<Button
					onClick={handleSave}
					disabled={
						updateOrganizationMutation.isPending ||
						updateLocationMutation.isPending ||
						updateStationMutation.isPending
					}
				>
					{(updateOrganizationMutation.isPending ||
						updateLocationMutation.isPending ||
						updateStationMutation.isPending) && "Saving..."}
					{!(updateOrganizationMutation.isPending ||
						updateLocationMutation.isPending ||
						updateStationMutation.isPending) && "Save"}
				</Button>
			</div>
		</div>
	);
};
