/**
 * Example usage of the query configuration
 * This file demonstrates how to use the query keys and options
 * You can delete this file after understanding the patterns
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import {
	queryKeys,
	mediumLivedQueryOptions,
	defaultMutationOptions,
} from "./index";

// Example: Fetching user data
export function useUserQuery() {
	return useQuery({
		queryKey: queryKeys.auth.user(),
		queryFn: async () => {
			const response = await axios.get("/api/auth/user");
			return response.data;
		},
		...mediumLivedQueryOptions, // User data doesn't change frequently
	});
}

// Example: Fetching customers with filters
export function useCustomersQuery(filters: Record<string, any>) {
	return useQuery({
		queryKey: queryKeys.customers.list(filters),
		queryFn: async () => {
			const response = await axios.get("/api/customers", {
				params: filters,
			});
			return response.data;
		},
		enabled: Object.keys(filters).length > 0, // Only fetch when filters are provided
	});
}

// Example: Creating a new customer
export function useCreateCustomerMutation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (customerData: any) => {
			const response = await axios.post("/api/customers", customerData);
			return response.data;
		},
		...defaultMutationOptions,
		onSuccess: () => {
			// Invalidate and refetch customers list
			queryClient.invalidateQueries({
				queryKey: queryKeys.customers.lists(),
			});
		},
	});
}

// Example: Prefetching data
export function prefetchCustomers(
	queryClient: any,
	filters: Record<string, any>
) {
	return queryClient.prefetchQuery({
		queryKey: queryKeys.customers.list(filters),
		queryFn: async () => {
			const response = await axios.get("/api/customers", {
				params: filters,
			});
			return response.data;
		},
	});
}
