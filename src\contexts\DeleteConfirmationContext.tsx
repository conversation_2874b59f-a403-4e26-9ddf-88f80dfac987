import React, { createContext, useContext, useState } from "react";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";

interface DeleteConfirmationOptions {
	title?: string;
	description?: string;
	confirmText?: string;
	cancelText?: string;
	onConfirm: () => void | Promise<void>;
	onCancel?: () => void;
}

interface DeleteConfirmationContextType {
	showDeleteConfirmation: (options: DeleteConfirmationOptions) => void;
	hideDeleteConfirmation: () => void;
}

const DeleteConfirmationContext = createContext<
	DeleteConfirmationContextType | undefined
>(undefined);

interface DeleteConfirmationState extends DeleteConfirmationOptions {
	isOpen: boolean;
	isLoading: boolean;
}

export function DeleteConfirmationProvider({
	children,
}: {
	children: React.ReactNode;
}) {
	const [state, setState] = useState<DeleteConfirmationState>({
		isOpen: false,
		isLoading: false,
		title: "",
		description: "",
		confirmText: "Continue",
		cancelText: "Cancel",
		onConfirm: () => {},
	});

	const showDeleteConfirmation = (options: DeleteConfirmationOptions) => {
		setState({
			...options,
			isOpen: true,
			isLoading: false,
		});
	};

	const hideDeleteConfirmation = () => {
		setState((prev) => ({
			...prev,
			isOpen: false,
			isLoading: false,
		}));
	};

	const handleConfirm = async () => {
		setState((prev) => ({ ...prev, isLoading: true }));

		try {
			await state.onConfirm();
			hideDeleteConfirmation();
		} catch (error) {
			console.error("Delete operation failed:", error);
			setState((prev) => ({ ...prev, isLoading: false }));
		}
	};

	const handleCancel = () => {
		if (state.onCancel) {
			state.onCancel();
		}
		hideDeleteConfirmation();
	};

	return (
		<DeleteConfirmationContext.Provider
			value={{ showDeleteConfirmation, hideDeleteConfirmation }}
		>
			{children}
			<DeleteConfirmationDialog
				open={state.isOpen}
				onOpenChange={hideDeleteConfirmation}
				title={state.title}
				description={state.description}
				confirmText={state.confirmText}
				cancelText={state.cancelText}
				onConfirm={handleConfirm}
				onCancel={handleCancel}
				isLoading={state.isLoading}
			/>
		</DeleteConfirmationContext.Provider>
	);
}

export const useDeleteConfirmation = () => {
	const context = useContext(DeleteConfirmationContext);
	if (!context) {
		throw new Error(
			"useDeleteConfirmation must be used within a DeleteConfirmationProvider"
		);
	}
	return context;
};
