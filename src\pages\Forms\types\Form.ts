import type {
	Control,
	UseFormWatch,
	UseFormSetValue,
	UseFormGetValues,
	UseFormClearErrors,
} from "react-hook-form";

export type FieldType =
	| "text"
	| "long_text"
	| "numeric"
	| "number"
	| "date"
	| "date_range"
	| "dropdown"
	| "radio"
	| "checkbox"
	| "attachment"
	| "info_image"
	| "info_text"
	| "scale_1_10"
	| "satisfaction_scale"
	| "agree_disagree"
	| "yes_no"
	| "rating"
	| "email"
	| "phone"
	| "boolean";

export type FieldOption = {
	id: string;
	value: string;
	conditions?: {
		type: "continue" | "submit" | "goto" | "block";
		destination: string;
		logic?: "equals" | "not_equals";
		selected?: boolean;
		conditional_block_message?: string | null;
	};
};

type DateValidationType = "static" | "relative" | "dynamic";

export interface DateValidation {
	type: DateValidationType;
	minDate?: string | null;
	maxDate?: string | null;
	minDateOffset?: {
		value: number;
		unit: "days" | "months" | "years";
		direction: "past" | "future";
	};
	maxDateOffset?: {
		value: number;
		unit: "days" | "months" | "years";
		direction: "past" | "future";
	};
	minDateField?: string;
	maxDateField?: string;
}

export interface DefaultValues {
	reference_field_id?: string | null;
	default_value?: string | null;
	allow_edit?: boolean;
}

export type FormField = {
	id: string;
	title: string;
	type: FieldType;
	order: number;
	description: string | null;
	required: boolean;
	options?: FieldOption[];
	image?: string | null;
	info_text_value?: string | null;
	approved_formats?: Array<"PNG" | "PDF" | "JPEG" | "CSV" | "Word Doc">;
	add_predefined_value?: boolean;
	dateValidation?: DateValidation;
	default_values?: DefaultValues[];
	reference_field_id?: string | null;
	placeholder?: string | null;
};

export type FormSection = {
	id: string;
	title: string;
	description: string | null;
	order: number;
	flow_action: "submit" | "continue" | "block_form";
	flow_target_section_id?: string | null;
	fields: FormField[];
	flow: {
		action: "submit" | "continue";
		targetSection?: string;
	};
};

export type FormDataType = {
	name: string;
	uuid?: string;
	type: "service" | "intake" | "feedback" | "enquiry" | "referral";
	logo_url: string | null;
	banner_url: string | null;
	success_message: string;
	block_message: string;
	submit_button_title: string;
	service_id?: string[];
	service_ids?: string[];
	location_id?: string[];
	location_ids?: string[];
	station_id?: string[];
	station_ids?: string[];
	apply_to?: string[];
	status: "live" | "draft" | "inactive";
	collect_rating?: boolean;
	collect_general_feedback?: boolean;
	general_feedback_title?: string;
	is_auto_approve?: boolean;
	sections: FormSection[];
	services: {
		id: string;
		name: string;
		description: string;
	}[];
};

export interface DraggableFieldProps {
	sectionIndex: number;
	fieldIndex: number;
	field: {
		value: FormField;
		name: number;
		onChange: (value: any) => void;
	};
	control: Control<FormDataType>;
	watch: UseFormWatch<FormDataType>;
	setValue: UseFormSetValue<FormDataType>;
	getValues: UseFormGetValues<FormDataType>;
	clearErrors: UseFormClearErrors<FormDataType>;
	moveField: (
		fromSectionIndex: number,
		fromFieldIndex: number,
		toSectionIndex: number,
		toFieldIndex: number
	) => void;
	hasMultipleFields: boolean;
	isLastField: boolean;
	activeSection: string | null;
	onAddField: (sectionIndex: number, type: FieldType) => void;
	onAddSection: () => void;
	sectionId: string;
	form: any;
}

export interface GetFormsResponse {
	data: FormTypes[];
	meta: {
		pagination: {
			count: number;
			current_page: number;
			per_page: number;
			total: number;
			total_pages: number;
		};
	};
}

export interface FormTypes {
	id: string;
	name: string;
	description: string;
	created_at: string;
	type: "service" | "intake" | "feedback" | "enquiry" | "referral";
	service: {
		id: string;
		name: string;
	};
	services: {
		id: string;
		name: string;
		description: string;
	}[];
	providers: string;
	status: "active" | "inactive" | "draft";
}

export type GetFormsQueryParams = {
	search?: string;
	business_id?: string;
	service_id?: string;
	service_ids?: string;
	location_ids?: string;
	location_id?: number;
	station_ids?: string;
	station_id?: number;
	type?: string;
	page?: string;
	per_page?: string;
	uuid?: string;
};

export type GetFormResponsesQueryParams = {
	search?: string;

	service_ids?: string;
	location_ids?: string;
	station_ids?: string;

	form_types?: string;
	form_statuses?: string;
	statuses?: string;
	flags?: string;
	client_ids?: string;
	date_from?: string;
	date_to?: string;

	page?: string;
	per_page?: string;
};

export type CreateFormResponse = {
	success: boolean;
	message: string;
	data: {
		type: string;
		status: string;
		name: string;
		description: string;
		business_id: number;
		service_id: number;
		location_id: number;
		station_id: number;
		logo_url: string;
		banner_url: string;
		success_message: string;
		submit_button_title: string;
		block_message: string;
		collect_rating: boolean;
		collect_general_feedback: boolean;
		general_feedback_title: string;
		is_auto_approve: boolean;
		validation_fields: any[];
		sections: any[];
		apply_options: any[];
	};
};

export type GetFormResponse = {
	success: boolean;
	message: string;
	data: FormDataType;
};

export type UpdateFormResponse = {
	success: boolean;
	message: string;
	data: {
		type: string;
		status: string;
		name: string;
		description: string;
		business_id: number;
		service_id: number;
		location_id: number;
		station_id: number;
		logo_url: string;
		banner_url: string;
		success_message: string;
		submit_button_title: string;
		block_message: string;
		collect_rating: boolean;
		collect_general_feedback: boolean;
		general_feedback_title: string;
		is_auto_approve: boolean;
		validation_fields: any[];
		sections: any[];
		apply_options: any[];
	};
};

export type DeleteFormResponse = {
	success: boolean;
	message: string;
	data: {
		type: string;
		status: string;
		name: string;
		description: string;
		business_id: number;
		service_id: number;
		location_id: number;
		station_id: number;
		logo_url: string;
		banner_url: string;
		success_message: string;
		submit_button_title: string;
		block_message: string;
		collect_rating: boolean;
		collect_general_feedback: boolean;
		general_feedback_title: string;
		is_auto_approve: boolean;
		validation_fields: any[];
		sections: any[];
		apply_options: any[];
	};
};
