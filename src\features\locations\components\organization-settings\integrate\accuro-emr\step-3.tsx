import migranium from "../../images/migranium.png"
import acurro from "../../images/acurro.png"
import vector from "../../images/vector.png"
import { useRef } from "react";
import { ChevronDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/Button/Button";

export default function IntegrateAccuroEHRStep3() {
    return (
        <div>
            <h1 className="text-[#09090B] font-medium text-lg mb-3">Step 3</h1>
            <div className="bg-[#F4F4F5] py-5 px-6 rounded-xl">
                <h1 className="text-[#27272A] font-medium text-base">
                    Connect to Migranium
                </h1>

                <div className="flex items-center justify-center gap-x-7 mt-7">
                    <div className="border border-[#D4D4D8] p-2 rounded-lg bg-white">
                        <img
                            src={migranium}
                            alt="Migranium"
                            className="size-6"
                        />
                    </div>
                    <img
                        src={vector}
                        alt="Vector"
                        className="size-7"
                    />
                    <div className="rounded-lg bg-white shadow-[0px_4px_20px_0px_#9BAAB42E] p-2 scale-90">
                        <img
                            src={acurro}
                            alt="Accuro"
                            className="size-9"
                        />
                    </div>
                </div>

                <div className="border border-[#E4E4E7] mt-6 shadow-[0px_0px_16px_0px_#9BAAB412] py-5 px-6 rounded-md bg-white">
                    <div
                        className="flex items-center justify-between"
                        role="button"
                        tabIndex={0}
                    >
                        <div className="flex-1">
                            <h1 className="font-medium text-base">EMR Integration Information </h1>
                            <p className="text-[#71717A] font-light text-sm mt-1">View your connection details</p>
                        </div>
                        <ChevronDown
                            size={20}
                            color="#27272A"
                            className={`transition-transform duration-300`}
                        />
                    </div>
                </div>
                <Button className="mt-4 w-[8rem]">
                    Connect
                </Button>
            </div>
        </div>
    )
}