import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import { ToggleButton } from "@/components/common/ToggleButton";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RefactorMultiSelect } from "@/pages/schedules/components/custom-select";

import { useLocationServices } from "@/features/locations/hooks/useServices";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useState, useEffect } from "react";
import { FiUser } from "react-icons/fi";
import { GoCheckCircleFill } from "react-icons/go";
import { MdOutlineVideocam } from "react-icons/md";
import { PiSpeakerHighThin } from "react-icons/pi";

export default function FirstAvailable({
    back,
    next,
    locationId
}: { back: () => void; next: () => void; locationId?: string }) {
    const { organizationId } = useOrganizationContext();
    const isProviderSingle = false;
    const isProviderMultiple = true;
    const [defaultDate, setDefaultDate] = useState<Date>(new Date());
    const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
    const [selectedServices, setSelectedServices] = useState<string[]>([]);
    const [selectedProviders, setSelectedProviders] = useState<string | string[]>("");

    // Fetch services for the current location
    const { data: servicesResponse, isLoading: isLoadingServices } = useLocationServices({
        locationId,
        organizationId: organizationId || undefined,
        enabled: !!locationId && !!organizationId
    });

    // Get full services data and create dropdown options
    const servicesData = servicesResponse?.data || [];
    const services = servicesData.map(service => service.name);

    // Find selected service object
    const selectedServiceName = selectedServices[0] || "";
    const selectedService = servicesData.find(service => service.name === selectedServiceName);

    // Get available appointment methods from selected service
    const availableMethodNames = selectedService?.appointment_methods?.map(method => method.name.toLowerCase().replace(" ", "-")) || [];

    // Get stations for the current location from the selected service
    const currentLocationSelection = selectedService?.location_selections?.find(
        selection => selection.location_id.toString() === locationId
    );
    const locationStations = currentLocationSelection?.stations || [];
    const stationNames = locationStations.map(station => station.name);

    // Use stations as providers (no fallback to hardcoded providers)
    const availableProviders = stationNames;
    // Reset selected types when service changes and default to in-person if available
    useEffect(() => {
        if (selectedService) {
            const hasInPerson = availableMethodNames.includes("in-person");
            setSelectedTypes(hasInPerson ? ["in-person"] : []);
        } else {
            setSelectedTypes([]);
        }
    }, [selectedServiceName, availableMethodNames.join(',')]);

    const toggleSelection = (type: string) => {
        // Only allow single selection for appointment methods
        setSelectedTypes([type]);
    };

    // Check if appointment method is available for selected service
    const isMethodAvailable = (methodId: string) => {
        if (!selectedService) return false; // Disable all if no service selected
        return availableMethodNames.includes(methodId);
    };
    const meetingTypes = [
        { id: "in-person", label: "In-Person", icon: FiUser },
        { id: "audio", label: "Audio", icon: PiSpeakerHighThin },
        { id: "video", label: "Video", icon: MdOutlineVideocam },
    ];

    return (
        <div className="mx-5 mt-5">

            <div className="space-y-6">
                <div className="flex flex-col gap-2">
                    <Label htmlFor="services" className="text-[#18181B] text-sm font-normal">Select Service</Label>

                    <RefactorMultiSelect
                        value={selectedServices}
                        setValue={(value) => setSelectedServices(Array.isArray(value) ? [value[0]].filter(Boolean) : value ? [value] : [])}
                        placeholder={isLoadingServices ? "Loading services..." : "Select a service"}
                        label="Services"
                        options={services}
                    />
                </div>
                <div>
                    <div className="flex flex-col gap-2">
                        <Label htmlFor="providers" className="text-[#18181B] text-sm font-normal">Select Provider</Label>

                        <RefactorMultiSelect
                            value={selectedProviders}
                            setValue={setSelectedProviders}
                            placeholder={!selectedServiceName ? "Select a service first" : availableProviders.length === 0 ? "No providers available" : "Select providers"}
                            label="Providers"
                            options={availableProviders}
                        />
                    </div>
                    <p className="text-xs text-[#71717A]">
                        Select a provider you want to book. You can select multiple providers together view all available time slots.
                    </p>
                </div>
                <div className="flex flex-col gap-3">
                    <Label htmlFor="appointmentType" className="text-[#18181B] text-sm font-normal">Appointment Method</Label>
                    <div className="flex flex-wrap gap-2">
                        {/* {["in-person", "audio", "video"].map((type, index) => (
                            <button
                                type="button"
                                key={index}
                                className={`cursor-pointer border flex items-center justify-between py-3 px-4 rounded-xl font-regular opacity-100 gap-x-3 text-center ${type === "in-person" ? "border-[#005893] bg-[#0058931A] text-[#005893]" : "border-[#D4D4D8]"}`}
                            // onClick={() => form.setValue("appointmentType", type as "in-person" | "audio" | "video")}
                            >
                                {type === "in-person" && <FiUser color="#005893" className="text-xl" />}
                                {type === "audio" && <PiSpeakerHighThin color="#005893" className="text-xl" />}
                                {type === "video" && <MdOutlineVideocam color="#005893" className="text-xl" />}
                                <span>{type.charAt(0).toUpperCase() + type.slice(1)}</span>
                                <GoCheckCircleFill color="#005893" />
                            </button>
                        ))} */}
                        {meetingTypes.map((type) => (
                            <ToggleButton
                                key={type.id}
                                label={type.label}
                                icon={type.icon as any}
                                isSelected={selectedTypes.includes(type.id)}
                                onClick={() => toggleSelection(type.id)}
                                disabled={!isMethodAvailable(type.id)}
                                className="py-5"
                            />
                        ))}

                    </div>
                </div>
            </div>

            <div className="mt-16 mb-5">
                {/* <h1 className="text-base font-medium">Select Appointment Date & Time</h1> */}
                <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-4">
                        <Label htmlFor="appointmentDate" className="text-[#323539] text-sm font-normal border-b border-[#E4E4E7] pb-5">Select Appointment Date</Label>

                        <style>
                            {`
                                .picker-bk-appointment > div[data-slot] {
	                                width: 100% !important;
                                }
                                button.rdp-button_previous[aria-label="Go to the Previous Month"] {
                                    border: 1px solid #E4E4E7;
                                    cursor: pointer;
                                }
                                button.rdp-button_next[aria-label="Go to the Next Month"] {
                                    border: 1px solid #E4E4E7;
                                    cursor: pointer;
                                }
                            `}
                        </style>
                        <DatePicker
                            value={defaultDate}
                            variant="default"
                        />

                        <DatePicker
                            variant="inline"
                            value={defaultDate}
                            onChange={date => setDefaultDate(date as Date)}
                            className="w-full border rounded-lg inline-block overflow-hidden scrollbar-hide picker-bk-appointment"
                        />

                    </div>

                    <div className="space-y-4">
                        <Label htmlFor="appointmentDate" className="text-[#323539] text-sm font-normal border-b border-[#E4E4E7] pb-5">Recommended Providers</Label>

                        {isProviderSingle ? (
                            <div className="space-y-3">
                                <div className="border border-[#E4E4E7] py-2.5 px-3 rounded-[10px] flex items-start gap-x-3">
                                    <div className="size-12 bg-[#E4E4E7] text-[#A1A1AA] rounded-full grid place-content-center">
                                        AB
                                    </div>

                                    <div>
                                        <h1 className="font-medium text-base">Dr. Abraham Lincoln</h1>
                                        <div className="flex items-center text-sm gap-x-1.5 text-[#A1A1AA]">
                                            <p className="border-r border-[#A1A1AA] pr-1.5 h-[0.8rem] grid place-content-center">Service 1</p>
                                            <p className="border-r border-[#A1A1AA] pr-1.5 h-[0.8rem] grid place-content-center">Service 2</p>
                                            <p>Service 3</p>
                                        </div>
                                    </div>
                                </div>
                                <div className="grid grid-cols-2 gap-x-3 gap-y-4">
                                    {Array.from({ length: 6 }).map((_, index) => (
                                        <button
                                            key={index}
                                            tabIndex={0}
                                            className="cursor-pointer py-4 px-2 bg-[#FAFAFA] border border-[#E4E4E7] rounded-xl text-xs font-medium"
                                        >
                                            <span>09:15 AM - 9:30 AM</span>
                                        </button>
                                    ))}
                                </div>
                            </div>
                        ) : (
                            <div className="space-y-2 max-h-[24rem] overflow-scroll scrollbar-hide">
                                {Array.from({ length: 6 }).map((_, index) => (
                                    <div
                                        key={index}
                                        className="w-full border border-[#E4E4E7] py-2 px-3 rounded-[10px] flex items-start gap-x-1"
                                    >
                                        <div className="shrink-0 size-12 bg-[#E4E4E7] text-[#A1A1AA] rounded-full grid place-content-center">
                                            AB
                                        </div>

                                        <div className="space-y-1.5">
                                            <h1 className="font-medium text-base">Dr. Abraham Lincoln</h1>
                                            <div className="max-w-[17rem] flex gap-x-2 overflow-scroll scrollbar-hide">
                                                {Array.from({ length: 6 }).map((_, index) => (
                                                    <button
                                                        key={index}
                                                        tabIndex={0}
                                                        className="whitespace-nowrap cursor-pointer py-2 px-2 bg-[#FAFAFA] border border-[#E4E4E7] rounded-xl text-xs font-medium"
                                                    >
                                                        <span>09:15 AM - 9:30 AM</span>
                                                    </button>
                                                ))}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                </div>
            </div>

            <div className="flex items-center justify-end gap-x-3">
                <Button
                    type="button"
                    variant="outline"
                    onClick={back}
                    className="bg-[#F4F4F5] cursor-pointer"
                >
                    Back
                </Button>
                <Button
                    type="button"
                    className="cursor-pointer"
                    onClick={next}
                >
                    Next
                </Button>
            </div>
        </div >
    )
}