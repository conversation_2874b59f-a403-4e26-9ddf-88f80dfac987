import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Card, CardContent } from "@/components/ui/card";
import { GripHorizontal } from "lucide-react";
import type { FormTypes } from "@/pages/Forms/types";
import { FieldControl } from "./FieldControl";
import { FormFieldInput } from "./FormFieldInput";
import { cn } from "@/lib/utils";

export const DraggableFormField: React.FC<FormTypes.DraggableFieldProps> = ({
	sectionIndex,
	fieldIndex,
	field,
	control,
	watch,
	setValue,
	getValues,
	clearErrors,
	hasMultipleFields,
	isLastField,
	activeSection,
	onAddField,
	onAddSection,
	sectionId,
	form,
}: FormTypes.DraggableFieldProps) => {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
		isOver,
	} = useSortable({
		id: field.value.id,
		data: {
			type: "form_field",
			sectionIndex,
			fieldIndex,
		},
	});

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.4 : 1,
	};

	return (
		<div ref={setNodeRef} style={style} className="relative">
			<Card
				className={cn(
					"relative py-0 transition-colors duration-200",
					isOver && "border-primary bg-primary/5"
				)}
			>
				<div
					{...attributes}
					{...listeners}
					className="absolute top-0 right-[50%] left-[50%] cursor-move p-1"
				>
					<GripHorizontal className="text-gray-400" size={16} />
				</div>
				<CardContent className="space-y-6 p-4">
					<h1 className="text-base font-semibold">Add New Field</h1>
					<FormFieldInput
						nestIndex={sectionIndex}
						field={{
							...field,
							name: fieldIndex,
						}}
						control={control}
						watch={watch}
						setValue={setValue}
						getValues={getValues}
						clearErrors={clearErrors}
						form={form}
					/>
				</CardContent>
			</Card>

			{hasMultipleFields && isLastField && (
				<FieldControl
					sectionIndex={sectionIndex}
					onAddField={onAddField}
					onAddSection={onAddSection}
					isVisible={activeSection === sectionId}
					position="field"
				/>
			)}
		</div>
	);
};
