export type GetPatientsParams = {
    search?: string;
    name?: string;
    email?: string;
    phone_number?: string;
    emr_sync_status?: string;
    last_visit_start?: string;
    last_visit_end?: string;
}

export type GetPatientsResponse = {
    success: true,
    message: string,
    data: {
        id: number,
        external_id: string,
        first_name: string,
        last_name: string,
        full_name: string,
        email: string,
        phone_number: string,
        profile_picture_url: string,
        is_active: boolean,
        last_visit: string | null,
        emr_sync_status: string | null
    }[],
    meta: {
        pagination: {
            total: number,
            count: number,
            per_page: number,
            current_page: number,
            total_pages: number
        }
    }
}

export type CreatePatientPayload = {
    first_name: string,
    last_name: string,
    phone_number: string,
    email: string,
    profile_picture_url: string,
    attribute_values?: {
        allergies: string,
        preferred_language: string
    }
}

export type CreatePatientResponse = {
    success: true,
    message: string,
    data: {
        id: number,
        external_id: string,
        first_name: string,
        last_name: string,
        full_name: string,
        email: string,
        phone_number: string,
        profile_picture_url: string | null,
        is_active: boolean,
        last_visit: string | null,
        emr_sync_status: string | null,
        attribute_values: {
            allergies: string,
            preferred_language: string
        }[]
    }
}