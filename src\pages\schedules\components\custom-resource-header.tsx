import { type ResourceHeaderProps } from "react-big-calendar";

export const CustomResourceHeader = ({ label, resource }: ResourceHeaderProps & {
    resource: { resourceId: number; resourceTitle: string; color: string }
}) => (
    <div className="relative flex items-center py-1.5 px-2">
        <p style={{ backgroundColor: resource.color }} className="w-[6px] h-3 rounded-[1px] mr-1.5"></p>
        <span className="text-sm font-medium">{label}</span>
        <div className="z-[1000] absolute inset-x-0 -bottom-0 w-full h-[2px]" style={{ backgroundColor: resource.color }}></div>
    </div>
);