import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import {
    Sheet,
    Sheet<PERSON>ontent,
    She<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Sheet<PERSON>rigger,
} from "@/components/ui/sheet"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { zodResolver } from "@hookform/resolvers/zod"
import { LuSettings2 } from "react-icons/lu"
import { filterSchema, type FilterFormData, type AppointmentFilters } from "../schema/filter"
import { useForm } from "react-hook-form"
import { FiUser } from "react-icons/fi";
import { PiSpeakerHighThin } from "react-icons/pi";
import { MdOutlineVideocam } from "react-icons/md";
import { DatePicker, type DateRange } from "@/components/common/Datepicker/DatePicker"
import { RefactorMultiSelect } from "../components/custom-select"
import { ToggleButton } from "@/components/common/ToggleButton"
import { useState, useEffect, useMemo } from "react"
import { useLocations } from "@/features/locations/hooks/useLocations"
import { useLocationServices } from "@/features/locations/hooks/useServices"
import { useStations } from "@/features/locations/hooks/useStations"
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext"
import { usePermissionStore } from "@/stores/permissionStore"

interface FilterAppointmentProps {
    onApplyFilters?: (filters: AppointmentFilters) => void;
    onResetFilters?: () => void;
    onLocationChange?: (locationId: string | undefined) => void;
    onActiveFiltersChange?: (activeCount: number) => void;
}

export default function FilterAppointment({ onApplyFilters, onResetFilters, onLocationChange, onActiveFiltersChange }: FilterAppointmentProps) {
    const { organizationId } = useOrganizationContext();
    const currentLocation = usePermissionStore(state => state.currentLocation);
    const [isOpen, setIsOpen] = useState(false);

    const form = useForm<FilterFormData>({
        resolver: zodResolver(filterSchema),
        defaultValues: {
            location: "", // Will be auto-selected in useEffect
            providers: [],
            services: [],
            appointmentMethods: [],
            dateRange: "",
        }
    })

    // Separate state for date range since DatePicker needs DateRange object
    const [selectedDateRange, setSelectedDateRange] = useState<DateRange | undefined>(undefined);

    const selectedLocationId = form.watch("location");
    const selectedProviders = form.watch("providers");
    const selectedServices = form.watch("services");
    const selectedMethods = form.watch("appointmentMethods");

    // Count active filters (excluding location since it's always selected)
    const activeFilterCount = useMemo(() => {
        let count = 0;
        if (selectedProviders.length > 0) count++;
        if (selectedServices.length > 0) count++;
        if (selectedMethods.length > 0) count++;
        if (selectedDateRange && (selectedDateRange.from || selectedDateRange.to)) count++;
        return count;
    }, [selectedProviders, selectedServices, selectedMethods, selectedDateRange]);

    // Notify parent about active filter count changes
    useEffect(() => {
        onActiveFiltersChange?.(activeFilterCount);
    }, [activeFilterCount, onActiveFiltersChange]);

    // Fetch all locations for the organization
    const { data: locationsData, isLoading: isLoadingLocations } = useLocations(
        {},
        organizationId || undefined
    );

    // Fetch services for the selected location
    const { data: servicesData, isLoading: isLoadingServices } = useLocationServices({
        locationId: selectedLocationId,
        organizationId: organizationId || undefined,
        enabled: !!selectedLocationId
    });

    // Fetch providers/stations for the selected location
    const { data: stationsData, isLoading: isLoadingStations } = useStations({
        locationId: selectedLocationId,
        organizationId: organizationId || undefined,
        enabled: !!selectedLocationId
    });

    // Clear form data when organization changes
    useEffect(() => {
        // Reset the form when organization changes
        form.reset({
            location: "",
            providers: [],
            services: [],
            appointmentMethods: [],
            dateRange: "",
        });
        setSelectedDateRange(undefined);

        // Reset active filter count
        onActiveFiltersChange?.(0);
    }, [organizationId, form, onActiveFiltersChange]);

    // Cleanup old localStorage entries (one-time cleanup)
    useEffect(() => {
        // Remove old non-organization-specific localStorage key
        const oldKey = 'appointmentFilter_lastLocation';
        if (localStorage.getItem(oldKey)) {
            localStorage.removeItem(oldKey);
        }
    }, []); // Run only once on mount

    // Automatically select location when component mounts or organization changes
    useEffect(() => {
        const currentFormLocation = form.getValues("location");

        // Don't auto-select if there's no locations data or no organizationId
        if (!locationsData?.length || !organizationId) return;

        // Create organization-specific localStorage key
        const orgSpecificLocationKey = `appointmentFilter_lastLocation_${organizationId}`;

        // If there's already a location set and it exists in the current organization's locations, keep it
        if (currentFormLocation && locationsData.some(loc => loc.id.toString() === currentFormLocation)) {
            onLocationChange?.(currentFormLocation);
            return;
        }

        // Try to get last selected location from organization-specific localStorage
        const lastSelectedLocation = localStorage.getItem(orgSpecificLocationKey);

        // Check if last selected location still exists in available locations
        const lastLocationExists = lastSelectedLocation &&
            locationsData.some(loc => loc.id.toString() === lastSelectedLocation);

        let locationToSelect: string;

        if (lastLocationExists) {
            // Use last selected location if it still exists
            locationToSelect = lastSelectedLocation;
        } else if (currentLocation && locationsData.some(loc => loc.id.toString() === currentLocation.toString())) {
            // Use current location if it exists in available locations
            locationToSelect = currentLocation.toString();
        } else {
            // Fallback to first available location
            locationToSelect = locationsData[0].id.toString();
        }

        form.setValue("location", locationToSelect);
        onLocationChange?.(locationToSelect);

        // Store the selected location for this organization
        localStorage.setItem(orgSpecificLocationKey, locationToSelect);

    }, [locationsData, currentLocation, form, onLocationChange, organizationId]);

    // Clear providers and services when location changes
    useEffect(() => {
        form.setValue("providers", []);
        form.setValue("services", []);
    }, [selectedLocationId, form]);

    const meetingTypes = [
        { id: "1", label: "In-Person", icon: FiUser },
        { id: "2", label: "Audio", icon: PiSpeakerHighThin },
        { id: "3", label: "Video", icon: MdOutlineVideocam },
    ];

    // Transform location data for the select
    const locationOptions = useMemo(() => {
        return locationsData?.map(location => ({
            value: location.id.toString(),
            label: location.name
        })) || [];
    }, [locationsData]);

    // Transform stations data for multi-select (RefactorMultiSelect expects string[])
    const providerOptions = useMemo(() => {
        if (!stationsData?.data) return [];

        return stationsData.data.map(station => station.name);
    }, [stationsData]);

    // Transform services data for multi-select (RefactorMultiSelect expects string[])
    const serviceOptions = useMemo(() => {
        return servicesData?.data?.map(service => service.name) || [];
    }, [servicesData]);

    // Helper function to map station names back to station IDs
    const getStationIdsByNames = (stationNames: string[]): number[] => {
        if (!stationsData?.data) return [];

        const stationMap = new Map<string, number>();
        stationsData.data.forEach(station => {
            stationMap.set(station.name, station.id);
        });

        return stationNames.map(name => stationMap.get(name)).filter(id => id !== undefined) as number[];
    };

    // Helper function to map service names back to IDs
    const getServiceIdsByNames = (serviceNames: string[]): number[] => {
        if (!servicesData?.data) return [];

        const serviceMap = new Map<string, number>();
        servicesData.data.forEach(service => {
            serviceMap.set(service.name, service.id);
        });

        return serviceNames.map(name => serviceMap.get(name)).filter(id => id !== undefined) as number[];
    };

    const toggleMethodSelection = (methodId: string) => {
        const currentMethods = form.getValues("appointmentMethods");
        const isSelected = currentMethods.includes(methodId);

        if (isSelected) {
            form.setValue("appointmentMethods", currentMethods.filter(id => id !== methodId));
        } else {
            form.setValue("appointmentMethods", [...currentMethods, methodId]);
        }
    };

    const handleApply = () => {
        const formData = form.getValues();

        const filters: AppointmentFilters = {
            locationId: formData.location || undefined,
            providerIds: getStationIdsByNames(formData.providers),
            serviceIds: getServiceIdsByNames(formData.services),
            appointmentMethods: formData.appointmentMethods.map(id => parseInt(id)),
            dateFrom: selectedDateRange?.from ? selectedDateRange.from.toISOString().split('T')[0] : undefined,
            dateTo: selectedDateRange?.to ? selectedDateRange.to.toISOString().split('T')[0] : undefined,
        };

        onApplyFilters?.(filters);
        setIsOpen(false);
    };

    const handleReset = () => {
        // Clear stored location for this organization
        if (organizationId) {
            const orgSpecificLocationKey = `appointmentFilter_lastLocation_${organizationId}`;
            localStorage.removeItem(orgSpecificLocationKey);
        }

        // Reset to first available location or current location
        let resetLocationValue = "";
        if (locationsData?.length) {
            if (currentLocation && locationsData.some(loc => loc.id.toString() === currentLocation.toString())) {
                resetLocationValue = currentLocation.toString();
            } else {
                resetLocationValue = locationsData[0].id.toString();
            }
        }

        form.reset({
            location: resetLocationValue,
            providers: [],
            services: [],
            appointmentMethods: [],
            dateRange: "",
        });
        setSelectedDateRange(undefined);
        onLocationChange?.(resetLocationValue || undefined);

        // Store the reset location for this organization
        if (resetLocationValue && organizationId) {
            const orgSpecificLocationKey = `appointmentFilter_lastLocation_${organizationId}`;
            localStorage.setItem(orgSpecificLocationKey, resetLocationValue);
        }

        // Reset active filter count
        onActiveFiltersChange?.(0);

        onResetFilters?.();
    };

    const handleCancel = () => {
        setIsOpen(false);
    };

    return (
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
                <Button variant="outline" className="py-5 cursor-pointer">
                    <LuSettings2 />
                </Button>
            </SheetTrigger>
            <SheetContent className="z-[1003] py-5 px-2 sm:max-w-[610px]">
                <SheetHeader>
                    <SheetTitle className="text-2xl mb-1">Filter Appointments</SheetTitle>
                    <SheetDescription>Select options below to help filter your search</SheetDescription>
                </SheetHeader>
                <div className="px-4 space-y-7">

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="location" className="text-[#18181B] text-base">Location</Label>

                        <Select
                            value={form.watch("location") || ""}
                            onValueChange={(value) => {
                                const locationValue = typeof value === 'string' ? value : value[0] || "";
                                form.setValue("location", locationValue);
                                onLocationChange?.(locationValue || undefined);
                                // Store the selected location for this organization
                                if (locationValue && organizationId) {
                                    const orgSpecificLocationKey = `appointmentFilter_lastLocation_${organizationId}`;
                                    localStorage.setItem(orgSpecificLocationKey, locationValue);
                                }
                            }}
                            disabled={isLoadingLocations}
                        >
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder={isLoadingLocations ? "Loading locations..." : "Select Location"} />
                            </SelectTrigger>
                            <SelectContent>
                                {locationOptions.map((location) => (
                                    <SelectItem key={location.value} value={location.value}>
                                        {location.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="providers" className="text-[#18181B] text-base">Stations/Providers</Label>

                        <RefactorMultiSelect
                            value={selectedProviders}
                            setValue={(value) => form.setValue("providers", value as string[])}
                            placeholder={isLoadingStations ? "Loading stations..." : "Select Stations"}
                            label="Stations"
                            id="providers"
                            options={providerOptions}
                        />
                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="services" className="text-[#18181B] text-base">Services</Label>

                        <RefactorMultiSelect
                            value={selectedServices}
                            setValue={(value) => form.setValue("services", value as string[])}
                            placeholder={isLoadingServices ? "Loading services..." : "Select Services"}
                            label="Services"
                            id="services"
                            options={serviceOptions}
                        />
                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="appointmentMethods" className="text-[#18181B] text-base">Appointment Method</Label>

                        <div className="flex flex-wrap gap-2">
                            {meetingTypes.map((type) => (
                                <ToggleButton
                                    key={type.id}
                                    label={type.label}
                                    icon={type.icon as any}
                                    isSelected={selectedMethods.includes(type.id)}
                                    onClick={() => toggleMethodSelection(type.id)}
                                    className="py-5"
                                />
                            ))}
                        </div>

                    </div>

                    <div className="flex flex-col gap-2">
                        <Label htmlFor="dateRange" className="text-[#18181B] text-base">
                            Select a Date or Range
                        </Label>

                        <div className="w-full">
                            <style>
                                {`
                                div[data-radix-popper-content-wrapper] {
                                    z-index: 3000 !important;
                                }
                                `}
                            </style>
                            <DatePicker
                                variant="range"
                                value={selectedDateRange}
                                onChange={(date) => {
                                    if (date && typeof date === 'object' && 'from' in date) {
                                        // Handle DateRange
                                        const dateRange = date as DateRange;
                                        setSelectedDateRange(dateRange);

                                        // Store as string for form (optional - for API later)
                                        let dateString = "";
                                        if (dateRange.from && dateRange.to) {
                                            dateString = `${dateRange.from.toISOString().split('T')[0]} to ${dateRange.to.toISOString().split('T')[0]}`;
                                        } else if (dateRange.from) {
                                            dateString = dateRange.from.toISOString().split('T')[0];
                                        }
                                        form.setValue("dateRange", dateString);
                                    } else {
                                        // Clear selection
                                        setSelectedDateRange(undefined);
                                        form.setValue("dateRange", "");
                                    }
                                }}
                                placeholder="Pick a date range"
                                size="md"
                            />
                        </div>
                    </div>
                </div>
                <SheetFooter>
                    <div className="flex items-center justify-between">
                        <Button
                            variant={"ghost"}
                            className="cursor-pointer opacity-60 font-medium"
                            type="button"
                            onClick={handleReset}
                        >
                            Reset
                        </Button>
                        <div className="space-x-4">
                            <Button variant="outline" className="cursor-pointer py-5" onClick={handleCancel}>
                                Cancel
                            </Button>
                            <Button className="cursor-pointer py-5" onClick={handleApply}>
                                Apply
                            </Button>
                        </div>
                    </div>
                </SheetFooter>
            </SheetContent>
        </Sheet>
    )
}