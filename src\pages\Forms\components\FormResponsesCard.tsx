import { type FC } from "react";
import { Trash2, X, <PERSON>, ChevronRight, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import * as Types from "../types";
import { Badge } from "@/components/ui/badge";
import { getVariantColor } from "../utils/formHelpers";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

export interface FormResponsesCardProps {
	formResponse: Types.FormResponseTypes.FormResponseDataType;
	onEdit?: (
		formResponse: Types.FormResponseTypes.FormResponseDataType
	) => void;
	onView?: (
		formResponse: Types.FormResponseTypes.FormResponseDataType
	) => void;
	onViewMarkType?: (
		formResponse: Types.FormResponseTypes.FormResponseDataType
	) => void;
	onDelete?: (formId: string) => void;
	isSelected?: boolean;
	onSelectionChange?: (selected: boolean) => void;
	onApprove?: (
		formResponse: Types.FormResponseTypes.FormResponseDataType
	) => void;
	onReject?: (
		formResponse: Types.FormResponseTypes.FormResponseDataType
	) => void;
}

export const FormResponsesCard: FC<FormResponsesCardProps> = ({
	formResponse,
	onEdit,
	onView,
	onViewMarkType,
	onDelete,
	isSelected = false,
	onSelectionChange,
	onApprove,
	onReject,
}) => {
	return (
		<div
			className="hover:bg-foreground-muted flex cursor-pointer flex-wrap items-center justify-start border-b border-zinc-200 bg-white last:border-b-0"
			onClick={() => onView?.(formResponse!)}
		>
			{/* Checkbox Section */}
			<div
				className="flex h-16 items-center px-4"
				onClick={(e) => e.stopPropagation()}
			>
				<Checkbox
					checked={isSelected}
					onCheckedChange={onSelectionChange}
					className="cursor-pointer"
				/>
			</div>

			{/* Patient Name Section */}
			<div className="flex flex-2 items-center px-3">
				<h2 className="text-base leading-5 font-medium text-wrap">
					{formResponse?.client?.name || "-"}
				</h2>
			</div>

			{/* Form Name Section */}
			<div className="flex flex-2 items-center px-3">
				<h2 className="text-muted text-sm leading-5 text-wrap">
					{formResponse?.form?.name}
				</h2>
			</div>

			{/* Type Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className={cn(
						formResponse?.form?.type &&
							getVariantColor(
								formResponse?.form?.type || "",
								"border-none text-xs"
							),
						"border-none text-xs"
					)}
				>
					{formResponse?.form?.type || ""}
				</Badge>
			</div>

			{/* Service Section */}
			<div className="flex flex-1 items-center gap-1.5 px-3">
				<h2 className="text-muted text-sm leading-5 font-normal text-wrap">
					{formResponse?.service?.name || ""}
				</h2>
			</div>

			{/* Providers Section */}
			<div className="flex flex-1 items-center gap-1.5 px-3">
				<h2 className="text-muted text-sm leading-5 font-normal text-wrap">
					{formResponse?.station?.name || ""}
				</h2>
			</div>

			{/* Status Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className={getVariantColor(
						formResponse?.status,
						"border-transparent text-xs"
					)}
				>
					{formResponse?.status}
				</Badge>
			</div>

			{/* Created At Section */}
			<div className="flex flex-1 items-center px-3">
				<h3 className="text-muted text-sm leading-5 font-normal text-wrap">
					{format(new Date(formResponse?.created_at), "dd MMM yyyy")}
				</h3>
			</div>

			{/* Actions Section */}
			<div className="flex min-w-[140px] flex-1 items-center justify-end px-3">
				<div className="flex items-center gap-2.5">
					{formResponse?.status === "pending" ? (
						<>
							<Button
								variant="outline"
								size="icon"
								className="bg-foreground-subtle h-8 w-8 cursor-pointer rounded-md border-none border-zinc-200"
								onClick={(e) => {
									e.stopPropagation();
									onApprove?.(formResponse);
								}}
							>
								<Check className="size-4 text-[#289144]" />
							</Button>
							<Button
								variant="outline"
								size="icon"
								className="bg-foreground-subtle h-8 w-8 cursor-pointer rounded-md border-none border-zinc-200"
								onClick={(e) => {
									e.stopPropagation();
									onReject?.(formResponse);
								}}
							>
								<X className="size-4 text-[#DC2626]" />
							</Button>
						</>
					) : (
						<>
							<Button
								variant="outline"
								size="icon"
								className="bg-foreground-subtle h-8 w-8 cursor-pointer rounded-md border-none border-zinc-200"
								onClick={(e) => {
									e.stopPropagation();
									onDelete?.(formResponse?.id);
								}}
							>
								<Trash2 className="text-muted size-4" />
							</Button>
							<Button
								variant="outline"
								size="icon"
								className={cn(
									"bg-foreground-subtle relative h-8 w-8 cursor-pointer rounded-md border-none border-zinc-200",
									formResponse?.mark_type &&
										"bg-foreground-subtle h-8 w-8 cursor-pointer rounded-md border-none border-zinc-200"
								)}
								style={{
									border: formResponse?.mark_color
										? `1px solid ${formResponse?.mark_color}`
										: "none",
								}}
								onClick={(e) => {
									e.stopPropagation();
									onViewMarkType?.(formResponse!);
								}}
							>
								<Flag className="text-muted size-4" />
								<span
									className="bg-foreground-subtle absolute -top-1 right-0.5 size-2 rounded-full"
									style={{
										backgroundColor:
											formResponse?.mark_color || "",
									}}
								/>
							</Button>
						</>
					)}

					<Button
						variant="outline"
						size="icon"
						className="bg-foreground-subtle h-8 w-8 cursor-pointer rounded-md border-none border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onView?.(formResponse!);
						}}
					>
						<ChevronRight className="text-muted size-4" />
					</Button>
				</div>
			</div>
		</div>
	);
};
