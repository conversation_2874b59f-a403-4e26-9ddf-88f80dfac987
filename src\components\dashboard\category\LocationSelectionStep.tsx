import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Search, ChevronDown, ChevronUp } from "lucide-react";
import { useLocations } from "@/features/locations/hooks/useLocations";
import { useOrganizationContext } from "@/features/organizations/context";
import type { LocationSelectionData } from "./types";

export interface LocationData {
	id: string;
	name: string;
	stations: StationData[];
	expanded?: boolean;
}

export interface StationData {
	id: string;
	name: string;
}

interface LocationSelectionStepProps {
	locationData: LocationSelectionData;
	onLocationDataChange: (data: LocationSelectionData) => void;
}

export function LocationSelectionStep({
	locationData,
	onLocationDataChange,
}: LocationSelectionStepProps) {
	const { organizationId } = useOrganizationContext();
	const [locations, setLocations] = useState<LocationData[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 300); // 300ms delay

		return () => clearTimeout(timer);
	}, [searchTerm]);

	const filters = {
		isActive: true,
		...(debouncedSearchTerm.trim() && {
			search: debouncedSearchTerm.trim(),
		}),
	};
	const {
		data: locationsResponse,
		isLoading,
		error,
	} = useLocations(filters, organizationId || undefined);

	useEffect(() => {
		if (locationsResponse) {
			const transformedLocations: LocationData[] = locationsResponse.map(
				(location) => ({
					id: location.id,
					name: location.name,
					expanded: false,
					stations:
						location.stations?.map((station) => ({
							id: station.id,
							name: station.name,
						})) || [],
				})
			);

			console.log("Transformed Locations:", transformedLocations);
			setLocations(transformedLocations);
		}
	}, [locationsResponse, isLoading, error, organizationId]);

	const filteredLocations = locations.filter((location) => {
		const locationMatches = location.name
			.toLowerCase()
			.includes(searchTerm.toLowerCase());
		const stationMatches = location.stations.some((station) =>
			station.name.toLowerCase().includes(searchTerm.toLowerCase())
		);
		return locationMatches || stationMatches;
	});

	const highlightText = (text: string, searchTerm: string) => {
		if (!searchTerm.trim()) return text;

		const regex = new RegExp(
			`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
			"gi"
		);
		const parts = text.split(regex);

		return parts.map((part, index) =>
			regex.test(part) ? (
				<span
					key={index}
					className="bg-yellow-200 font-medium text-yellow-800"
				>
					{part}
				</span>
			) : (
				part
			)
		);
	};

	const isLocationAllStationsSelected = (locationId: string): boolean => {
		const locationIdNum = parseInt(locationId);
		const selection = locationData.locationSelections.find(
			(sel) => sel.location_id === locationIdNum
		);
		return selection?.all_stations || false;
	};
	const isLocationSelected = (locationId: string): boolean => {
		const locationIdNum = parseInt(locationId);
		const selection = locationData.locationSelections.find(
			(sel) => sel.location_id === locationIdNum
		);
		return !!selection;
	};

	const isStationSelected = (
		locationId: string,
		stationId: string
	): boolean => {
		const locationIdNum = parseInt(locationId);
		const stationIdNum = parseInt(stationId);
		const selection = locationData.locationSelections.find(
			(sel) => sel.location_id === locationIdNum
		);

		if (!selection) return false;
		if (selection.all_stations) return true;
		return selection.station_ids.includes(stationIdNum);
	};

	const handleLocationToggle = (locationId: string) => {
		const locationIdNum = parseInt(locationId);
		const existingSelection = locationData.locationSelections.find(
			(selection) => selection.location_id === locationIdNum
		);

		if (existingSelection) {
			const newLocationSelections =
				locationData.locationSelections.filter(
					(selection) => selection.location_id !== locationIdNum
				);

			onLocationDataChange({
				...locationData,
				selectedLocations: locationData.selectedLocations.filter(
					(id) => id !== locationId
				),
				selectedStations: {
					...locationData.selectedStations,
					[locationId]: [],
				},
				locationSelections: newLocationSelections,
			});
		} else {
			const newLocationSelections = [
				...locationData.locationSelections,
				{
					location_id: locationIdNum,
					all_stations: true,
					station_ids: [],
				},
			];

			onLocationDataChange({
				...locationData,
				selectedLocations: [
					...locationData.selectedLocations,
					locationId,
				],
				selectedStations: {
					...locationData.selectedStations,
					[locationId]: [],
				},
				locationSelections: newLocationSelections,
			});
		}
	};

	const handleStationToggle = (locationId: string, stationId: string) => {
		const locationIdNum = parseInt(locationId);
		const stationIdNum = parseInt(stationId);

		const existingSelection = locationData.locationSelections.find(
			(selection) => selection.location_id === locationIdNum
		);

		let newLocationSelections = [...locationData.locationSelections];
		let newSelectedStations = { ...locationData.selectedStations };

		if (existingSelection) {
			if (existingSelection.all_stations) {
				const location = locations.find((loc) => loc.id === locationId);
				const allStationIds =
					location?.stations.map((s) => parseInt(s.id)) || [];
				const newStationIds = allStationIds.filter(
					(id) => id !== stationIdNum
				);

				const existingIndex = newLocationSelections.findIndex(
					(sel) => sel.location_id === locationIdNum
				);

				if (newStationIds.length === 0) {
					newLocationSelections = newLocationSelections.filter(
						(sel) => sel.location_id !== locationIdNum
					);
					newSelectedStations[locationId] = [];
				} else {
					newLocationSelections[existingIndex] = {
						location_id: locationIdNum,
						all_stations: false,
						station_ids: newStationIds,
					};
					newSelectedStations[locationId] = newStationIds.map(String);
				}
			} else {
				const currentStationIds = existingSelection.station_ids;
				const isCurrentlySelected =
					currentStationIds.includes(stationIdNum);

				let newStationIds: number[];
				if (isCurrentlySelected) {
					newStationIds = currentStationIds.filter(
						(id) => id !== stationIdNum
					);
				} else {
					newStationIds = [...currentStationIds, stationIdNum];
				}

				const existingIndex = newLocationSelections.findIndex(
					(sel) => sel.location_id === locationIdNum
				);

				if (newStationIds.length === 0) {
					newLocationSelections = newLocationSelections.filter(
						(sel) => sel.location_id !== locationIdNum
					);
					newSelectedStations[locationId] = [];
				} else {
					newLocationSelections[existingIndex] = {
						location_id: locationIdNum,
						all_stations: false,
						station_ids: newStationIds,
					};
					newSelectedStations[locationId] = newStationIds.map(String);
				}
			}
		} else {
			newLocationSelections.push({
				location_id: locationIdNum,
				all_stations: false,
				station_ids: [stationIdNum],
			});
			newSelectedStations[locationId] = [stationId];
		}

		const newSelectedLocations = locationData.selectedLocations.filter(
			(id) => id !== locationId
		);
		const hasSelections = newLocationSelections.some(
			(sel) => sel.location_id === locationIdNum
		);
		if (hasSelections && !newSelectedLocations.includes(locationId)) {
			newSelectedLocations.push(locationId);
		}

		onLocationDataChange({
			...locationData,
			selectedLocations: newSelectedLocations,
			selectedStations: newSelectedStations,
			locationSelections: newLocationSelections,
		});
	};

	const toggleLocationExpansion = (locationId: string) => {
		setLocations(
			locations.map((loc) =>
				loc.id === locationId
					? { ...loc, expanded: !loc.expanded }
					: loc
			)
		);
	};

	return (
		<div className="flex flex-col gap-6">
			<div className="flex flex-col gap-4">
				<h3 className="text-sm font-semibold">Apply categories to</h3>
				<div className="flex items-start gap-2 rounded-md border border-gray-200 bg-white p-3">
					<div className="relative mt-0.5 flex h-3 w-3 items-center justify-center rounded-full border border-[#005893] bg-white">
						{locationData.applyToAll && (
							<div className="h-1.5 w-1.5 rounded-full bg-[#005893]" />
						)}
					</div>
					<button
						onClick={() =>
							onLocationDataChange({
								...locationData,
								applyToAll: true,
							})
						}
						className="flex flex-col gap-1.5 text-left"
					>
						<div className="text-xs font-medium">
							All Locations and Stations
						</div>
						<div className="text-xs text-gray-500">
							This will add this Client to all locations &
							stations in this location.
						</div>
					</button>
				</div>
				<div className="flex items-start gap-2 rounded-md border border-gray-200 bg-white p-3">
					<div className="relative mt-0.5 flex h-3 w-3 items-center justify-center rounded-full border border-[#005893] bg-white">
						{!locationData.applyToAll && (
							<div className="h-1.5 w-1.5 rounded-full bg-[#005893]" />
						)}
					</div>
					<button
						onClick={() =>
							onLocationDataChange({
								...locationData,
								applyToAll: false,
							})
						}
						className="flex flex-col gap-1.5 text-left"
					>
						<div className="text-xs font-medium">
							Selected Locations and Stations only
						</div>
						<div className="text-xs text-gray-500">
							This will add this Client to all locations &
							stations in this location.
						</div>
					</button>
				</div>
			</div>

			{!locationData.applyToAll && (
				<div className="flex w-[477px] flex-col gap-3 border-t border-gray-200 pt-3">
					<div className="relative flex h-9 items-center gap-2 overflow-hidden rounded-md border border-gray-200 bg-white p-3">
						<Search className="h-3 w-3 opacity-50" />
						<input
							type="text"
							placeholder="Search locations and stations..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="flex-1 border-none bg-transparent text-xs text-gray-900 placeholder-gray-500 outline-none"
						/>
						{searchTerm.trim() && filteredLocations.length > 0 && (
							<div className="text-xs text-gray-400">
								{filteredLocations.length} result
								{filteredLocations.length !== 1 ? "s" : ""}
							</div>
						)}
					</div>

					{isLoading && (
						<div className="flex items-center justify-center py-4">
							<div className="text-sm text-gray-500">
								Loading locations...
							</div>
						</div>
					)}

					{error ? (
						<div className="flex items-center justify-center py-4">
							<div className="text-sm text-red-500">
								Error loading locations:{" "}
								{error instanceof Error
									? error.message
									: "Unknown error"}
							</div>
						</div>
					) : null}

					<div className="flex flex-col gap-2">
						{filteredLocations.length === 0 &&
							!isLoading &&
							searchTerm.trim() && (
								<div className="flex items-center justify-center py-8">
									<div className="text-sm text-gray-500">
										No locations or stations found matching
										"{searchTerm}"
									</div>
								</div>
							)}

						{filteredLocations.map((location) => (
							<div key={location.id}>
								{!location.expanded && (
									<div className="flex h-14 items-center rounded-md border border-gray-200 bg-white">
										<div className="flex items-center gap-2.5 px-4">
											<Checkbox
												checked={isLocationSelected(
													location.id
												)}
												onCheckedChange={() =>
													handleLocationToggle(
														location.id
													)
												}
												className="h-4 w-4"
											/>
										</div>
										<div className="flex min-w-20 flex-1 items-center gap-3 px-3">
											<div className="text-sm font-medium">
												{highlightText(
													location.name,
													searchTerm
												)}
											</div>
										</div>
										<div className="flex min-w-16 items-center justify-end gap-1.5 px-3">
											<Button
												variant="ghost"
												size="sm"
												className="h-6 w-6 rounded-md p-2"
												onClick={() =>
													toggleLocationExpansion(
														location.id
													)
												}
											>
												<ChevronDown className="h-3 w-3 text-blue-600" />
											</Button>
										</div>
									</div>
								)}
								{location.expanded && (
									<div className="flex flex-col overflow-hidden rounded-md border border-gray-200">
										<div className="flex h-14 items-center border-t border-gray-200 bg-white">
											<div className="flex items-center gap-2.5 px-4">
												<Checkbox
													checked={isLocationSelected(
														location.id
													)}
													onCheckedChange={() =>
														handleLocationToggle(
															location.id
														)
													}
													className="h-4 w-4"
												/>
											</div>
											<div className="flex min-w-20 flex-1 items-center gap-3 px-3">
												<div className="text-sm font-medium">
													{highlightText(
														location.name,
														searchTerm
													)}
												</div>
											</div>
											<div className="flex min-w-16 items-center justify-end gap-1.5 px-3">
												<Button
													variant="ghost"
													size="sm"
													className="h-6 w-6 rounded-md p-2"
													onClick={() =>
														toggleLocationExpansion(
															location.id
														)
													}
												>
													<ChevronUp className="h-3 w-3" />
												</Button>
											</div>
										</div>
										<div className="flex flex-col bg-white pl-9">
											{location.stations.map(
												(station) => (
													<div
														key={station.id}
														className="flex h-9 items-center bg-white"
													>
														<div className="flex items-center gap-2.5 px-4">
															<Checkbox
																checked={isStationSelected(
																	location.id,
																	station.id
																)}
																onCheckedChange={() =>
																	handleStationToggle(
																		location.id,
																		station.id
																	)
																}
																className="h-4 w-4"
															/>
														</div>
														<div className="flex min-w-20 flex-1 items-center gap-3 px-3">
															<div className="text-sm font-normal">
																{highlightText(
																	station.name,
																	searchTerm
																)}
															</div>
														</div>
														<div className="w-16 min-w-16 px-3" />
													</div>
												)
											)}
										</div>
									</div>
								)}
							</div>
						))}
					</div>
				</div>
			)}
		</div>
	);
}
