import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@/lib/api/clients";
import type {
	BannerMessage,
	CreateBannerData,
	UpdateBannerData,
	BannerResponse,
	BannerListResponse,
} from "@/types/banner";

// API functions
const bannerApi = {
	// Get banners for a specific context (organization, location, or station)
	getBanners: async (params: {
		organizationId?: string;
		locationId?: string;
		stationId?: string;
	}): Promise<BannerListResponse> => {
		const { data } = await apiClient.get("/banners", { params });
		return data;
	},

	// Get a specific banner by ID
	getBanner: async (id: string): Promise<BannerResponse> => {
		const { data } = await apiClient.get(`/banners/${id}`);
		return data;
	},

	// Create a new banner
	createBanner: async (
		bannerData: CreateBannerData
	): Promise<BannerResponse> => {
		const { data } = await apiClient.post("/banners", bannerData);
		return data;
	},

	// Update an existing banner
	updateBanner: async (
		bannerData: UpdateBannerData
	): Promise<BannerResponse> => {
		const { id, ...updateData } = bannerData;
		const { data } = await apiClient.put(`/banners/${id}`, updateData);
		return data;
	},

	// Delete a banner
	deleteBanner: async (
		id: string
	): Promise<{ success: boolean; message: string }> => {
		const { data } = await apiClient.delete(`/banners/${id}`);
		return data;
	},
};

// Query keys
export const bannerKeys = {
	all: ["banners"] as const,
	lists: () => [...bannerKeys.all, "list"] as const,
	list: (params: {
		organizationId?: string;
		locationId?: string;
		stationId?: string;
	}) => [...bannerKeys.lists(), params] as const,
	details: () => [...bannerKeys.all, "detail"] as const,
	detail: (id: string) => [...bannerKeys.details(), id] as const,
};

// Custom hooks
export const useBanners = (params: {
	organizationId?: string;
	locationId?: string;
	stationId?: string;
	enabled?: boolean;
}) => {
	return useQuery({
		queryKey: bannerKeys.list(params),
		queryFn: () => bannerApi.getBanners(params),
		enabled: params.enabled !== false,
	});
};

export const useBanner = (id: string, enabled = true) => {
	return useQuery({
		queryKey: bannerKeys.detail(id),
		queryFn: () => bannerApi.getBanner(id),
		enabled: enabled && !!id,
	});
};

export const useCreateBanner = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: bannerApi.createBanner,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: bannerKeys.lists() });
		},
	});
};

export const useUpdateBanner = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: bannerApi.updateBanner,
		onSuccess: (data) => {
			queryClient.invalidateQueries({ queryKey: bannerKeys.lists() });
			queryClient.invalidateQueries({
				queryKey: bannerKeys.detail(data.data.id!),
			});
		},
	});
};

export const useDeleteBanner = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: bannerApi.deleteBanner,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: bannerKeys.lists() });
		},
	});
};
