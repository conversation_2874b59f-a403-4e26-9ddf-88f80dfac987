import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./styles/index.css";
import { RouterProvider } from "react-router";
import { QueryProvider } from "./providers/QueryProvider";
import { ErrorBoundary } from "./components/common/ErrorBoundary";
import { router } from "./routes";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { OrganizationProvider } from "@/features/organizations/context";
import { DeleteConfirmationProvider } from "@/contexts/DeleteConfirmationContext";
import { Toaster } from "./components/ui/sonner";

createRoot(document.getElementById("root")!).render(
	<StrictMode>
		<ErrorBoundary
			onError={(error, errorInfo) => {
				// You can integrate with error reporting services here
				// Example: logErrorToService(error, errorInfo);
				console.error("Application Error:", error, errorInfo);
			}}
		>
			<Toaster />
			<QueryProvider>
				<OrganizationProvider>
					<DeleteConfirmationProvider>
						<RouterProvider router={router} />
					</DeleteConfirmationProvider>
				</OrganizationProvider>
			</QueryProvider>
		</ErrorBoundary>
	</StrictMode>
);
