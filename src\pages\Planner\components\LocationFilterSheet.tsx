import React, { useState, useEffect } from 'react';
import { X, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
    SheetDescription,
    SheetFooter,
} from '@/components/ui/sheet';
import type { LocationsFilters } from '@/features/locations/types';

interface LocationFilterSheetProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onApplyFilters: (filters: Partial<LocationsFilters>) => void;
    currentFilters: Partial<LocationsFilters>;
}

export function LocationFilterSheet({
    open,
    onOpenChange,
    onApplyFilters,
    currentFilters,
}: LocationFilterSheetProps) {
    const [localFilters, setLocalFilters] = useState<Partial<LocationsFilters>>({});

    // Initialize local filters when sheet opens
    useEffect(() => {
        if (open) {
            setLocalFilters(currentFilters);
        }
    }, [open, currentFilters]);

    const handleStatusChange = (status: string, checked: boolean) => {
        setLocalFilters(prev => {
            const currentStatus = prev.isActive;
            if (status === 'active' && checked) {
                return { ...prev, isActive: true };
            } else if (status === 'inactive' && checked) {
                return { ...prev, isActive: false };
            } else {
                // If unchecking, remove the filter
                const { isActive, ...rest } = prev;
                return rest;
            }
        });
    };

    const handleCityChange = (city: string) => {
        setLocalFilters(prev => ({
            ...prev,
            city: city || undefined,
        }));
    };

    const handleStateChange = (state: string) => {
        setLocalFilters(prev => ({
            ...prev,
            state: state || undefined,
        }));
    };

    const handleSortChange = (value: string | string[]) => {
        const valueStr = Array.isArray(value) ? value[0] : value;
        const parts = valueStr.split('-');
        if (parts.length === 2) {
            const [sortBy, sortOrder] = parts;
            if (sortOrder === 'asc' || sortOrder === 'desc') {
                setLocalFilters(prev => ({
                    ...prev,
                    sortBy: sortBy as LocationsFilters['sortBy'],
                    sortOrder,
                }));
            }
        }
    };

    const handleApply = () => {
        onApplyFilters(localFilters);
    };

    const handleReset = () => {
        setLocalFilters({});
    };

    const getSortValue = () => {
        if (localFilters.sortBy && localFilters.sortOrder) {
            return `${localFilters.sortBy}-${localFilters.sortOrder}`;
        }
        return 'name-asc';
    };

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="w-[400px] sm:w-[500px]">
                <SheetHeader>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                            <Filter className="h-5 w-5" />
                            <SheetTitle>Filter Locations</SheetTitle>
                        </div>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onOpenChange(false)}
                            className="h-6 w-6 p-0"
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                    <SheetDescription>
                        Filter locations by status, location details, and sort preferences.
                    </SheetDescription>
                </SheetHeader>

                <div className="py-6 space-y-6">
                    {/* Status Filter */}
                    <div className="space-y-3">
                        <Label className="text-sm font-medium">Status</Label>
                        <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="active"
                                    checked={localFilters.isActive === true}
                                    onCheckedChange={(checked) => handleStatusChange('active', checked as boolean)}
                                />
                                <Label htmlFor="active" className="text-sm">Active Locations</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="inactive"
                                    checked={localFilters.isActive === false}
                                    onCheckedChange={(checked) => handleStatusChange('inactive', checked as boolean)}
                                />
                                <Label htmlFor="inactive" className="text-sm">Inactive Locations</Label>
                            </div>
                        </div>
                    </div>

                    {/* City Filter */}
                    <div className="space-y-2">
                        <Label htmlFor="city" className="text-sm font-medium">City</Label>
                        <Input
                            id="city"
                            placeholder="Enter city name..."
                            value={localFilters.city || ''}
                            onChange={(e) => handleCityChange(e.target.value)}
                        />
                    </div>

                    {/* State Filter */}
                    <div className="space-y-2">
                        <Label htmlFor="state" className="text-sm font-medium">State</Label>
                        <Input
                            id="state"
                            placeholder="Enter state name..."
                            value={localFilters.state || ''}
                            onChange={(e) => handleStateChange(e.target.value)}
                        />
                    </div>

                    {/* Sort Options */}
                    <div className="space-y-2">
                        <Label className="text-sm font-medium">Sort By</Label>
                        <Select value={getSortValue()} onValueChange={handleSortChange}>
                            <SelectTrigger>
                                <SelectValue placeholder="Select sort option" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                                <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                                <SelectItem value="city-asc">City (A-Z)</SelectItem>
                                <SelectItem value="city-desc">City (Z-A)</SelectItem>
                                <SelectItem value="createdAt-desc">Newest First</SelectItem>
                                <SelectItem value="createdAt-asc">Oldest First</SelectItem>
                                <SelectItem value="updatedAt-desc">Recently Updated</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Applied Filters Summary */}
                    {Object.keys(localFilters).length > 0 && (
                        <div className="space-y-2">
                            <Label className="text-sm font-medium">Active Filters</Label>
                            <div className="space-y-1">
                                {localFilters.isActive !== undefined && (
                                    <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                        Status: {localFilters.isActive ? 'Active' : 'Inactive'}
                                    </div>
                                )}
                                {localFilters.city && (
                                    <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                        City: {localFilters.city}
                                    </div>
                                )}
                                {localFilters.state && (
                                    <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                        State: {localFilters.state}
                                    </div>
                                )}
                                {localFilters.sortBy && (
                                    <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                        Sort: {localFilters.sortBy} ({localFilters.sortOrder})
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>

                <SheetFooter className="flex justify-between">
                    <Button variant="outline" onClick={handleReset}>
                        Reset Filters
                    </Button>
                    <div className="flex space-x-2">
                        <Button variant="outline" onClick={() => onOpenChange(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleApply}>
                            Apply Filters
                        </Button>
                    </div>
                </SheetFooter>
            </SheetContent>
        </Sheet>
    );
} 