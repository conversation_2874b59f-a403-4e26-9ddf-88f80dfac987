import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from "@/components/ui/card";
import { <PERSON>roll<PERSON>rea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>he<PERSON>, Search, Filter } from "lucide-react";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";

interface AuditLog {
	id: string;
	patient: string;
	login: string;
	ip: string;
	timestamp: string;
	event: string;
}

const mockAuditLogs: AuditLog[] = [
	{
		id: "1",
		patient: "<PERSON>",
		login: "<EMAIL>",
		ip: "************",
		timestamp: "Oct 25, 2024 — 04:30 PM",
		event: "Booked appointment with Dr. <PERSON><PERSON> (First Time Consultation)",
	},
	{
		id: "2",
		patient: "<PERSON>",
		login: "<EMAIL>",
		ip: "**********",
		timestamp: "Oct 23, 2024 — 11:15 AM",
		event: "Viewed billing history",
	},
	{
		id: "3",
		patient: "<PERSON>",
		login: "<EMAIL>",
		ip: "***********",
		timestamp: "Oct 20, 2024 — 07:40 PM",
		event: "Updated personal information",
	},
	{
		id: "4",
		patient: "<PERSON>urie",
		login: "<EMAIL>",
		ip: "************",
		timestamp: "Oct 25, 2024 — 04:32 PM",
		event: "Accepted privacy agreement",
	},
];

export function AuditLogsTabContent() {
	return (
		<div className="flex flex-col gap-6">
			{/* Header */}
			<div>
				<div className="flex w-full gap-4 items-center justify-between">
					{/* Title */}
					<h1 className="flex items-center gap-2 text-left text-2xl font-bold">
						Audit Logs
					</h1>

					{/* Search and Filter */}
					<div className="flex items-center gap-3">
						<div className="relative">
							<Search className="text-muted-foreground absolute top-2.5 left-3 h-4 w-4" />
							<Input
								type="text"
								placeholder="Search"
								className="h-9 w-64 rounded-md border border-gray-300 pl-9"
							/>
						</div>
						<Button
							variant="ghost"
							size="icon"
							className="h-9 w-9 border border-gray-300"
						>
							<Filter className="text-muted-foreground h-4 w-4" />
						</Button>
					</div>
				</div>

				<p className="text-muted-foreground mt-1 text-sm">
					Monitor actions and system events linked to user accounts.
				</p>
			</div>

			{/* List */}
			<div className="flex w-full flex-col items-start justify-start">
				{mockAuditLogs.map((log) => (
					<Card
						key={log.id}
						className="flex w-full rounded-none border-t border-transparent border-t-[#E4E4E7] pt-2 shadow-none"
					>
						<CardHeader className="flex w-full flex-col gap-2 px-0 pb-2">
							{/* Main event content */}
							<div className="text-sm text-gray-800 leading-relaxed">
								<span className="font-semibold">
									{log.patient} •{" "}
								</span>
								{log.event}
							</div>

							{/* Metadata row */}
							<div className="text-muted-foreground flex items-center justify-start gap-3 text-xs">
								<div>Login: {log.login}</div>
								<div>IP: {log.ip}</div>
								<div className="text-gray-500">
									{log.timestamp}
								</div>
							</div>
						</CardHeader>
					</Card>
				))}
			</div>
		</div>
	);
}
