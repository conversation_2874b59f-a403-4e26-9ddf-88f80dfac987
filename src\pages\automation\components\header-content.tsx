import { Button } from "@/components/ui/Button/Button";
import { Switch } from "@/components/ui/switch";
import { MdOutlineRedo, MdOutlineUndo } from "react-icons/md";
import { Play } from "lucide-react";
import { FiSave } from "react-icons/fi";
import type { EditorActions, EditorNodeType } from "../libs/type";
import type { Dispatch } from "react";

type HeaderContentProps = {
    onTest: () => void
    canUndo: boolean
    canRedo: boolean
    dispatch: Dispatch<EditorActions>;
    title: string;
    focusTitleInput: () => void;
    isActive: boolean;
    setIsActive: () => void;
}

export default function HeaderContent({ onTest, canUndo, canRedo, dispatch, title, focusTitleInput, isActive, setIsActive }: HeaderContentProps) {
    return (
        <div className="flex flex-1 items-center justify-between">
            <div className="flex items-center gap-x-6">
                <h1 className="text-foreground text-2xl font-semibold">
                    {title}
                </h1>
                <button onClick={focusTitleInput} className="cursor-pointer" type="button">
                    <svg width="19" height="19" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10.5 2.40967C10.5 2.16847 10.4039 1.93718 10.2334 1.7666C10.0629 1.59611 9.83192 1.50009 9.59083 1.5C9.34974 1.49997 9.11783 1.59523 8.94727 1.76562L8.94776 1.76611L8.21046 2.50342L9.49659 3.78955L10.2334 3.05273L10.294 2.98584C10.4265 2.82406 10.5 2.62065 10.5 2.40967ZM2.27393 8.44141C2.21594 8.49926 2.17295 8.57061 2.14893 8.64893L1.62452 10.3745L3.35303 9.85059L3.41016 9.82959C3.466 9.80483 3.51705 9.76993 3.56055 9.72656L8.78907 4.49658L7.50294 3.21045L2.27393 8.44141ZM11.5 2.40967C11.4999 2.88447 11.3231 3.34117 11.0059 3.69141L10.9404 3.75977L9.89649 4.80322C9.88319 4.82058 9.86941 4.83812 9.85352 4.854C9.83759 4.86992 9.82015 4.88365 9.80274 4.89697L4.26661 10.4346C4.09275 10.6079 3.87891 10.7362 3.64405 10.8076L1.46729 11.4678L1.46583 11.4683C1.33627 11.5072 1.19858 11.51 1.06739 11.4771C0.936166 11.4441 0.816009 11.3763 0.720222 11.2808C0.624407 11.1851 0.556178 11.0648 0.522956 10.9336C0.4898 10.8025 0.492554 10.6648 0.531257 10.5352L0.532234 10.5332L1.19239 8.35693L1.19288 8.35547L1.22266 8.26855C1.28721 8.09646 1.38295 7.93781 1.50489 7.80029L1.56788 7.73291L7.1045 2.19482C7.11738 2.17819 7.13122 2.16175 7.14649 2.14648C7.16181 2.13118 7.17814 2.1174 7.19483 2.10449L8.24024 1.05908L8.30909 0.993652C8.65939 0.676584 9.1161 0.49994 9.59083 0.5C10.0972 0.500094 10.5829 0.701463 10.9409 1.05957C11.2989 1.41769 11.5001 1.90331 11.5 2.40967Z" fill="#27272A" />
                    </svg>
                </button>
            </div>
            <div className="flex items-center gap-x-3">
                <Button
                    variant="outline"
                    className="flex items-center gap-x-3 border-none hover:bg-transparent cursor-pointer !px-2">
                    <Switch
                        onCheckedChange={setIsActive}
                        checked={isActive}
                    />
                    <p className="text-[#71717A] !font-light text-sm">Inactive</p>
                </Button>
                {/* <Button
                    variant="outline"
                    className="flex items-center gap-x-3 border-none hover:bg-transparent cursor-pointer !px-2">
                    <MdOutlineUndo color="#27272A" size={16} />
                    <p className="text-[#27272A] !font-medium text-sm">Undo</p>
                </Button> */}
                <Button
                    variant="outline"
                    className="flex items-center gap-x-3 border-none hover:bg-transparent cursor-pointer !px-2"
                    onClick={() => dispatch({ type: 'UNDO' })}
                    disabled={!canUndo}
                >
                    <MdOutlineUndo color="#27272A" size={16} />
                    <p className="text-[#27272A] !font-medium text-sm">Undo</p>
                </Button>
                <Button
                    variant="outline"
                    className="flex items-center gap-x-3 border-none hover:bg-transparent cursor-pointer !px-2"
                    onClick={() => dispatch({ type: 'REDO' })}
                    disabled={!canRedo}
                >
                    <MdOutlineRedo color="#27272A" size={16} />
                    <p className="text-[#27272A] !font-medium text-sm">Redo</p>
                </Button>
                <Button
                    variant="outline"
                    className="flex items-center gap-x-2.5 border-none hover:bg-transparent cursor-pointer !px-2"
                    onClick={onTest}
                >
                    <Play color="#27272A" size={16} />
                    <p className="text-[#27272A] !font-medium text-sm">Test Run</p>
                </Button>
                <Button
                    variant="ghost"
                    className="flex items-center bg-[#f5f5f5] gap-x-2.5 cursor-pointer">
                    <FiSave color="#27272A" size={16} />
                    <p className="text-[#27272A] !font-medium text-sm">Save as Draft</p>
                </Button>
                <Button
                    variant="outline"
                    className="flex items-center gap-x-2.5 cursor-pointer">
                    <p className="text-[#27272A] !font-medium text-sm">Publish</p>
                </Button>
            </div>
        </div>
    );
}