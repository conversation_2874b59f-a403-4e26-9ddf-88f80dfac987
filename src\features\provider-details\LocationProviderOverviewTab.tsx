import { useEffect, useState, type FC } from "react";
import { Search, Plus, MapPin, Settings2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";
import { EditStationInformationSheet } from "../locations/components/sheets";
import { useLocations } from "../locations/hooks/useLocations";
import { LocationProviderCard } from "./components/LocationProviderCard";
import type {
	LocationsFilters,
	LocationsResponse,
	Location,
	CreateStationRequest,
	CreateProviderStationRequest,
} from "../locations/types";
import { SendBookingLinkSheet } from "@/features/schedule";
import { StationInformationSheet } from "../locations/components/sheets/station-information";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AddStationSheet } from "../locations/components/sheets";
import { AppointmentsView } from "./components";
import { AnalyticsView } from "./components/AnalyticsView";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";

interface LocationProviderOverviewTabProps {
	className?: string;
}

export const LocationProviderOverviewTab: FC<
	LocationProviderOverviewTabProps
> = ({ className }) => {
	const { organizationId } = useOrganizationContext();
	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});
	const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showAddLocationForm, setShowAddLocationForm] = useState(false);
	const [showLocationDetails, setShowLocationDetails] = useState(false);
	const [
		showEditStationInformationSheet,
		setShowEditStationInformationSheet,
	] = useState(false);
	const [selectedLocation, setSelectedLocation] = useState<Location | null>(
		null
	);
	const [showSendBookingLinkSheet, setShowSendBookingLinkSheet] =
		useState(false);
	const [activeTab, setActiveTab] = useState("list");
	const { data: locationsData2, isLoading } = useLocations({
		page: currentPage,
		search: searchTerm,
		limit: 10,
	}, organizationId || undefined);

	// Debounced search filter
	useEffect(() => {
		const timer = setTimeout(() => {
			setFilters((prev) => ({
				...prev,
				search: searchTerm || undefined,
				page: 1,
			}));
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// Use real API data instead of mock data
	const locationsData: LocationsResponse = {
		data: locationsData2 || [],
		pagination: {
			page: currentPage,
			limit: 10,
			total: locationsData2?.length || 0,
			totalPages: Math.ceil((locationsData2?.length || 0) / 10),
		},
	};

	const handleFilterChange = (newFilters: Partial<LocationsFilters>) => {
		setFilters((prev) => ({ ...prev, ...newFilters, page: 1 }));
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked && locationsData?.data) {
			setSelectedLocations(
				locationsData.data.map((location) => location.id)
			);
		} else {
			setSelectedLocations([]);
		}
	};

	const handleLocationSelection = (locationId: string, selected: boolean) => {
		if (selected) {
			setSelectedLocations((prev) => [...prev, locationId]);
		} else {
			setSelectedLocations((prev) =>
				prev.filter((id) => id !== locationId)
			);
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleAddLocation = async (
		data: (CreateProviderStationRequest & { imageFile?: File }) | { name: string; description?: string }
	) => {
		console.log("Adding new location:", data);
		// Here you would typically call your API to create the location
		// For now, we'll just log the data and close the form
		setShowAddLocationForm(false);
	};

	const handleViewLocation = (location: Location) => {
		setSelectedLocation(location);
		setShowLocationDetails(true);
	};

	return (
		<div className={className}>
			{/* Header */}
			<div className="grid h-screen max-h-screen w-full max-w-screen grid-cols-[400px_1fr] gap-x-7">
				<div>
					<h1 className="px-3 py-4 text-2xl font-semibold">
						Appointments
					</h1>
					<AppointmentsView />
				</div>

				<div className="w-full overflow-x-auto">
					<h1 className="px-3 py-4 text-2xl font-semibold">
						Analytics
					</h1>
					<AnalyticsView />
				</div>
			</div>

			{/* Add Location Sheet */}
			<AddStationSheet
				open={showAddLocationForm}
				onOpenChange={setShowAddLocationForm}
				onSubmit={handleAddLocation}
			/>

			{/* Location Details Sheet */}
			<StationInformationSheet
				open={showLocationDetails}
				onClose={() => setShowLocationDetails(false)}
				onSendBookingLink={() => setShowSendBookingLinkSheet(true)}
			/>

			{/* Send Booking Link Sheet */}
			<SendBookingLinkSheet
				open={showSendBookingLinkSheet}
				onOpenChange={setShowSendBookingLinkSheet}
			/>

			{/* Edit Station Information Sheet */}
			<EditStationInformationSheet
				open={showEditStationInformationSheet}
				onClose={() => setShowEditStationInformationSheet(false)}
				stationId="station-123"
				stationData={selectedLocation as any}
				onSave={async () => { }}
				onProviderEdit={() => { }}
				onProviderDelete={() => { }}
				onAddProvider={() => { }}
			/>
		</div>
	);
};
