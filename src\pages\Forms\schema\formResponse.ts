import { z } from "zod";
import { FormTypes } from "../types";

// Helper function to calculate time offset in milliseconds
const getTimeOffset = (
	value: number,
	unit: "days" | "months" | "years"
): number => {
	switch (unit) {
		case "days":
			return value * 24 * 60 * 60 * 1000;
		case "months":
			return value * 30 * 24 * 60 * 60 * 1000; // Approximate
		case "years":
			return value * 365 * 24 * 60 * 60 * 1000; // Approximate
		default:
			return 0;
	}
};

const formResponseFieldSchema = z.object({
	field_id: z.string(),
	value: z.string(),
	value_type: z.string(),
});

const formResponseSchema = z.object({
	form_id: z.string(),
	session_id: z.string(),
	status: z.enum(["in_progress", "completed", "blocked"]),
	responses: z.array(formResponseFieldSchema),
	customer_id: z.string(),
});

// Dynamic Zod schema generator
export const createDynamicSchema = (formData: FormTypes.FormDataType) => {
	const schemaFields: Record<string, z.ZodTypeAny> = {};

	formData?.sections?.forEach((section) => {
		section?.fields?.forEach((field) => {
			let fieldSchema: z.ZodTypeAny = z.any(); // Initialize with default

			switch (field.type) {
				case "text":
				case "long_text":
					fieldSchema = z.string();
					if (field.required) {
						fieldSchema = z
							.string()
							.min(1, `${field.title} is required`);
					}
					break;

				case "numeric":
				case "number":
					fieldSchema = z.number();
					if (field.required) {
						fieldSchema = z
							.number()
							.min(1, `${field.title} is required`);
					}

					break;

				case "email":
					fieldSchema = z
						.string()
						.email("Please enter a valid email address");
					break;

				case "phone":
					fieldSchema = z
						.string()
						.regex(
							/^\+?[\d\s\-\(\)]+$/,
							"Please enter a valid phone number"
						);
					break;

				case "date":
					fieldSchema = z
						.string()
						.regex(
							/^\d{4}-\d{2}-\d{2}$/,
							"Please enter a valid date"
						);

					// Apply date validation if configured
					if (field.dateValidation) {
						const { dateValidation } = field;

						if (dateValidation.minDate) {
							fieldSchema = fieldSchema.refine(
								(date) =>
									new Date(date) >=
									new Date(dateValidation.minDate!),
								`Date must be on or after ${dateValidation.minDate}`
							);
						}

						if (dateValidation.maxDate) {
							fieldSchema = fieldSchema.refine(
								(date) =>
									new Date(date) <=
									new Date(dateValidation.maxDate!),
								`Date must be on or before ${dateValidation.maxDate}`
							);
						}

						if (dateValidation.minDateOffset) {
							const { value, unit, direction } =
								dateValidation.minDateOffset;
							const now = new Date();
							let minDate: Date;

							if (direction === "past") {
								minDate = new Date(
									now.getTime() - getTimeOffset(value, unit)
								);
							} else {
								minDate = new Date(
									now.getTime() + getTimeOffset(value, unit)
								);
							}

							fieldSchema = fieldSchema.refine(
								(date) => new Date(date) >= minDate,
								`Date must be at least ${value} ${unit} ${direction}`
							);
						}

						if (dateValidation.maxDateOffset) {
							const { value, unit, direction } =
								dateValidation.maxDateOffset;
							const now = new Date();
							let maxDate: Date;

							if (direction === "past") {
								maxDate = new Date(
									now.getTime() - getTimeOffset(value, unit)
								);
							} else {
								maxDate = new Date(
									now.getTime() + getTimeOffset(value, unit)
								);
							}

							fieldSchema = fieldSchema.refine(
								(date) => new Date(date) <= maxDate,
								`Date must be at most ${value} ${unit} ${direction}`
							);
						}
					}
					break;

				case "date_range":
					let startDateSchema: z.ZodTypeAny = z
						.string()
						.regex(
							/^\d{4}-\d{2}-\d{2}$/,
							"Please enter a valid start date"
						);

					let endDateSchema: z.ZodTypeAny = z
						.string()
						.regex(
							/^\d{4}-\d{2}-\d{2}$/,
							"Please enter a valid end date"
						);

					// Apply date validation if configured
					if (field.dateValidation) {
						const { dateValidation } = field;

						if (dateValidation.minDate) {
							startDateSchema = startDateSchema.refine(
								(date) =>
									new Date(date) >=
									new Date(dateValidation.minDate!),
								`Start date must be on or after ${dateValidation.minDate}`
							);
						}

						if (dateValidation.maxDate) {
							endDateSchema = endDateSchema.refine(
								(date) =>
									new Date(date) <=
									new Date(dateValidation.maxDate!),
								`End date must be on or before ${dateValidation.maxDate}`
							);
						}

						if (dateValidation.minDateOffset) {
							const { value, unit, direction } =
								dateValidation.minDateOffset;
							const now = new Date();
							let minDate: Date;

							if (direction === "past") {
								minDate = new Date(
									now.getTime() - getTimeOffset(value, unit)
								);
							} else {
								minDate = new Date(
									now.getTime() + getTimeOffset(value, unit)
								);
							}

							startDateSchema = startDateSchema.refine(
								(date) => new Date(date) >= minDate,
								`Start date must be at least ${value} ${unit} ${direction}`
							);
						}

						if (dateValidation.maxDateOffset) {
							const { value, unit, direction } =
								dateValidation.maxDateOffset;
							const now = new Date();
							let maxDate: Date;

							if (direction === "past") {
								maxDate = new Date(
									now.getTime() - getTimeOffset(value, unit)
								);
							} else {
								maxDate = new Date(
									now.getTime() + getTimeOffset(value, unit)
								);
							}

							endDateSchema = endDateSchema.refine(
								(date) => new Date(date) <= maxDate,
								`End date must be at most ${value} ${unit} ${direction}`
							);
						}
					}

					fieldSchema = z
						.object({
							start_date: startDateSchema,
							end_date: endDateSchema,
						})
						.refine(
							(data) =>
								new Date(data.start_date) <=
								new Date(data.end_date),
							{
								message: "End date must be after start date",
								path: ["end_date"],
							}
						);
					break;

				case "dropdown":
				case "radio":
				case "yes_no":
				case "boolean":
					if (field.options && field.options.length > 0) {
						const validOptions = field.options.map(
							(opt) => opt.value
						);
						fieldSchema = z.enum(
							validOptions as [string, ...string[]]
						);
					} else {
						fieldSchema = z.string();
					}
					break;

				case "checkbox":
					fieldSchema = z.array(z.string());
					break;

				// case "yes_no":
				// 	fieldSchema = z.boolean();
				// 	break;

				case "scale_1_10":
					fieldSchema = z.number().min(1).max(10);
					break;

				case "satisfaction_scale":
				case "agree_disagree":
				case "rating":
					if (field.options && field.options.length > 0) {
						const scaleOptions = field.options.map(
							(opt) => opt.value
						);
						fieldSchema = z.enum(
							scaleOptions as [string, ...string[]]
						);
					} else {
						fieldSchema = z.string();
					}
					break;

				case "attachment":
					fieldSchema = z.string();
					break;

				case "info_text":
				case "info_image":
					// Info fields don't need validation
					fieldSchema = z.any().optional();
					break;

				default:
					fieldSchema = z.any();
			}

			// Apply required validation
			if (field.required) {
				if (field.type === "checkbox") {
					fieldSchema = z
						.array(z.string())
						.min(1, `${field.title} is required`);
				} else if (fieldSchema instanceof z.ZodString) {
					fieldSchema = z
						.string()
						.min(1, `${field.title} is required`);
				}
			} else {
				fieldSchema = z.string().optional();
			}

			schemaFields[field.id] = fieldSchema;
		});
	});

	return z.object(schemaFields);
};

export { formResponseSchema };
