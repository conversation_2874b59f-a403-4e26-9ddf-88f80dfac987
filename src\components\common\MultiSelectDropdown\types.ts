import type { ReactNode } from "react";

export interface MultiSelectOption {
	value: string;
	label: string;
	disabled?: boolean;
}

export interface MultiSelectDropdownProps {
	value: string[];
	onValueChange: (value: string[]) => void;
	options: MultiSelectOption[] | string[];
	placeholder?: string;
	label?: string;
	id?: string;
	className?: string;
	disabled?: boolean;
	error?: boolean;
	selectTriggerProps?: {
		className?: string;
		style?: React.CSSProperties;
	};
	selectContentProps?: {
		className?: string;
	};
	selectGroupProps?: {
		className?: string;
	};
	selectItemProps?: {
		className?: string;
	};
	chipProps?: {
		className?: string;
		removeButtonClassName?: string;
	};
	emptyText?: string;
	searchable?: boolean;
	maxSelectedDisplay?: number;
	showSelectAll?: boolean;
	selectAllLabel?: string;
	clearAllLabel?: string;
	customRenderChip?: (value: string, onRemove: () => void) => ReactNode;
	customRenderOption?: (option: MultiSelectOption | string) => ReactNode;
}

export interface MultiSelectChipProps {
	value: string;
	onRemove: (value: string, event: React.MouseEvent) => void;
	className?: string;
	removeButtonClassName?: string;
	customRender?: (value: string, onRemove: () => void) => ReactNode;
}

export interface MultiSelectValueProps {
	value: string[];
	onValueChange: (value: string[]) => void;
	placeholder?: string;
	className?: string;
	chipProps?: {
		className?: string;
		removeButtonClassName?: string;
	};
	maxSelectedDisplay?: number;
	customRenderChip?: (value: string, onRemove: () => void) => ReactNode;
	options?: MultiSelectOption[];
}
