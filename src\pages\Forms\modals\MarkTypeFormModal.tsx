import { type FC, useState, useEffect } from "react";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	Di<PERSON>Footer,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { useMarkFormResponse } from "../store/slices/formResponseSlice";
import { useGetFormResponsesMarkType } from "../store/slices/formResponseSlice";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import useCustomToast from "@/components/CustomToast";
import { Loader2 } from "lucide-react";
import { FormResponseTypes } from "../types";

interface MarkTypeFormModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	response: FormResponseTypes.FormResponseDataType | null;
	onSuccess?: () => void;
	onClose?: () => void;
}

export const MarkTypeFormModal: FC<MarkTypeFormModalProps> = ({
	open,
	onOpenChange,
	response,
	onSuccess,
	onClose,
}) => {
	const { organizationId } = useOrganizationContext();
	const { mutate: markFormResponse, isPending } = useMarkFormResponse();
	const customToast = useCustomToast();
	const { data: markTypeData, isLoading: isLoadingMarkTypes } =
		useGetFormResponsesMarkType();

	const [markType, setMarkType] = useState<string>("");
	const [notes, setNotes] = useState<string>("");

	useEffect(() => {
		if (response) {
			setMarkType(response.mark_type || "");
			setNotes(response.marking_notes || "");
		}
	}, [response]);

	const handleSubmit = () => {
		if (!markType.trim()) {
			return;
		}

		markFormResponse(
			{
				response_uuid: response?.id || "",
				data: {
					mark_type: markType,
					notes: notes.trim(),
				},
			},
			{
				onSuccess: (data) => {
					// Reset form
					setMarkType("");
					setNotes("");
					onOpenChange(false);
					onSuccess?.();
					customToast(
						data.message || "Form response marked successfully 🎉",
						{
							id: "mark-form-response",
							type: "success",
						}
					);
				},
				onError: (error: any) => {
					customToast(
						error.response.data.message ||
							"Failed to mark form response 🤕",
						{
							id: "mark-form-response",
							type: "error",
						}
					);
				},
			}
		);
	};

	const handleCancel = () => {
		setMarkType("");
		setNotes("");
		onOpenChange(false);
		onClose?.();
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader className="gap-0">
					<DialogTitle className="text-lg font-semibold">
						Mark Form
					</DialogTitle>
					<DialogDescription className="text-muted text-sm">
						This is a dialog description.
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{/* Select Type */}
					<div className="space-y-2">
						<Label
							htmlFor="mark-type"
							className="text-right text-base leading-none font-medium"
						>
							Select Type
						</Label>
						<Select
							value={markType}
							onValueChange={(value: string | string[]) => {
								if (typeof value === "string") {
									setMarkType(value);
								}
							}}
							disabled={isLoadingMarkTypes}
						>
							<SelectTrigger className="w-full text-base">
								<SelectValue placeholder="Select from below" />
							</SelectTrigger>
							<SelectContent>
								{markTypeData?.data?.map((markTypeOption) => (
									<SelectItem
										key={markTypeOption.value}
										value={markTypeOption.value}
									>
										<div className="flex items-center gap-2">
											<div
												className="h-3 w-3 rounded-full"
												style={{
													backgroundColor:
														markTypeOption.color,
												}}
											/>
											{markTypeOption.label}
										</div>
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>

					{/* Add Notes */}
					<div className="space-y-2">
						<Label
							htmlFor="notes"
							className="text-right text-base leading-none font-medium"
						>
							Add Notes
						</Label>
						<Textarea
							id="notes"
							placeholder="Enter Notes Here..."
							value={notes}
							onChange={(e) => setNotes(e.target.value)}
							className="min-h-[144px] text-base"
						/>
					</div>
				</div>

				<DialogFooter>
					<div className="flex flex-col gap-3 sm:flex-row sm:justify-end sm:gap-2">
						<Button
							variant="outline"
							onClick={handleCancel}
							className="w-full min-w-[130px] cursor-pointer sm:w-auto"
						>
							Cancel
						</Button>
						<Button
							onClick={handleSubmit}
							disabled={!markType.trim() || isPending}
							className="w-full min-w-[130px] cursor-pointer sm:w-auto"
						>
							{isPending ? (
								<Loader2 className="h-4 w-4 animate-spin" />
							) : (
								"Add"
							)}
						</Button>
					</div>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};
