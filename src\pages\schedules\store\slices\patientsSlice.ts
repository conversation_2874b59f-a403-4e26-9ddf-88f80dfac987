import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { CreatePatient, GetPatients } from "../../http";
import * as Types from "../../types";
import { useOrganizationContext } from "@/features/organizations/context";

export function useGetPatients(
    params: Types.ClientsTypes.GetPatientsParams
) {
    const { organizationId } = useOrganizationContext();
    return useQuery({
        queryKey: ["patients", organizationId, params],
        queryFn: () => GetPatients(params, organizationId || undefined),
        enabled: !!organizationId, // Only run query when we have an organizationId
    })
}

export function useAddPatient() {
    const { organizationId } = useOrganizationContext();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (
            data: Types.ClientsTypes.CreatePatientPayload
        ) => CreatePatient(data, organizationId || undefined),
        onSuccess: () => {
            // Invalidate all patients queries for the current organization
            queryClient.invalidateQueries({
                queryKey: ["patients", organizationId],
            });
            // Also invalidate the main clients queries
            queryClient.invalidateQueries({
                queryKey: ["clients"],
            });
        },
    })
}