import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { FileImage } from "lucide-react";
import { useState } from "react";
import { Uploader } from "./Uploader";
import type { UploadedFile } from "./types";

const meta: Meta<typeof Uploader> = {
	title: "Common/Uploader",
	component: Uploader,
	parameters: {
		docs: {
			description: {
				component:
					"A versatile file uploader component with drag and drop support, file management, and multiple states. Supports single and multiple file uploads with customizable validation.",
			},
		},
	},
	argTypes: {
		multiple: {
			control: "boolean",
			description: "Allow multiple file uploads",
		},
		size: {
			control: "select",
			options: ["sm", "md", "lg"],
			description: "Size variant of the uploader",
		},
		variant: {
			control: "select",
			options: ["default", "compact", "bordered"],
			description: "Visual variant of the uploader",
		},
		accept: {
			control: "text",
			description: "Accepted file types",
		},
		maxFileSize: {
			control: "number",
			description: "Maximum file size in bytes",
		},
		maxFiles: {
			control: "number",
			description: "Maximum number of files",
		},
		disabled: {
			control: "boolean",
			description: "Disable the uploader",
		},
		isLoading: {
			control: "boolean",
			description: "Show loading state",
		},
		onFilesChange: {
			action: "files-changed",
			description: "Callback when files are selected",
		},
		onFileRemove: {
			action: "file-removed",
			description: "Callback when file is removed",
		},
		// onFileEdit: {
		// 	action: "file-edited",
		// 	description: "Callback when file is edited",
		// },
		onError: {
			action: "error",
			description: "Callback for upload errors",
		},
	},
	tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample uploaded files for stories
const sampleFiles: UploadedFile[] = [
	{
		id: "1",
		name: "company-logo.svg",
		size: 2048,
		type: "image/svg+xml",
	},
	{
		id: "2",
		name: "hero-image.jpg",
		size: 1536000,
		type: "image/jpeg",
	},
];

export const Default: Story = {
	args: {
		uploadText: "Click or drag file here to upload file",
		descriptionText:
			"Recommended file type: .svg, .png, .jpg (Max of 10 mb)",
		accept: ".svg,.jpg,.jpeg,.png",
		maxFileSize: 10 * 1024 * 1024,
	},
};

export const EmptyState: Story = {
	args: {
		files: [],
		uploadText: "Click or drag file here to upload file",
		descriptionText:
			"Recommended file type: .svg, .png, .jpg (Max of 10 mb)",
	},
};

export const FilledState: Story = {
	args: {
		files: sampleFiles,
		uploadText: "Click or drag file here to upload file",
		descriptionText:
			"Recommended file type: .svg, .png, .jpg (Max of 10 mb)",
	},
};

export const LoadingState: Story = {
	args: {
		isLoading: true,
		uploadText: "Click or drag file here to upload file",
		descriptionText:
			"Recommended file type: .svg, .png, .jpg (Max of 10 mb)",
	},
};

export const MultipleFiles: Story = {
	args: {
		multiple: true,
		maxFiles: 5,
		files: sampleFiles,
		uploadText: "Click or drag files here to upload",
		descriptionText:
			"Upload up to 5 files. Recommended file type: .svg, .png, .jpg (Max of 10 mb each)",
	},
};

export const Sizes: Story = {
	render: () => (
		<div className="space-y-8">
			<div className="space-y-2">
				<h3 className="text-sm font-medium">Small</h3>
				<Uploader size="sm" />
			</div>
			<div className="space-y-2">
				<h3 className="text-sm font-medium">Medium (Default)</h3>
				<Uploader size="md" />
			</div>
			<div className="space-y-2">
				<h3 className="text-sm font-medium">Large</h3>
				<Uploader size="lg" />
			</div>
		</div>
	),
};

export const Variants: Story = {
	render: () => (
		<div className="space-y-8">
			<div className="space-y-2">
				<h3 className="text-sm font-medium">Default (Dashed)</h3>
				<Uploader variant="default" />
			</div>
			<div className="space-y-2">
				<h3 className="text-sm font-medium">Compact</h3>
				<Uploader variant="compact" />
			</div>
			<div className="space-y-2">
				<h3 className="text-sm font-medium">Bordered</h3>
				<Uploader variant="bordered" />
			</div>
		</div>
	),
};

export const CustomIcon: Story = {
	args: {
		uploadIcon: <FileImage className="text-primary h-6 w-6" />,
		uploadText: "Upload images",
		descriptionText: "PNG, JPG, SVG up to 10MB",
		accept: ".png,.jpg,.jpeg,.svg",
	},
};

export const Disabled: Story = {
	args: {
		disabled: true,
		uploadText: "Upload disabled",
		descriptionText: "File upload is currently disabled",
	},
};

export const Interactive: Story = {
	render: () => {
		const [files, setFiles] = useState<UploadedFile[]>([]);
		const [isLoading, setIsLoading] = useState(false);

		const handleFilesChange = (newFiles: File[]) => {
			setIsLoading(true);

			// Simulate upload delay
			setTimeout(() => {
				const uploadedFiles: UploadedFile[] = newFiles.map(
					(file, index) => ({
						id: `${Date.now()}-${index}`,
						name: file.name,
						size: file.size,
						type: file.type,
					})
				);

				setFiles((prev) => [...prev, ...uploadedFiles]);
				setIsLoading(false);
			}, 2000);
		};

		const handleFileRemove = (fileId: string) => {
			setFiles((prev) => prev.filter((f) => f.id !== fileId));
		};

		// const handleFileEdit = (fileId: string, newName: string) => {
		// 	setFiles((prev) =>
		// 		prev.map((f) => (f.id === fileId ? { ...f, name: newName } : f))
		// 	);
		// };

		return (
			<Uploader
				files={files}
				isLoading={isLoading}
				multiple={true}
				maxFiles={3}
				onFilesChange={handleFilesChange}
				onFileRemove={handleFileRemove}
				// onFileEdit={handleFileEdit}
				onError={(error) => alert(`Error: ${error}`)}
			/>
		);
	},
	parameters: {
		docs: {
			description: {
				story: "Interactive example with file management, loading states, and error handling.",
			},
		},
	},
};

export const WithValidation: Story = {
	args: {
		accept: ".pdf,.doc,.docx",
		maxFileSize: 5 * 1024 * 1024, // 5MB
		uploadText: "Upload documents",
		descriptionText: "PDF, DOC, DOCX files only (Max 5MB)",
		onError: (error: string) => alert(`Upload Error: ${error}`),
	},
};

export const AllStates: Story = {
	render: () => (
		<div className="grid grid-cols-1 gap-6 md:grid-cols-3">
			<div className="space-y-2">
				<h3 className="text-sm font-medium">Empty</h3>
				<Uploader
					uploadText="Click or drag file here to upload file"
					descriptionText="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
				/>
			</div>
			<div className="space-y-2">
				<h3 className="text-sm font-medium">Filled</h3>
				<Uploader
					files={[sampleFiles[0]]}
					uploadText="Click or drag file here to upload file"
					descriptionText="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
				/>
			</div>
			<div className="space-y-2">
				<h3 className="text-sm font-medium">Loading</h3>
				<Uploader
					isLoading={true}
					uploadText="Click or drag file here to upload file"
					descriptionText="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
				/>
			</div>
		</div>
	),
	parameters: {
		docs: {
			description: {
				story: "Shows all three main states of the uploader component as seen in the Figma design.",
			},
		},
	},
};
