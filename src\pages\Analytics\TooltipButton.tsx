import React, { useCallback, useState, memo } from "react";
import { Info } from "lucide-react";
import InfoTooltip from "./InfoTooltip";

const TooltipButton = memo(({ tooltipContent }: any) => {
	const [isVisible, setIsVisible] = useState(false);

	const handleMouseEnter = useCallback(() => {
		setIsVisible(true);
	}, []);

	const handleMouseLeave = useCallback(() => {
		setIsVisible(false);
	}, []);

	return (
		<button
			className="absolute right-[-25px] top-1"
			onMouseEnter={handleMouseEnter}
			onMouseLeave={handleMouseLeave}
		>
			<Info className="h-4 w-4 rounded bg-gray-100 p-1 text-gray-700" />
			<InfoTooltip trigger={<></>}>{tooltipContent}</InfoTooltip>
		</button>
	);
});

TooltipButton.displayName = "TooltipButton";

export default TooltipButton;
