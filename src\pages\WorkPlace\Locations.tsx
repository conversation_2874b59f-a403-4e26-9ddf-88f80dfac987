import { useEffect, useState, type FC, useRef } from "react";
import {
	LocationOrganizationCard,
	LocationsList,
	OrganizationDetailsSheet,
	type Organization,
} from "@/features/locations";
import { useUIStore } from "@/stores/uiStore";
import { useOrganizationContext } from "@/features/organizations/context";
import { Button } from "@/components/ui/button";
import { RefreshCcw, Loader2 } from "lucide-react";

const Locations: FC = () => {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore((state) => state.setPageHeaderContent);
	const { organizationId, refetch: refetchOrganization } = useOrganizationContext();
	const [openOrganizationDetailsSheet, setOpenOrganizationDetailsSheet] =
		useState(false);
	const [isRefreshing, setIsRefreshing] = useState(false);
	const currentRefetchRef = useRef<(() => void) | null>(null);

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Workplace",
				href: "/workplace",
			},
			{
				label: "Locations",
				isCurrentPage: true,
			},
		]);

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	// Set page header with refresh functionality
	useEffect(() => {
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				<h1 className="text-foreground text-2xl font-bold">
					Locations
				</h1>
				<div className="flex items-center space-x-3">
					<Button
						variant="outline"
						size="icon"
						className="h-9 w-9"
						disabled={isRefreshing}
						onClick={async () => {
							setIsRefreshing(true);
							try {
								// Trigger refetch of current tab data, or organization data if tab doesn't support refresh
								if (currentRefetchRef.current) {
									await currentRefetchRef.current();
								} else {
									// Fallback to refreshing organization data for non-refreshable tabs
									await refetchOrganization();
								}
							} finally {
								setIsRefreshing(false);
							}
						}}
					>
						{isRefreshing ? (
							<Loader2 className="h-4 w-4 animate-spin" />
						) : (
							<RefreshCcw className="h-4 w-4" />
						)}
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [setPageHeaderContent, refetchOrganization]);

	const handleView = (org: Organization) => {
		console.log("View:", org.name);
		setOpenOrganizationDetailsSheet(true);
	};
	const organization = useOrganizationContext();

	return (
		<div className="flex flex-col gap-4 py-6">
			{organization.organization && (
				<LocationOrganizationCard
					organization={{
						...organization.organization,
						id: organization.organization.id?.toString() || "0",
					}}
					onView={handleView}
					onEdit={(org) => console.log("Edit:", org.name)}
					onDelete={(org) => console.log("Delete:", org.name)}
				/>
			)}
			<LocationsList
				onRefetchReady={(refetchFn) => {
					currentRefetchRef.current = refetchFn;
				}}
			/>
			<OrganizationDetailsSheet
				open={openOrganizationDetailsSheet}
				onClose={() => setOpenOrganizationDetailsSheet(false)}
			/>
		</div>
	);
};

export default Locations;
