import type { FC } from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";
import { useNavigate, useSearchParams } from "react-router";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import type {
	TabsProps,
	TabsListProps,
	TabsTriggerProps,
	TabsContentProps,
} from "./types";

export interface TabItem {
	value: string;
	label: string;
	count?: number | undefined;
	disabled?: boolean;
}

const Tabs: FC<TabsProps> = ({
	className,
	triggerClassName,
	listClassName,
	items,
	children,
	useRouting = false,
	searchParamKey = "tab",
	defaultTab,
	value: controlledValue,
	onValueChange: controlledOnValueChange,
	defaultValue,
	...props
}) => {
	const navigate = useNavigate();
	const [searchParams] = useSearchParams();

	// Determine the current value based on routing or controlled state
	const getCurrentValue = () => {
		if (useRouting) {
			return (
				searchParams.get(searchParamKey) ||
				defaultTab ||
				defaultValue ||
				items[0]?.value
			);
		}
		return controlledValue || defaultValue || defaultTab || items[0]?.value;
	};

	const currentValue = getCurrentValue();

	const handleValueChange = (value: string) => {
		if (useRouting) {
			const newSearchParams = new URLSearchParams(searchParams);
			newSearchParams.set(searchParamKey, value);
			navigate(`?${newSearchParams.toString()}`, { replace: true });
		}

		// Still call the controlled onValueChange if provided
		controlledOnValueChange?.(value);
	};

	return (
		<TabsPrimitive.Root
			className={cn("w-full", className)}
			value={currentValue}
			onValueChange={handleValueChange}
			{...props}
		>
			<TabsList className={listClassName}>
				{items.map((item) => (
					<TabsTrigger
						key={item.value}
						value={item.value}
						count={item.count || undefined}
						disabled={item.disabled}
						className={triggerClassName}
					>
						{item.label}
					</TabsTrigger>
				))}
			</TabsList>
			{children}
		</TabsPrimitive.Root>
	);
};

const TabsList: FC<TabsListProps> = ({ className, ...props }) => {
	return (
		<TabsPrimitive.List
			className={cn(
				"inline-flex h-auto w-full items-center justify-start overflow-x-auto rounded-lg bg-zinc-100 p-1",
				className
			)}
			{...props}
		/>
	);
};

const TabsTrigger: FC<TabsTriggerProps> = ({
	className,
	count,
	children,
	...props
}) => {
	return (
		<TabsPrimitive.Trigger
			className={cn(
				"inline-flex cursor-pointer items-center justify-center gap-2 rounded-md px-3 py-1 text-sm font-medium text-zinc-500 transition-all",
				"hover:text-zinc-700 focus-visible:ring-2 focus-visible:ring-zinc-400 focus-visible:ring-offset-2 focus-visible:outline-none",
				"disabled:pointer-events-none disabled:opacity-50",
				"data-[state=active]:bg-white data-[state=active]:font-semibold data-[state=active]:text-zinc-800 data-[state=active]:shadow-sm",
				"flex-1", // Allow tabs to grow and shrink
				className
			)}
			{...props}
		>
			<span className="whitespace-nowrap">{children}</span>
			{count !== undefined && (
				<Badge
					variant="secondary"
					className="h-5 bg-zinc-100 px-2 text-xs font-medium text-zinc-800"
				>
					{count}
				</Badge>
			)}
		</TabsPrimitive.Trigger>
	);
};

const TabsContent: FC<TabsContentProps> = ({ className, ...props }) => {
	return (
		<TabsPrimitive.Content
			className={cn(
				"ring-offset-background focus-visible:ring-ring mt-4 focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none",
				className
			)}
			{...props}
		/>
	);
};

export { Tabs, TabsList, TabsTrigger, TabsContent };
