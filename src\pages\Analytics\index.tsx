// AnalyticsDashboard.tsx
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	DialogTrigger,
} from "@/components/ui/dialog";
// import Header from "./Header";
import AllLocations from "./AllLocations";
import CategoryFilter from "./CategoryFilter";
import ServiceFilter from "./ServiceFilter";
import StatusFilter from "./StatusFilter";
// Remove these API imports since we're using dummy data
// import {
// 	GetAnalyticsMutationSlice,
// 	GetAnalyticsLogoBlobSlice,
// } from "@/src/store/slices/analytics/getAnalyticsSlice";
// import useAnalyticsStore from "@/src/store/useAnalyticsStore";
import { Button } from "@/components/ui/button";
import {
	Select,
	SelectContent,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import useUserStore from "@/stores/useUserStore";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
// import * as htmlToImage from "html-to-image";
// import html2pdf from "html2pdf.js";
import { Download } from "lucide-react";
import React, {
	useCallback,
	useEffect,
	useLayoutEffect,
	useRef,
	useState,
} from "react";
import { FormProvider, useForm } from "react-hook-form";
import { LuRotateCcw, LuX } from "react-icons/lu";
import { CalendarDateRangePicker } from "./CalendarDateRangePicker";
import MetricCard from "./MetricCard";
import RatingCard from "./RatingCard";
import StatisticsCharts from "./StatisticsCharts";
import { analyticsInitialMetrics } from "@/utils/constants";
import { DummyAnalyticsService } from './dummyAnalyticsService';
import { dummyAnalyticsData } from './data';

// Define proper types
interface AnalyticsQuery {
	start_date: string | null;
	end_date: string | null;
	service_ids: number[];
	statuses: string[];
	locations: any[];
	categories: string[];
}

interface Location {
	id: number;
	name: string;
	stations: Array<{ id: number; name: string }>;
}

// Create a simple analytics store for local state management
const useAnalyticsStore = () => {
	const [analyticsQuery, setAnalyticsQuery] = useState<AnalyticsQuery>({
		start_date: null,
		end_date: null,
		service_ids: [],
		statuses: [],
		locations: [],
		categories: []
	});

	return {
		analyticsQuery,
		setAnalyticsQuery: (query: Partial<AnalyticsQuery>) =>
			setAnalyticsQuery(prev => ({ ...prev, ...query }))
	};
};

const AnalyticsDashboard = () => {
	const contentRef = useRef<HTMLDivElement>(null);
	// Remove queryClient since we're not using API calls
	// const queryClient = useQueryClient();
	// const { user, selectedOrganisation } = useUserStore((s) => ({
	// 	user: s.user,
	// 	selectedOrganisation: s.selectedOrganisation,
	// }));

	// Replace API calls with dummy data
	const [analyticsData, setAnalyticsData] = useState(dummyAnalyticsData);
	const [isLoading, setIsLoading] = useState(false);

	const { analyticsQuery, setAnalyticsQuery } = useAnalyticsStore();

	const [selectedUserOrganisation, setSelectedUserOrganisation] =
		useState<any>({});
	const [metrics, setMetrics] = useState(analyticsInitialMetrics);
	const [selectedAllLocations, setSelectedAllLocations] = useState<Location[]>([]);
	const [stations, setStations] = useState<string>("");
	const [isReload, setIsReload] = useState(false);
	const [isExporting, setIsExporting] = useState(false);
	const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

	const metricsRefs = useRef<HTMLDivElement[]>([]);
	const statisticsRefs = useRef<HTMLDivElement[]>([]);

	const summaryRatingRef = useRef<HTMLDivElement>(null);

	// useEffect(() => {
	// 	if (selectedOrganisation) {
	// 		Object.keys(selectedOrganisation).length > 0 &&
	// 			Object.keys(selectedUserOrganisation).length === 0 &&
	// 			setSelectedUserOrganisation(selectedOrganisation);
	// 	}
	// }, [selectedOrganisation]);

	const methods = useForm({
		defaultValues: {
			apply_to_option: {
				locations: [],
			},
		},
	});

	const handleExportPDF = useCallback(async () => {
		// PDF export functionality temporarily disabled due to missing dependencies
		// To enable: install html-to-image and html2pdf.js packages
		console.log('PDF export temporarily disabled');
	}, [isExporting,]);

	const handleLocationUpdate = (selectedLocations: Location[]) => {
		const locations = selectedLocations.map((stObj) => ({
			location_id: stObj.id,
			station_ids: stObj.stations?.map(s => s.id) || [],
		}));
		const selectedStationNames = locations
			.reduce((acc, loc) => {
				const locationData = selectedAllLocations.find(
					(l) => l.id === loc.location_id
				);
				if (locationData && loc.station_ids) {
					// Filter and map station names that match the selected station IDs
					const stationNames = locationData.stations
						.filter((station) =>
							loc.station_ids.includes(station.id)
						)
						.map((station) => station.name);
					acc.push(...stationNames);
				}
				return acc;
			}, [] as string[])
			.join(", ");
		setStations(selectedStationNames);

		setAnalyticsQuery({
			...analyticsQuery,
			locations,
		});
	};

	// Simulate API call for analytics data
	const fetchAnalyticsData = useCallback(async () => {
		setIsLoading(true);
		try {
			const response = await DummyAnalyticsService.getAnalyticsData(analyticsQuery);
			setAnalyticsData(response.data.data);
		} catch (error) {
			console.error('Error fetching analytics data:', error);
		} finally {
			setIsLoading(false);
		}
	}, [analyticsQuery]);

	// Load data on mount and when query changes
	useEffect(() => {
		fetchAnalyticsData();
	}, [fetchAnalyticsData]);

	// console.log(statisticsRefs.current.filter((item) => Boolean(item)));

	// useLayoutEffect(() => {
	// 	{
	// 		setAnalyticsQuery({
	// 			...analyticsQuery,
	// 			start_date: selectedOrganisation?.created_at?.toString(),
	// 		});
	// 	}
	// }, []);

	return (
		<main className="flex flex-1 flex-col">
			{/* <Header title="Analytics" /> */}
			<FormProvider {...methods}>
				<div className="relative w-full bg-white">
					{/* Filters Section */}
					<div className={cn("bg-white lg:relative lg:block")}>
						<div className="px-4 pb-6 pt-4 ">
							<div className="mx-auto max-w-[1800px]">
								<div className="flex gap-4 mlg:hidden">
									<div className="flex w-full gap-4">
										<Select defaultValue="all-services">
											<SelectTrigger className="h-9 w-full border-gray-200 bg-white px-3 text-left text-gray-600">
												<SelectValue
													placeholder="All Locations"
													className=""
												>
													{"All Location"}
												</SelectValue>
											</SelectTrigger>
											<SelectContent className="border-0 bg-transparent shadow-none">
												<AllLocations
													locationNameHandler={(
														loc: Location
													) => {
														const allLocs =
															JSON.parse(
																JSON.stringify(
																	selectedAllLocations
																)
															);
														allLocs.push(loc);
														setSelectedAllLocations(
															allLocs
														);
													}}
													stationHandler={
														handleLocationUpdate
													}
												/>
											</SelectContent>
										</Select>
										<ServiceFilter />
										<CategoryFilter />
										<StatusFilter />
										<CalendarDateRangePicker
											initialStartDate={analyticsQuery?.start_date?.toString()}
											initialEndDate={analyticsQuery?.end_date?.toString()}
											onDateRangeChange={({
												start_date,
												end_date,
											}) => {
												setAnalyticsQuery({
													...analyticsQuery,
													start_date,
													end_date,
												});
											}}
											triggerClassName="h-9"
										/>

										<div className="flex gap-2">
											<Button
												variant="outline"
												size="sm"
												className="max-w-9 flex-1 shadow-[0px_1px_2px_0_rgba(16,24,40,0.04)] lg:w-16"
												// onClick={handleRefresh}
												disabled={isLoading}
											>
												<LuRotateCcw
													size={18}
													className={cn({
														"animate-spin":
															isLoading,
													})}
												/>
											</Button>

											<Button
												variant="default"
												className="h-9 w-40 flex-1 bg-primary px-4 font-normal hover:bg-primary/90 disabled:opacity-50"
												// onClick={handleExportPDF}
												disabled={isExporting || isLoading}
											>
												<Download className="mr-2 h-4 w-4" />
												{isExporting
													? "Exporting..."
													: "Export PDF"}
											</Button>
										</div>
									</div>
								</div>
								{/* Mobile Filter Toggle */}
								{/* <MobileAnalyticsFilter
									isMobileFilterOpen={isMobileFilterOpen}
									isReload={isReload}
									isExporting={isExporting}
									selectedAllLocations={selectedAllLocations}
									setSelectedAllLocations={
										setSelectedAllLocations
									}
									setIsMobileFilterOpen={
										setIsMobileFilterOpen
									}
									handleLocationUpdate={handleLocationUpdate}
									handleExportPDF={handleExportPDF}
									setIsReload={setIsReload}
								/> */}
							</div>
						</div>

						{/* Main Content */}
						<div className="px-4 py-6 ">
							<div
								ref={contentRef}
								className="mx-auto max-w-[1800px]"
							>
								<h2 className="mb-6 text-lg font-semibold text-gray-900 sm:text-xl">
									Summary
								</h2>

								<div className="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-5">
									{metrics.map((metric, index) => (
										<MetricCard
											key={index}
											metric={metric}
											showInfo={
												metric.title ===
												"Average Utilization Rate"
											}
										/>
									))}

									<RatingCard
										rating={analyticsData.data.summary?.average_rating?.average ?? 0}
										reviews={analyticsData.data.summary?.average_rating?.review_count ?? 0}
										ref={summaryRatingRef}
									/>
								</div>

								<StatisticsCharts
									analyticsData={analyticsData}
									isLoading={isLoading}
									ref={statisticsRefs}
								/>
							</div>
						</div>
					</div>
				</div>
			</FormProvider>
		</main>
	);
};

export default React.memo(AnalyticsDashboard);

const MobileAnalyticsFilter: React.FC<{
	isMobileFilterOpen: boolean;
	isReload: boolean;
	isExporting: boolean;
	selectedAllLocations: Location[];
	setSelectedAllLocations: React.Dispatch<
		React.SetStateAction<Location[]>
	>;
	setIsMobileFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
	handleLocationUpdate: (ff: Location[]) => any;
	handleExportPDF: (ff: any) => any;
	setIsReload: any;
}> = ({
	isMobileFilterOpen,
	selectedAllLocations,
	// isExporting,
	setSelectedAllLocations,
	setIsMobileFilterOpen,
	handleLocationUpdate,
	// handleExportPDF,
}) => {
		// const queryClient = useQueryClient();
		const { analyticsQuery, setAnalyticsQuery } = useAnalyticsStore();

		return (
			<>
				<Dialog
					open={isMobileFilterOpen}
					onOpenChange={setIsMobileFilterOpen}
				>
					<DialogTrigger className="sticky top-0 flex w-full flex-col space-y-4">
						<Button
							className="w-full justify-between bg-white lg:hidden"
							variant="outline"
							onClick={() =>
								setIsMobileFilterOpen(!isMobileFilterOpen)
							}
						>
							<span>Filters</span>
							{isMobileFilterOpen ? (
								<i className="mgc_close_line before:!text-accent-foreground" />
							) : (
								<i className="mgc_menu_line before:!text-accent-foreground" />
							)}
						</Button>
						<div className="border-b lg:hidden" />
					</DialogTrigger>
					<DialogContent>
						<DialogTitle>
							<DialogHeader>
								<div className="flex items-center justify-between">
									<div className="flex flex-1 items-center space-x-3">
										<p className="text-[22px] font-semibold capitalize leading-[30px] -tracking-[1%] text-main-1">
											Filter Analytics
										</p>
									</div>
									<button
										type="button"
										onClick={() => {
											setIsMobileFilterOpen(
												!isMobileFilterOpen
											);
										}}
									>
										<LuX
											color="#858C95"
											className="cursor-pointer"
											size={20}
										/>
									</button>
								</div>
							</DialogHeader>
						</DialogTitle>
						<div className="mt-3 flex flex-col gap-4">
							<div className="flex w-full flex-col gap-4">
								<Select defaultValue="all-services">
									<SelectTrigger className="h-9 w-full border-gray-200 bg-white px-3 text-left text-gray-600">
										<SelectValue
											placeholder="All Locations"
											className=""
										>
											{"All Location"}
										</SelectValue>
									</SelectTrigger>
									<SelectContent className="border-0 bg-transparent shadow-none">
										<AllLocations
											locationNameHandler={(loc: Location) => {
												const allLocs = JSON.parse(
													JSON.stringify(
														selectedAllLocations
													)
												);
												allLocs.push(loc);
												setSelectedAllLocations(allLocs);
											}}
											stationHandler={handleLocationUpdate}
										/>
									</SelectContent>
								</Select>
								<ServiceFilter />
								<CategoryFilter />
								<StatusFilter />
								<CalendarDateRangePicker
									initialStartDate={analyticsQuery?.start_date?.toString()}
									initialEndDate={analyticsQuery?.end_date?.toString()}
									onDateRangeChange={({
										start_date,
										end_date,
									}) => {
										setAnalyticsQuery({
											...analyticsQuery,
											start_date,
											end_date,
										});
									}}
									triggerClassName="h-9"
								/>

								<div className="flex gap-2">
									<Button
										variant="outline"
										size="sm"
										className="flex-1 shadow-[0px_1px_2px_0_rgba(16,24,40,0.04)] lg:w-16"
									// onClick={() => {
									// 	queryClient.invalidateQueries([
									// 		"get-analytics",
									// 		analyticsQuery,
									// 	]);
									// }}
									// disabled={
									// 	queryClient.getQueryState([
									// 		"get-analytics",
									// 		analyticsQuery,
									// 	])?.isFetching
									// }
									>
										<LuRotateCcw
											size={18}
										// className={cn({
										// 	"animate-spin":
										// 		queryClient.getQueryState([
										// 			"get-analytics",
										// 			analyticsQuery,
										// 		])?.isFetching,
										// })}
										/>
									</Button>

									{/* <Button
									variant="default"
									className="h-9 flex-1 bg-primary px-4 font-normal hover:bg-primary/90 disabled:opacity-50 lg:w-40"
									onClick={handleExportPDF}
									disabled={isExporting}
								>
									<Download className="mr-2 h-4 w-4" />
									{isExporting
										? "Exporting..."
										: "Export PDF"}
								</Button> */}
								</div>
							</div>
						</div>
					</DialogContent>
				</Dialog>
			</>
		);
	};
