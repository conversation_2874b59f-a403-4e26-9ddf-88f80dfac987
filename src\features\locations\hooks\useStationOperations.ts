import { useCallback } from "react";
import { useStationContext } from "../context/StationContext";
import type { CreateProviderStationRequest } from "../types";

export interface UseStationOperationsReturn {
	// Data and state
	stations: any[];
	isLoading: boolean;
	error: Error | null;

	// Operations
	addStation: (
		data: CreateProviderStationRequest & { imageFile?: File }
	) => Promise<void>;
	addStationOnly: (data: {
		name: string;
		description?: string;
	}) => Promise<void>;
	refetchStations: () => void;

	// Context info
	locationId?: string;
	organizationId?: number;
}

/**
 * Custom hook that provides station operations with a simplified interface
 * This hook abstracts the context usage and provides convenient methods
 */
export function useStationOperations(): UseStationOperationsReturn {
	const {
		stations,
		isLoading,
		error,
		refetchStations,
		createStation,
		locationId,
		organizationId,
	} = useStationContext();

	const addStation = useCallback(
		async (data: CreateProviderStationRequest & { imageFile?: File }) => {
			await createStation(data);
		},
		[createStation]
	);

	const addStationOnly = useCallback(
		async (data: { name: string; description?: string }) => {
			// Convert simple station data to full provider station format with minimal data
			const stationData: CreateProviderStationRequest & {
				imageFile?: File;
			} = {
				name: data.name,
				description: data.description || "",
				image: "",
				service_provider_first_name: "",
				service_provider_last_name: "",
				service_provider_email: "",
				service_provider_phone: "",
			};

			await createStation(stationData);
		},
		[createStation]
	);

	return {
		stations,
		isLoading,
		error,
		addStation,
		addStationOnly,
		refetchStations,
		locationId,
		organizationId,
	};
}
