import { apiClient } from "@/lib/api/clients"

export type TriggerTypesData = {
    uuid: string,
    trigger_type: string,
    name: string,
    description: string,
    is_active: boolean,
    is_system: boolean,
    sort_order: number,
    created_at: string,
    updated_at: string
}

export const triggerApi = {
    // Get all available triggers

    getTriggerTypes: async (
        organizationId: number,
        business_id: string,
        active?: boolean,
        is_system?: boolean,
    ): Promise<{
        success: boolean;
        data: TriggerTypesData[];
    }> => {
        const response = await apiClient.get(`/api/v1/automations/trigger-types`, {
            params: {
                active,
                is_system,
            },
            headers: {
                "X-Business-ID": business_id,
                "X-organizationId": organizationId,
            },
        })
        return response.data
    }
}