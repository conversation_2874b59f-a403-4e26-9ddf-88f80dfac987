import { memo, useCallback } from "react";
import {
	DialogHeader,
	DialogTitle,
	DialogDescription,
	DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

interface ConfirmationModalProps {
	data: {
		title: string;
		message: string;
		confirmText?: string;
		cancelText?: string;
		variant?: "default" | "destructive";
		cancelVariant?: "default" | "destructive";
		cancelClassName?: string;
		confirmClassName?: string;
		onConfirm: () => void;
		isLoading?: boolean;
	};
	onClose: () => void;
}

export const ConfirmationModal = memo(
	({ data, onClose }: ConfirmationModalProps) => {
		const {
			title,
			message,
			confirmText = "Confirm",
			cancelText = "Cancel",
			variant = "default",
			cancelVariant = "outline",
			cancelClassName = "",
			confirmClassName = "",
			onConfirm,
			isLoading = false,
		} = data;

		const handleConfirm = useCallback(() => {
			onConfirm();
		}, [onConfirm, onClose]);

		return (
			<>
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{message}</DialogDescription>
				</DialogHeader>
				<DialogFooter>
					<Button
						variant={variant}
						onClick={handleConfirm}
						disabled={isLoading}
						className={cn(
							"min-w-[130px] cursor-pointer border bg-white hover:text-white",
							variant === "destructive" && "text-[#DC2626]",
							confirmClassName
						)}
					>
						{isLoading ? (
							<Loader2 className="h-4 w-4 animate-spin" />
						) : (
							confirmText
						)}
					</Button>
					<Button
						variant={cancelVariant}
						onClick={onClose}
						disabled={isLoading}
						className={cn(
							"bg-primary hover:bg-primary/90 min-w-[130px] cursor-pointer text-white hover:text-white",
							cancelVariant === "destructive" && "text-[#DC2626]",
							cancelClassName
						)}
					>
						{cancelText}
					</Button>
				</DialogFooter>
			</>
		);
	}
);

ConfirmationModal.displayName = "ConfirmationModal";
