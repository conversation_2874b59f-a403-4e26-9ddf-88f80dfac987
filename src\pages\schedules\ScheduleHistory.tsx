import { useUIS<PERSON> } from "@/stores/uiStore";
import { useEffect, useCallback, useState, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
    Settings,
    Download,
    Plus,
    Settings2,
    RefreshCcw,
} from "lucide-react";
import { EmptyContent } from "@/components/ui-components/EmptyContent";
import {
    AllAppointmentListCard,
    type Appointment,
} from "@/components/ui-components/AllAppointmentListCard";
import { Checkbox } from "@/components/common/Checkbox";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import { DeleteConfirmationDialog } from "@/components/common/DeleteConfirmationDialog";

// Mock data for appointments
const mockAppointments: Appointment[] = [
    {
        id: "1",
        patientName: "<PERSON>",
        service: "General Checkup",
        providerName: "<PERSON><PERSON> <PERSON>",
        method: "In Person",
        status: "Upcoming",
        dateTime: "2023-10-17, 1:00 PM",
    },
    {
        id: "2",
        patientName: "<PERSON>",
        service: "Dental Checkup",
        providerName: "Dr. <PERSON>",
        method: "In Person",
        status: "In Progress",
        dateTime: "2023-10-15, 10:00 AM",
    },
    {
        id: "3",
        patientName: "Jane Roe",
        service: "Physical Therapy",
        providerName: "Dr. Johnson",
        method: "In Person",
        status: "Completed",
        dateTime: "2023-10-17, 1:00 PM",
    },
    {
        id: "4",
        patientName: "Alice Brown",
        service: "Annual Checkup",
        providerName: "Dr. Lee",
        method: "In Person",
        status: "No Show",
        dateTime: "2023-10-20, 9:30 AM",
    },
    {
        id: "5",
        patientName: "Bob White",
        service: "Vaccination",
        providerName: "Nurse Kelly",
        method: "In Person",
        status: "Cancelled (Patient)",
        dateTime: "2023-10-22, 2:00 PM",
    },
    {
        id: "6",
        patientName: "Charlie Green",
        service: "Eye Exam",
        providerName: "Dr. Patel",
        method: "In Person",
        status: "Cancelled (Patient)",
        dateTime: "2023-10-25, 11:30 AM",
    },
    {
        id: "7",
        patientName: "Charlie Green",
        service: "Eye Exam",
        providerName: "Dr. Patel",
        method: "In Person",
        status: "Cancelled (Admin)",
        dateTime: "2023-10-25, 11:30 AM",
    },
    {
        id: "8",
        patientName: "Charlie Green",
        service: "Eye Exam",
        providerName: "Dr. Patel",
        method: "In Person",
        status: "Completed",
        dateTime: "2023-10-25, 11:30 AM",
    },
];

export default function ScheduleHistory() {
    const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
    const setPageHeaderContent = useUIStore(
        (state) => state.setPageHeaderContent
    );

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [searchQuery, setSearchQuery] = useState("");
    const [statusFilter, setStatusFilter] = useState<string>("");
    const itemsPerPage = 10;

    // Mock loading and error states
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);

    // Filter appointments based on search and status
    const filteredAppointments = useMemo(() => {
        return mockAppointments.filter((appointment) => {
            const matchesSearch = !searchQuery ||
                appointment.patientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                appointment.service.toLowerCase().includes(searchQuery.toLowerCase()) ||
                appointment.providerName.toLowerCase().includes(searchQuery.toLowerCase());

            const matchesStatus = !statusFilter || appointment.status === statusFilter;

            return matchesSearch && matchesStatus;
        });
    }, [searchQuery, statusFilter]);

    // Pagination calculations
    const totalItems = filteredAppointments.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentAppointments = filteredAppointments.slice(startIndex, startIndex + itemsPerPage);

    // UI state management
    const [selectedAppointments, setSelectedAppointments] = useState<string[]>([]);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [appointmentToDelete, setAppointmentToDelete] = useState<Appointment | null>(null);

    // Check if we have appointments
    const hasAppointments = filteredAppointments.length > 0;

    const handleSearch = useCallback((query: string) => {
        setSearchQuery(query);
        setCurrentPage(1); // Reset to first page when searching
        console.log("Search query:", query);
    }, []);

    const handleSettings = useCallback(() => {
        console.log("Settings clicked");
    }, []);

    const handleAddAppointment = useCallback(() => {
        console.log("Add appointment clicked");
    }, []);

    const handlePageChange = useCallback((page: number) => {
        setCurrentPage(page);
        setSelectedAppointments([]);
        console.log("Changed to page:", page);
    }, []);

    const handlePreviousPage = useCallback(() => {
        if (currentPage > 1) {
            handlePageChange(currentPage - 1);
        }
    }, [currentPage, handlePageChange]);

    const handleNextPage = useCallback(() => {
        if (currentPage < totalPages) {
            handlePageChange(currentPage + 1);
        }
    }, [currentPage, totalPages, handlePageChange]);

    const handleEditAppointment = useCallback((appointment: Appointment) => {
        console.log("Edit appointment:", appointment.id);
    }, []);

    const handleDeleteAppointment = useCallback((appointment: Appointment) => {
        setAppointmentToDelete(appointment);
        setIsDeleteDialogOpen(true);
    }, []);

    const handleConfirmDelete = useCallback(() => {
        if (appointmentToDelete) {
            console.log("Deleting appointment:", appointmentToDelete.id);
            // Here you would call the delete API
            setIsDeleteDialogOpen(false);
            setAppointmentToDelete(null);
        }
    }, [appointmentToDelete]);

    const handleCancelDelete = useCallback(() => {
        setIsDeleteDialogOpen(false);
        setAppointmentToDelete(null);
    }, []);

    const handleInfoAppointment = useCallback((appointment: Appointment) => {
        console.log("Info appointment:", appointment.id);
    }, []);

    const handleEmailPatient = useCallback((appointment: Appointment) => {
        console.log("Email patient:", appointment.patientName);
    }, []);

    const handleSelectAll = useCallback(
        (checked: boolean) => {
            if (checked) {
                const currentPageAppointmentIds = currentAppointments.map(
                    (appointment) => appointment.id
                );
                setSelectedAppointments((prev) => {
                    const newSelections = [
                        ...prev,
                        ...currentPageAppointmentIds.filter(
                            (id) => !prev.includes(id)
                        ),
                    ];
                    console.log(
                        "Selected all appointments on current page:",
                        currentPageAppointmentIds
                    );
                    return newSelections;
                });
            } else {
                const currentPageAppointmentIds = currentAppointments.map(
                    (appointment) => appointment.id
                );
                setSelectedAppointments((prev) => {
                    const newSelections = prev.filter(
                        (id) => !currentPageAppointmentIds.includes(id)
                    );
                    console.log(
                        "Deselected all appointments on current page:",
                        currentPageAppointmentIds
                    );
                    return newSelections;
                });
            }
        },
        [currentAppointments]
    );

    const handleAppointmentSelection = useCallback(
        (appointmentId: string, selected: boolean) => {
            if (selected) {
                setSelectedAppointments((prev) => {
                    const newSelection = [...prev, appointmentId];
                    console.log(
                        "Selected appointment:",
                        appointmentId,
                        "Total selected:",
                        newSelection.length
                    );
                    return newSelection;
                });
            } else {
                setSelectedAppointments((prev) => {
                    const newSelection = prev.filter((id) => id !== appointmentId);
                    console.log(
                        "Deselected appointment:",
                        appointmentId,
                        "Total selected:",
                        newSelection.length
                    );
                    return newSelection;
                });
            }
        },
        []
    );

    const refetch = useCallback(() => {
        console.log("Refetching appointments...");
        setIsLoading(true);
        // Simulate API call
        setTimeout(() => setIsLoading(false), 1000);
    }, []);

    useEffect(() => {
        setBreadcrumbs([
            {
                label: "Schedule",
                href: "/schedule",
            },
            {
                label: "Schedule History",
                isCurrentPage: true,
            },
        ]);
        return () => {
            setBreadcrumbs([]);
        };
    }, [setBreadcrumbs]);

    useEffect(() => {
        const headerContent = (
            <div className="flex flex-1 items-center justify-between">
                <h1 className="text-foreground text-2xl font-bold">
                    All Appointments
                </h1>
                <div className="flex items-center space-x-3">
                    <Button variant="outline" size="icon" className="h-9 w-9">
                        <Download className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="outline"
                        size="icon"
                        className="h-9 w-9"
                    >
                        <Settings2 className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={handleSettings}
                        className="h-9 w-9"
                    >
                        <Settings className="h-4 w-4" />
                    </Button>
                    <Button
                        variant="outline"
                        size="icon"
                        className="h-9 w-9"
                        onClick={refetch}
                    >
                        <RefreshCcw className="h-4 w-4" />
                    </Button>

                    <Button
                        variant="default"
                        onClick={handleAddAppointment}
                        className="h-9 bg-[#005893] px-4 text-white hover:bg-[#004a7a]"
                    >
                        <Plus className="h-4 w-4" />
                        New Appointment
                    </Button>
                </div>
            </div>
        );

        setPageHeaderContent(headerContent);

        return () => {
            setPageHeaderContent(null);
        };
    }, [setPageHeaderContent, handleSettings, refetch, handleAddAppointment]);

    // Show loading state
    if (isLoading) {
        return (
            <div className="flex min-h-[400px] items-center justify-center">
                <div className="text-center">
                    <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-[#005893]"></div>
                    <p className="text-gray-600">Loading appointments...</p>
                </div>
            </div>
        );
    }

    // Show error state
    if (error) {
        return (
            <div className="flex min-h-[400px] items-center justify-center">
                <div className="text-center">
                    <p className="mb-4 text-red-600">Failed to load appointments</p>
                    <Button onClick={refetch} variant="outline">
                        Try Again
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {!hasAppointments ? (
                <div className="flex min-h-[400px] items-center justify-center">
                    <EmptyContent
                        title="No appointments found"
                        description="There are no appointments to display. Add a new appointment to get started."
                        actions={[
                            {
                                label: "Add New Appointment",
                                onClick: handleAddAppointment,
                                variant: "primary",
                            },
                        ]}
                    />
                </div>
            ) : (
                <div>
                    <div className="mt-2 rounded-xl border border-[#E4E4E7] overflow-hidden">
                        <div className="overflow-x-auto">
                            <div className="min-w-[800px]">
                                <div className="flex items-center border-b border-gray-200 py-[15.5px]">
                                    <div className="flex items-center px-4 flex-shrink-0">
                                        <Checkbox
                                            checked={
                                                currentAppointments.length > 0 &&
                                                currentAppointments.every((appointment) =>
                                                    selectedAppointments.includes(
                                                        appointment.id
                                                    )
                                                )
                                            }
                                            onCheckedChange={handleSelectAll}
                                            className="border-[#005893]"
                                        />
                                    </div>

                                    <div className="flex w-80 sm:w-72 md:w-80 min-w-32 items-center gap-3 px-3">
                                        <div className="flex items-center gap-2">
                                            <p className="text-main-1 text-sm text-[14px] font-bold">
                                                Patient Name
                                            </p>
                                            {selectedAppointments.length > 0 && (
                                                <span className="text-xs text-gray-500 hidden sm:inline">
                                                    ({selectedAppointments.length} selected)
                                                </span>
                                            )}
                                        </div>
                                    </div>

                                    <div className="flex w-56 sm:w-48 md:w-56 min-w-28 items-center px-3">
                                        <p className="text-main-1 text-sm text-[14px] font-bold">
                                            Service
                                        </p>
                                    </div>

                                    <div className="flex w-48 sm:w-40 md:w-48 min-w-24 items-center px-3">
                                        <p className="text-main-1 text-sm text-[14px] font-bold">
                                            Provider Name
                                        </p>
                                    </div>

                                    <div className="flex w-36 sm:w-28 md:w-36 min-w-20 items-center px-3">
                                        <p className="text-main-1 text-sm text-[14px] font-bold">
                                            Method
                                        </p>
                                    </div>

                                    <div className="flex w-44 sm:w-36 md:w-44 min-w-24 items-center px-3">
                                        <p className="text-main-1 text-sm text-[14px] font-bold">
                                            Status
                                        </p>
                                    </div>

                                    <div className="flex w-52 sm:w-44 md:w-52 min-w-32 items-center px-3">
                                        <p className="text-main-1 text-sm text-[14px] font-bold">
                                            Date & Time
                                        </p>
                                    </div>
                                </div>

                                {currentAppointments.map((appointment) => (
                                    <AllAppointmentListCard
                                        key={appointment.id}
                                        appointment={appointment}
                                        checked={selectedAppointments.includes(appointment.id)}
                                        onCheckboxChange={handleAppointmentSelection}
                                        className="cursor-pointer hover:bg-gray-50"
                                    />
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {hasAppointments && totalPages > 1 && (
                <div className="mt-2 flex justify-end">
                    <div>
                        <Pagination>
                            <PaginationContent>
                                <PaginationItem>
                                    <PaginationPrevious
                                        onClick={handlePreviousPage}
                                        className={
                                            currentPage === 1
                                                ? "pointer-events-none opacity-50"
                                                : "cursor-pointer"
                                        }
                                    />
                                </PaginationItem>
                                {Array.from(
                                    { length: Math.min(totalPages, 5) },
                                    (_, i) => {
                                        const page = i + 1;
                                        if (totalPages <= 5) {
                                            return page;
                                        }
                                        if (currentPage <= 3) {
                                            return page;
                                        }
                                        if (currentPage >= totalPages - 2) {
                                            return totalPages - 4 + i;
                                        }
                                        return currentPage - 2 + i;
                                    }
                                ).map((page) => (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            onClick={() =>
                                                handlePageChange(page)
                                            }
                                            isActive={currentPage === page}
                                            className="cursor-pointer"
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                ))}

                                {totalPages > 5 &&
                                    currentPage < totalPages - 2 && (
                                        <PaginationItem>
                                            <PaginationEllipsis />
                                        </PaginationItem>
                                    )}

                                <PaginationItem>
                                    <PaginationNext
                                        onClick={handleNextPage}
                                        className={
                                            currentPage === totalPages
                                                ? "pointer-events-none opacity-50"
                                                : "cursor-pointer"
                                        }
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                </div>
            )}

            <DeleteConfirmationDialog
                open={isDeleteDialogOpen}
                onOpenChange={setIsDeleteDialogOpen}
                title="Are you sure you want to delete this appointment?"
                description={`This action cannot be undone and will permanently delete the appointment for ${appointmentToDelete?.patientName || "this patient"}.`}
                onConfirm={handleConfirmDelete}
                onCancel={handleCancelDelete}
                confirmText="Delete Appointment"
                isLoading={false}
            />
        </div>
    );
} 