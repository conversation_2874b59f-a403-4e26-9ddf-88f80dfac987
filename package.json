{"name": "migranium-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --port 3000", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "start": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@azure/msal-browser": "^4.13.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@lexical/code": "^0.33.0", "@lexical/html": "^0.33.0", "@lexical/link": "^0.33.0", "@lexical/list": "^0.33.0", "@lexical/markdown": "^0.33.0", "@lexical/react": "^0.33.0", "@lexical/rich-text": "^0.33.0", "@lexical/table": "^0.33.0", "@lexical/utils": "^0.33.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-query": "^4.39.2", "@tanstack/react-query-devtools": "^4.39.2", "@tanstack/react-table": "^8.21.3", "@types/html2canvas": "^1.0.0", "@types/papaparse": "^5.3.16", "@xyflow/react": "^12.8.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "countries-and-timezones": "^3.8.0", "country-data-list": "^1.4.1", "country-state-city": "^3.2.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.23.6", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "init": "^0.1.2", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "lexical": "^0.33.0", "libphonenumber-js": "^1.12.9", "lucide-react": "^0.516.0", "moment": "^2.30.1", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "qrcode.react": "^4.2.0", "react": "^19.1.0", "react-big-calendar": "^1.19.4", "react-circle-flags": "^0.0.23", "react-color": "^2.19.3", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.3", "react-router": "^7.6.2", "react-router-dom": "^7.6.2", "react-spinners": "^0.17.0", "react-tooltip": "^5.29.1", "reactflow": "^11.11.4", "recharts": "^3.0.2", "shadcn": "^2.6.4", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@chromatic-com/storybook": "4.0.0", "@eslint/js": "^9.25.0", "@storybook/addon-a11y": "9.0.11", "@storybook/addon-docs": "9.0.11", "@storybook/addon-onboarding": "9.0.11", "@storybook/addon-vitest": "9.0.11", "@storybook/react-vite": "9.0.11", "@tanstack/eslint-plugin-query": "4", "@testing-library/react": "^16.3.0", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-big-calendar": "^1.16.2", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "@vitest/browser": "^3.2.3", "@vitest/coverage-v8": "^3.2.3", "eslint": "^9.25.0", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "9.0.11", "globals": "^16.0.0", "playwright": "^1.53.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "storybook": "9.0.11", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.3"}, "packageManager": "pnpm@9.15.3+sha512.1f79bc245a66eb0b07c5d4d83131240774642caaa86ef7d0434ab47c0d16f66b04e21e0c086eb61e62c77efc4d7f7ec071afad3796af64892fae66509173893a"}