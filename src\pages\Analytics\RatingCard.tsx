import { Card, CardContent } from "@/components/ui/card";
import DynamicStar from "./DynamicStar";
import { ThumbsUp } from "lucide-react";
import React, { forwardRef } from "react";

interface RatingCardProps {
	rating: any;
	reviews?: any;
	className?: string;
}
const RatingCard = forwardRef<HTMLDivElement, RatingCardProps>(
	({ rating, reviews, className = "" }, ref) => {
		return (
			<Card
				className={`h-[130px] min-w-[250px] flex-1 rounded-lg border-0 bg-[#F8F9FB] ${className}`}
				ref={ref}
			>
				<CardContent className="p-[9px]">
					<div className="flex items-start gap-8">
						<div className="flex h-[90px] w-[90px] items-center justify-center">
							<DynamicStar rating={rating} />
						</div>
						<div className="flex flex-col pt-[12.5px]">
							<span className="mb-0.5 text-[30px] font-semibold leading-8 text-gray-900">
								{rating.toFixed(1)}
							</span>
							<span className="text-xs font-normal leading-tight text-[#596574]">
								Average Rating
							</span>
							<div className="mt-1.5 flex items-center gap-1.5">
								<ThumbsUp className="h-4 w-4 text-amber-500" />
								<span className="text-xs font-normal leading-tight text-[#09090B]">
									{reviews.toLocaleString()} Reviews
								</span>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		);
	}
);

RatingCard.displayName = "Rating Card";

export default RatingCard;
