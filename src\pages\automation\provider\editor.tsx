import {
    type Dispatch,
    createContext,
    useContext,
    useReducer,
} from 'react'
import type { EditorActions, EditorNodeType } from "../libs/type"

export type EditorNode = EditorNodeType

export type Editor = {
    elements: EditorNode[]
    edges: {
        id: string
        source: string
        target: string
    }[]
    selectedNode: EditorNodeType
}

export type HistoryState = {
    history: Editor[]
    currentIndex: number
}

export type EditorState = {
    editor: Editor
    history: HistoryState
}

const initialEditorState: EditorState['editor'] = {
    elements: [],
    selectedNode: {
        data: {
            completed: false,
            current: false,
            metadata: {
                type: "Trigger",
                triggerType: "",
            },
            title: '',
            description: 'Select the trigger that start the automation.',
            type: 'Trigger',
        },
        id: '',
        position: { x: 0, y: 0 },
        type: 'Trigger',
    },
    edges: [],
}

const initialHistoryState: HistoryState = {
    history: [initialEditorState],
    currentIndex: 0,
}

const initialState: EditorState = {
    editor: initialEditorState,
    history: initialHistoryState,
}

const editorReducer = (
    state: EditorState = initialState,
    action: EditorActions
): EditorState => {
    // Helper function to add a new state to history
    const addToHistory = (newEditorState: Editor) => {
        // Truncate history after currentIndex to prevent branching
        const newHistory = state.history.history.slice(0, state.history.currentIndex + 1);
        // Limit history size (e.g., max 50 entries to prevent memory issues)
        const maxHistoryLength = 50;
        if (newHistory.length >= maxHistoryLength) {
            newHistory.shift(); // Remove oldest entry
        }
        return {
            history: [...newHistory, newEditorState],
            currentIndex: newHistory.length,
        };
    };

    switch (action.type) {
        case 'REDO':
            if (state.history.currentIndex < state.history.history.length - 1) {
                const nextIndex = state.history.currentIndex + 1;
                const nextEditorState = { ...state.history.history[nextIndex] };
                return {
                    ...state,
                    editor: nextEditorState,
                    history: {
                        ...state.history,
                        currentIndex: nextIndex,
                    },
                };
            }
            return state;

        case 'UNDO':
            if (state.history.currentIndex > 0) {
                const prevIndex = state.history.currentIndex - 1;
                const prevEditorState = { ...state.history.history[prevIndex] };
                return {
                    ...state,
                    editor: prevEditorState,
                    history: {
                        ...state.history,
                        currentIndex: prevIndex,
                    },
                };
            }
            return state;

        case 'LOAD_DATA': {
            const newEditorState = {
                ...state.editor,
                elements: action.payload.elements || initialEditorState.elements,
                edges: action.payload.edges || initialEditorState.edges,
            };
            return {
                ...state,
                editor: newEditorState,
                history: addToHistory(newEditorState),
            };
        }

        case 'SELECTED_ELEMENT': {
            const newEditorState = {
                ...state.editor,
                selectedNode: action.payload.element,
            };
            return {
                ...state,
                editor: newEditorState,
                history: addToHistory(newEditorState),
            };
        }

        case 'DELETE_NODE': {
            const { nodeId } = action.payload;
            const updatedElements = state.editor.elements.filter((node) => node.id !== nodeId);
            const updatedEdges = state.editor.edges.filter((edge) => edge.source !== nodeId && edge.target !== nodeId);
            const newEditorState = {
                ...state.editor,
                elements: updatedElements,
                edges: updatedEdges,
                selectedNode: state.editor.selectedNode?.id === nodeId ? initialEditorState.selectedNode : state.editor.selectedNode,
            };
            return {
                ...state,
                editor: newEditorState,
                history: addToHistory(newEditorState),
            };
        }
        
        // New action for updating node metadata
        case 'UPDATE_NODE_METADATA': {
            const updatedElements = state.editor.elements.map((node) =>
                node.id === action.payload.nodeId
                    ? {
                        ...node,
                        data: {
                            ...node.data,
                            metadata: action.payload.metadata,
                        },
                    }
                    : node
            );
            const newEditorState = {
                ...state.editor,
                elements: updatedElements,
            };
            return {
                ...state,
                editor: newEditorState,
                history: addToHistory(newEditorState),
            };
        }

        default:
            return state;
    }
};

export const EditorContext = createContext<{
    state: EditorState
    dispatch: Dispatch<EditorActions>
}>({
    state: initialState,
    dispatch: () => undefined,
})

type EditorProps = {
    children: React.ReactNode
}

const EditorProvider = (props: EditorProps) => {
    const [state, dispatch] = useReducer(editorReducer, initialState)

    return (
        <EditorContext.Provider
            value={{
                state,
                dispatch,
            }}
        >
            {props.children}
        </EditorContext.Provider>
    )
}

export const useEditor = () => {
    const context = useContext(EditorContext)
    if (!context) {
        throw new Error('useEditor Hook must be used within the editor Provider')
    }
    return context
}

export default EditorProvider
