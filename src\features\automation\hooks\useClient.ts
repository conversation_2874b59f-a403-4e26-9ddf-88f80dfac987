import { useOrganizationContext } from "@/features/organizations/context";
import { useQuery } from "@tanstack/react-query";
import { clientStatus, type ClientStatusData } from "@/features/automation/api/client";

export function useGetClientsStatus({ enabled }: { enabled: boolean }) { 
    const organizationId = useOrganizationContext()?.organizationId
    if (!organizationId) {
        throw new Error("Organization ID is required")
    }
    return useQuery<ClientStatusData>({
        queryKey: ["clientsStatus", organizationId],
        queryFn: () => clientStatus.getClientStatus(organizationId),
        enabled
    })
}