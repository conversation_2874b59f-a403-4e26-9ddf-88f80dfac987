import { z } from "zod";

export const filterSchema = z.object({
    location: z.string().optional(),
    providers: z.array(z.string()),
    services: z.array(z.string()),
    appointmentMethods: z.array(z.string()),
    dateRange: z.string().optional(),
})

export type FilterFormData = z.infer<typeof filterSchema>;

// Interface for applying filters to API calls
export interface AppointmentFilters {
    locationId?: string;
    providerIds?: number[]; // Actually station IDs (stations are the "providers" in the UI)
    serviceIds?: number[];
    appointmentMethods?: number[];
    dateFrom?: string;
    dateTo?: string;
}