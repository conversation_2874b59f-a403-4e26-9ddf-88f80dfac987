import { Button } from "@/components/ui/Button/Button";
import clsx from "clsx";
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import { DefineActionSchema, type DefineActionSchemaType } from "../schema/define-action";
import { FaPlusCircle } from "react-icons/fa";
import { Select, SelectItem, SelectContent, SelectTrigger, SelectValue } from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import type { EditorCanvasCardMetaType, EditorNode } from "../libs/type";

type DefineActionProps = {
    open: boolean;
    onClose?: () => void;
    selectedNode: EditorNode;
    onNext: () => void;
    onUpdateMetadata: (metadata: EditorCanvasCardMetaType) => void;
}

export default function DefineAction({ open, onClose, selectedNode, onNext, onUpdateMetadata }: DefineActionProps) {
    const [fullyOpen, setFullyOpen] = useState(false);

    useEffect(() => {
        if (open) {
            setTimeout(() => {
                setFullyOpen(open);
            }, 10);
        }
    }, [open]);

    const defineActionForm = useForm<DefineActionSchemaType>({
        resolver: zodResolver(DefineActionSchema)
    })

    const handleAddAction = (key: "ifMatch" | "elseMatch") => {
        defineActionForm.setValue(key, [...defineActionForm.getValues(key), ""]);
        defineActionForm.trigger(key);
    }

    useEffect(() => {
        if (selectedNode.data.metadata.type !== "IF Match" && selectedNode.data.metadata.type !== "Else Match") {
            onClose?.();
            defineActionForm.reset({
                ifMatch: [],
                elseMatch: [],
            });
        }
        if (selectedNode.data.metadata.type === "IF Match") {
            defineActionForm.reset({
                ifMatch: selectedNode.data.metadata.action || [""],
            });
        }
        if (selectedNode.data.metadata.type === "Else Match") {
            defineActionForm.reset({
                elseMatch: selectedNode.data.metadata.action || [""],
            });
        }

    }, [selectedNode]);

    const onSubmit = (type: "IF Match" | "Else Match") => {
        const updatedMetadata = {
            type: type,
            action: defineActionForm.getValues(type === "IF Match" ? "ifMatch" : "elseMatch"),
        }

        if (onUpdateMetadata) {
            onUpdateMetadata(updatedMetadata);
        }

        onNext();
    }

    return (
        <div className={clsx("z-50 fixed top-5 right-[4%] w-[30rem] bg-white border border-[#00589340] shadow-[0px_2px_4px_-1px_#0000000F,0px_0px_6px_-1px_#0000001A] transition-transform duration-300 ease-in-out will-change-transform rounded-xl py-5", fullyOpen ? "translate-x-0" : "translate-x-[115%]")}>

            <div className="flex items-center justify-between">
                <h1 className="text-[#27272A] text-xl font-semibold ml-6">Define Action</h1>
                <Button
                    variant="ghost"
                    className="!px-0 w-11 h-10.5 rounded-lg cursor-pointer mr-3" onClick={onClose}>
                    <X className="text-base" color="#27272A" />
                </Button>
            </div>

            <div className="min-h-[85vh] h-full flex flex-col justify-between">
                <div className="flex flex-col gap-y-5 px-5 mt-8">

                    {selectedNode.data.metadata.type === "IF Match" && (
                        <div className="bg-[#F4F4F5] py-3 px-5 rounded-xl">
                            <h1 className="text-[#27272A] text-base font-medium">IF Match</h1>

                            <div className="mt-4 flex flex-col gap-y-3">
                                {(defineActionForm.watch("ifMatch") || []).map((action, index) => (
                                    <div key={index} className="flex flex-col gap-y-1.5">
                                        <label htmlFor="" className="text-[#27272A] text-sm font-medium">Select Action</label>
                                        <Select onValueChange={(value) => defineActionForm.setValue(`ifMatch.${index}`, value as string)} value={action}>
                                            <SelectTrigger className="w-full bg-white">
                                                <SelectValue placeholder="Select Action" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="redirect-to-registration-form">Redirect to registration form</SelectItem>
                                                <SelectItem value="email">Email</SelectItem>
                                                <SelectItem value="sms">SMS</SelectItem>
                                                <SelectItem value="notify">Notify User</SelectItem>
                                                <SelectItem value="log-event">Log Event</SelectItem>
                                                <SelectItem value="trigger-webhook">Trigger Webhook</SelectItem>
                                                <SelectItem value="update-database">Update Database</SelectItem>
                                                <SelectItem value="show-modal">Show Modal</SelectItem>
                                                <SelectItem value="download-file">Download File</SelectItem>
                                                <SelectItem value="api-call">API Call</SelectItem>
                                                <SelectItem value="assign-role">Assign Role</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                ))}
                            </div>

                            {(defineActionForm.watch("ifMatch") || []).length < 3 && (
                                <button onClick={() => handleAddAction("ifMatch")} className="flex items-center gap-x-2 text-[#005893] text-sm font-medium mt-4 cursor-pointer">
                                    <FaPlusCircle /> Add Another
                                </button>
                            )}

                        </div>
                    )}

                    {selectedNode.data.metadata.type === "Else Match" && (
                        <div className="bg-[#F4F4F5] py-3 px-5 rounded-xl">
                            <h1 className="text-[#27272A] text-base font-medium">Else Match</h1>

                            <div className="mt-4 flex flex-col gap-y-3">
                                {(defineActionForm.watch("elseMatch") || []).map((action, index) => (
                                    <div key={index} className="flex flex-col gap-y-1.5">
                                        <label htmlFor="" className="text-[#27272A] text-sm font-medium">Select Action</label>
                                        <Select onValueChange={(value) => defineActionForm.setValue(`elseMatch.${index}`, value as string)} value={action}>
                                            <SelectTrigger className="w-full bg-white">
                                                <SelectValue placeholder="Select Action" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="redirect-to-registration-form">Redirect to registration form</SelectItem>
                                                <SelectItem value="email">Email</SelectItem>
                                                <SelectItem value="sms">SMS</SelectItem>
                                                <SelectItem value="notify">Notify User</SelectItem>
                                                <SelectItem value="log-event">Log Event</SelectItem>
                                                <SelectItem value="trigger-webhook">Trigger Webhook</SelectItem>
                                                <SelectItem value="update-database">Update Database</SelectItem>
                                                <SelectItem value="show-modal">Show Modal</SelectItem>
                                                <SelectItem value="download-file">Download File</SelectItem>
                                                <SelectItem value="api-call">API Call</SelectItem>
                                                <SelectItem value="assign-role">Assign Role</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                ))}
                            </div>

                            {(defineActionForm.watch("elseMatch") || []).length < 3 && (
                                <button onClick={() => handleAddAction("elseMatch")} className="flex items-center gap-x-2 text-[#005893] text-sm font-medium mt-4 cursor-pointer">
                                    <FaPlusCircle /> Add Another
                                </button>
                            )}

                        </div>
                    )}
                </div>
                <div className="flex justify-end px-4">
                    <Button className="w-[7.5rem] cursor-pointer" onClick={() => onSubmit(selectedNode.data.metadata.type === "IF Match" ? "IF Match" : "Else Match")}>
                        Add Action
                    </Button>
                </div>
            </div>

        </div>
    )
}
