import { apiClient } from "@/lib/api/clients";

export type GetActionTypesData = {
    uuid: string;
    action_type: string;
    name: string;
    description: string;
    config_schema: {
        fields: {
            to: {
                type: string;
                required: boolean;
            };
            subject: {
                type: string;
                required: boolean;
            };
            body: {
                type: string;
                required: boolean;
            };
            template: {
                type: string;
                required: boolean;
            };
        };
    };
    default_config: any[];
    required_fields: string[];
    optional_fields: string[];
    is_active: boolean;
    is_system: boolean;
    sort_order: number;
    display_name: string;
    created_at: string;
    updated_at: string;
}

export const actionApi = {
    getActionTypes: async (
        organizationId: number,
        business_id: string,
    ): Promise<{
        success: boolean;
        data: GetActionTypesData[];
    }> => {
        const response = await apiClient.get(`/api/v1/automations/action-types`, {
            headers: {
                "X-Business-ID": business_id,
                "X-organizationId": organizationId,
            }
        })
        return response.data
    }
}