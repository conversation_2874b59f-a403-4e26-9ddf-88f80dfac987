import { useState, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON> } from "react";
import { X } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useLocations } from "@/features/locations/hooks/useLocations";
import { useAllStations } from "@/features/locations/hooks/useStations";
import { useServices } from "@/features/locations/hooks/useServices";
import { MultiSelectDropdown } from "@/components/common/MultiSelectDropdown";
import { useGetFormResponsesMarkType } from "../store/slices/formResponseSlice";
import { useClients } from "@/hooks/useClients";
import {
	DatePicker,
	type DateRange,
} from "@/components/common/Datepicker/DatePicker";

interface FilterData {
	location_ids: string[];
	station_ids: string[];
	service_ids: string[];
	form_types: string[];
	form_statuses: string[];
	statuses: string[];
	flags: string[];
	client_ids: string[];
	date_from: string;
	date_to: string;
}

interface FormResponsesFilterSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onApplyFilters?: (filters: FilterData) => void;
	isLoading: boolean;
	clearFilters?: () => void;
}

// Define the ref interface
export interface FormResponsesFilterSheetRef {
	handleReset: () => void;
}

const FORM_TYPES = [
	{
		value: "all",
		label: "All",
	},
	{
		value: "intake",
		label: "Intake",
	},
	{
		value: "service",
		label: "Service",
	},
	{
		value: "general",
		label: "General Inquiry",
	},
	{
		value: "feedback",
		label: "Feedback",
	},
];

const FORM_STATUS = [
	{
		value: "all",
		label: "All",
	},
	{
		value: "draft",
		label: "Draft",
	},
	{
		value: "live",
		label: "Live",
	},
	{
		value: "inactive",
		label: "Inactive",
	},
];

const FORM_RESPONSE_STATUS = [
	{
		value: "all",
		label: "All",
	},
	{
		value: "pending",
		label: "Pending",
	},
	{
		value: "approved",
		label: "Approved",
	},
	{
		value: "declined",
		label: "Declined",
	},
	{
		value: "completed",
		label: "Completed",
	},
	{
		value: "blocked",
		label: "Blocked",
	},
];

export const FormResponsesFilterSheet = forwardRef<
	FormResponsesFilterSheetRef,
	FormResponsesFilterSheetProps
>(({ open, onOpenChange, onApplyFilters, isLoading, clearFilters }, ref) => {
	const { organizationId } = useOrganizationContext();

	const { data: markTypeData, isLoading: isLoadingMarkType } =
		useGetFormResponsesMarkType();

	const [selectedFormStatuses, setSelectedFormStatuses] = useState<string[]>([
		"all",
	]);
	const [selectedLocations, setSelectedLocations] = useState<string[]>([
		"all",
	]);
	const [selectedProviders, setSelectedProviders] = useState<string[]>([
		"all",
	]);
	const [selectedFormTypes, setSelectedFormTypes] = useState<string[]>([
		"all",
	]);
	const [selectedServices, setSelectedServices] = useState<string[]>(["all"]);
	const [selectedDateRange, setSelectedDateRange] = useState<
		DateRange | undefined
	>(undefined);
	const [selectedFlags, setSelectedFlags] = useState<string[]>(["all"]);
	const [selectedClients, setSelectedClients] = useState<string[]>(["all"]);
	const [selectedFormResponseStatuses, setSelectedFormResponseStatuses] =
		useState<string[]>(["all"]);

	const { data: clientsData, isLoading: isLoadingClients } = useClients();

	// Fetch real data from APIs
	const { data: locationsData, isLoading: isLoadingLocations } = useLocations(
		{},
		organizationId || undefined
	);

	const { data: stationsData, isLoading: isLoadingStations } = useAllStations(
		{
			organizationId: organizationId || undefined,
			enabled: !!organizationId,
		}
	);

	const { data: servicesData, isLoading: isLoadingServices } = useServices({
		organizationId: organizationId || undefined,
		enabled: !!organizationId,
	});

	const [filters, setFilters] = useState<FilterData>({
		location_ids: [],
		station_ids: [],
		service_ids: [],
		form_types: [],
		form_statuses: [],
		statuses: [],
		flags: [],
		client_ids: [],
		date_from: "",
		date_to: "",
	});

	const handleReset = () => {
		setSelectedLocations(["all"]);
		setSelectedProviders(["all"]);
		setSelectedServices(["all"]);
		setSelectedFormTypes(["all"]);
		setSelectedFormStatuses(["all"]);
		setSelectedFlags(["all"]);
		setSelectedClients(["all"]);
		setSelectedFormResponseStatuses(["all"]);
		setSelectedDateRange(undefined);
		setFilters({
			location_ids: [],
			station_ids: [],
			service_ids: [],
			form_types: [],
			form_statuses: [],
			statuses: [],
			flags: [],
			client_ids: [],
			date_from: "",
			date_to: "",
		});
	};

	// Expose handleReset to parent component
	useImperativeHandle(ref, () => ({
		handleReset,
	}));

	const handleApply = () => {
		// Transform selected values to actual data for filtering
		const appliedFilters: FilterData = {
			location_ids:
				selectedLocations.length === 1 &&
				selectedLocations.includes("all")
					? []
					: selectedLocations.filter((id) => id !== "all"),
			station_ids:
				selectedProviders.length === 1 &&
				selectedProviders.includes("all")
					? []
					: selectedProviders.filter((id) => id !== "all"),
			service_ids:
				selectedServices.length === 1 &&
				selectedServices.includes("all")
					? []
					: selectedServices.filter((id) => id !== "all"),
			form_statuses: selectedFormStatuses.includes("all")
				? []
				: selectedFormStatuses.filter((status) => status !== "all"),
			form_types: selectedFormTypes.includes("all")
				? []
				: selectedFormTypes.filter((type) => type !== "all"),
			statuses: selectedFormResponseStatuses.includes("all")
				? []
				: selectedFormResponseStatuses.filter(
						(status) => status !== "all"
					),
			flags: selectedFlags.includes("all")
				? []
				: selectedFlags.filter((flag) => flag !== "all"),
			client_ids: selectedClients.includes("all")
				? []
				: selectedClients.filter((client) => client !== "all"),
			date_from: selectedDateRange?.from?.toISOString() || "",
			date_to: selectedDateRange?.to?.toISOString() || "",
		};

		onApplyFilters?.(appliedFilters);
		onOpenChange(false);
	};

	const handleCancel = () => {
		onOpenChange(false);
	};

	// Transform API data to options format
	const locationOptions = [
		{ value: "all", label: "All" },
		...(locationsData?.map((location) => ({
			value: location.id.toString(),
			label: location.name,
		})) || []),
	];

	const providerOptions = [
		{ value: "all", label: "All" },
		...(stationsData?.data?.map((station) => ({
			value: station.id.toString(),
			label: station.name,
		})) || []),
	];

	const serviceOptions = [
		{ value: "all", label: "All" },
		...(servicesData?.data?.map((service) => ({
			value: service.id.toString(),
			label: service.name,
		})) || []),
	];

	const formFlagsOptions = [
		{ value: "all", label: "All" },
		...(markTypeData?.data?.map((flag) => ({
			value: flag.value,
			label: flag.label,
			color: flag.color,
		})) || []),
	];

	const clientOptions = [
		{ value: "all", label: "All" },
		...(clientsData?.data?.map((client) => ({
			value: client.id.toString(),
			label: `${client.first_name} ${client.last_name}`,
		})) || []),
	];

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full px-9 py-9 sm:w-[540px] sm:max-w-[525px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<SheetTitle className="flex items-center justify-between">
						<span className="text-3xl font-semibold">
							Filter Forms
						</span>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6"
						>
							<X className="h-4 w-4" />
						</Button>
					</SheetTitle>
					<p className="text-muted-foreground text-[15px]">
						Select options below to help filter your search
					</p>
				</SheetHeader>

				<div className="space-y-6 overflow-y-auto px-0.5">
					{/* Locations Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Locations
						</label>
						<MultiSelectDropdown
							options={locationOptions}
							onValueChange={(values) => {
								const wasAllSelected =
									selectedLocations.includes("all");
								const isAllSelected = values.includes("all");

								let filteredValues;

								if (isAllSelected && !wasAllSelected) {
									// User just clicked 'all' - keep only 'all'
									filteredValues = ["all"];
								} else if (values.length > 1 && isAllSelected) {
									// Multiple values selected including 'all' - remove 'all'
									filteredValues = values.filter(
										(value) => value !== "all"
									);
								} else {
									// Normal selection
									filteredValues = values;
								}

								setSelectedLocations(filteredValues);
							}}
							value={selectedLocations}
							placeholder={
								isLoadingLocations
									? "Loading locations..."
									: "Select locations"
							}
							className="w-full"
							disabled={isLoadingLocations}
						/>
					</div>

					{/* Form Types Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Form Types
						</label>
						<MultiSelectDropdown
							options={FORM_TYPES}
							onValueChange={(values) => {
								const wasAllSelected =
									selectedFormTypes.includes("all");
								const isAllSelected = values.includes("all");

								let filteredValues;

								if (isAllSelected && !wasAllSelected) {
									// User just clicked 'all' - keep only 'all'
									filteredValues = ["all"];
								} else if (values.length > 1 && isAllSelected) {
									// Multiple values selected including 'all' - remove 'all'
									filteredValues = values.filter(
										(value) => value !== "all"
									);
								} else {
									// Normal selection
									filteredValues = values;
								}

								setSelectedFormTypes(filteredValues);
							}}
							value={selectedFormTypes}
							placeholder={"Select form types"}
							className="w-full"
							disabled={false}
							maxSelectedDisplay={13}
						/>
					</div>

					{/* Form Response Status Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Form Response Status
						</label>
						<MultiSelectDropdown
							options={FORM_RESPONSE_STATUS}
							onValueChange={(values) => {
								const wasAllSelected =
									selectedFormResponseStatuses.includes(
										"all"
									);
								const isAllSelected = values.includes("all");

								let filteredValues;

								if (isAllSelected && !wasAllSelected) {
									// User just clicked 'all' - keep only 'all'
									filteredValues = ["all"];
								} else if (values.length > 1 && isAllSelected) {
									// Multiple values selected including 'all' - remove 'all'
									filteredValues = values.filter(
										(value) => value !== "all"
									);
								} else {
									// Normal selection
									filteredValues = values;
								}

								setSelectedFormResponseStatuses(filteredValues);
							}}
							value={selectedFormResponseStatuses}
							placeholder={"Select form response statuses"}
							className="w-full"
							disabled={false}
							maxSelectedDisplay={13}
						/>
					</div>

					{/* Form Status Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Form Status
						</label>
						<MultiSelectDropdown
							options={FORM_STATUS}
							onValueChange={(values) => {
								const wasAllSelected =
									selectedFormStatuses.includes("all");
								const isAllSelected = values.includes("all");

								let filteredValues;

								if (isAllSelected && !wasAllSelected) {
									// User just clicked 'all' - keep only 'all'
									filteredValues = ["all"];
								} else if (values.length > 1 && isAllSelected) {
									// Multiple values selected including 'all' - remove 'all'
									filteredValues = values.filter(
										(value) => value !== "all"
									);
								} else {
									// Normal selection
									filteredValues = values;
								}

								setSelectedFormStatuses(filteredValues);
							}}
							value={selectedFormStatuses}
							placeholder={"Select form statuses"}
							className="w-full"
							disabled={false}
						/>
					</div>

					{/* Flags Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Flags
						</label>
						<MultiSelectDropdown
							options={formFlagsOptions}
							onValueChange={(values) => {
								// Check if 'all' was just selected (not previously selected)
								const wasAllSelected =
									selectedFlags.includes("all");
								const isAllSelected = values.includes("all");

								let filteredValues;

								if (isAllSelected && !wasAllSelected) {
									// User just clicked 'all' - keep only 'all'
									filteredValues = ["all"];
								} else if (values.length > 1 && isAllSelected) {
									// Multiple values selected including 'all' - remove 'all'
									filteredValues = values.filter(
										(value) => value !== "all"
									);
								} else {
									// Normal selection
									filteredValues = values;
								}

								setSelectedFlags(filteredValues);
							}}
							value={selectedFlags}
							placeholder={
								isLoadingMarkType
									? "Loading flags..."
									: "Select flags"
							}
							className="w-full"
							disabled={isLoadingMarkType}
						/>
					</div>

					{/* Providers Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Providers
						</label>
						<MultiSelectDropdown
							options={providerOptions}
							onValueChange={(values) => {
								const wasAllSelected =
									selectedProviders.includes("all");
								const isAllSelected = values.includes("all");

								let filteredValues;

								if (isAllSelected && !wasAllSelected) {
									// User just clicked 'all' - keep only 'all'
									filteredValues = ["all"];
								} else if (values.length > 1 && isAllSelected) {
									// Multiple values selected including 'all' - remove 'all'
									filteredValues = values.filter(
										(value) => value !== "all"
									);
								} else {
									// Normal selection
									filteredValues = values;
								}

								setSelectedProviders(filteredValues);
							}}
							value={selectedProviders}
							placeholder={
								isLoadingStations
									? "Loading providers..."
									: "Select providers"
							}
							className="w-full"
							disabled={isLoadingStations}
						/>
					</div>

					{/* Services Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Services
						</label>
						<MultiSelectDropdown
							options={serviceOptions}
							onValueChange={(values) => {
								const wasAllSelected =
									selectedServices.includes("all");
								const isAllSelected = values.includes("all");

								let filteredValues;

								if (isAllSelected && !wasAllSelected) {
									// User just clicked 'all' - keep only 'all'
									filteredValues = ["all"];
								} else if (values.length > 1 && isAllSelected) {
									// Multiple values selected including 'all' - remove 'all'
									filteredValues = values.filter(
										(value) => value !== "all"
									);
								} else {
									// Normal selection
									filteredValues = values;
								}

								setSelectedServices(filteredValues);
							}}
							value={selectedServices}
							placeholder={
								isLoadingServices
									? "Loading services..."
									: "Select services"
							}
							className="w-full"
							disabled={isLoadingServices}
						/>
					</div>

					{/* Patients Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Patients
						</label>
						<MultiSelectDropdown
							options={clientOptions}
							onValueChange={(values) => {
								const wasAllSelected =
									selectedClients.includes("all");
								const isAllSelected = values.includes("all");

								let filteredValues;

								if (isAllSelected && !wasAllSelected) {
									// User just clicked 'all' - keep only 'all'
									filteredValues = ["all"];
								} else if (values.length > 1 && isAllSelected) {
									// Multiple values selected including 'all' - remove 'all'
									filteredValues = values.filter(
										(value) => value !== "all"
									);
								} else {
									// Normal selection
									filteredValues = values;
								}

								setSelectedClients(filteredValues);
							}}
							value={selectedClients}
							placeholder={"Select clients"}
							className="w-full"
							disabled={false}
							maxSelectedDisplay={13}
						/>
					</div>

					{/* Date Range Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Select a Date or Range
						</label>
						<DatePicker
							variant="range"
							value={selectedDateRange}
							onChange={(dateRange) => {
								setSelectedDateRange(dateRange as DateRange);
							}}
							placeholder="Pick a date"
							className="w-full"
							disabled={false}
							size="lg"
						/>
					</div>
				</div>

				<SheetFooter className="flex-row justify-between px-4 py-0">
					<Button
						variant="ghost"
						type="button"
						onClick={handleReset}
						className="text-muted-foreground hover:text-foreground cursor-pointer"
					>
						Reset
					</Button>
					<div className="flex gap-3">
						<Button
							className="cursor-pointer"
							variant="outline"
							type="button"
							onClick={handleCancel}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							className="cursor-pointer"
							onClick={handleApply}
						>
							Apply
						</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
});
