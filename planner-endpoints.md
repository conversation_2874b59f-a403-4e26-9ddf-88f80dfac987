# 🗓️ Migranium Planner System - Complete Documentation

## Overview
The Migranium Planner System is a comprehensive scheduling management platform with **14 specialized endpoints** organized into 4 main categories. It provides hierarchical planning capabilities for organizations, locations, and stations with sophisticated rule-based scheduling.

## 📋 Endpoint Categories

### 1. **Schedule Planners** (4 endpoints)
Core planner management with hierarchical inheritance

### 2. **Planner Rules** (6 endpoints) 
Business rules for availability, restrictions, and time management

### 3. **Planner Utilities** (4 endpoints)
Helper endpoints for configuration and data retrieval

---

## 🏗️ 1. SCHEDULE PLANNERS

### **GET /api/planner/planners/**
**List schedule planners**
- **Purpose**: Retrieve paginated list of schedule planners with filtering
- **Key Features**:
  - Comprehensive filtering (organization, target type, active status)
  - Search functionality across planner names and organizations
  - Include/exclude specific fields for performance optimization
  - Supports pagination with customizable page sizes

**Query Parameters**:
```
- target_type: organization | location | locationstation
- organization_id: UUID filter
- is_active: boolean filter
- search: text search across names
- include: details,time_offs,service_rules,category_rules,stats
- exclude: created_at,updated_at
- ordering: field ordering (prefix '-' for desc)
- page, per_page: pagination controls
```

### **POST /api/planner/planners/**
**Create schedule planner**
- **Purpose**: Create new planner for organization, location, or station
- **Validation**: Prevents duplicate planners for same target
- **Hierarchy**: Establishes inheritance relationships

### **GET /api/planner/planners/{id}/**
**Get planner details**
- **Purpose**: Retrieve comprehensive planner information
- **Includes**: Associated time-offs, service rules, and category rules
- **Response**: Full planner configuration with nested relationships

### **PUT/PATCH /api/planner/planners/{id}/**
**Update planner**
- **Purpose**: Modify existing planner configuration
- **Validation**: Ensures business rule compliance
- **Flexibility**: Supports both full and partial updates

### **DELETE /api/planner/planners/{id}/**
**Delete planner**
- **Purpose**: Remove planner and all associated rules
- **Safety**: Cascades deletion to prevent orphaned rules
- **Audit**: Maintains deletion audit trail

### **POST /api/planner/planners/{id}/validate_config/**
**Validate planner configuration**
- **Purpose**: Check configuration validity and detect conflicts
- **Validation**: Comprehensive rule conflict detection
- **Response**: Detailed validation results with conflict explanations

### **GET /api/planner/planners/stats/**
**Get planner statistics**
- **Purpose**: Retrieve system-wide planner metrics
- **Metrics**: 
  - Total/active planners count
  - Rules distribution by type
  - Organization breakdown
  - Usage statistics

---

## 📏 2. PLANNER RULES

### **Category Rules Management**

#### **GET /api/planner/category-rules/**
**List category rules**
- **Purpose**: Manage client category-based scheduling rules
- **Filtering**: By planner, category, status, preference type
- **Search**: Across rule names and descriptions

#### **POST /api/planner/category-rules/**
**Create category rule**
- **Purpose**: Define availability/restriction rules for client categories
- **Validation**: Rule conflict detection and priority management

#### **GET/PUT/PATCH/DELETE /api/planner/category-rules/{id}/**
**Manage specific category rule**
- **Operations**: Full CRUD operations for individual rules
- **Validation**: Maintains rule integrity and hierarchy

**Category Rule Properties**:
```json
{
  "client_category": "UUID",
  "rule_name": "string",
  "priority": "integer (1 = highest)",
  "preference_type": "availability | restriction",
  "weekdays": [0,1,2,3,4,5,6],
  "start_time": "HH:MM",
  "end_time": "HH:MM",
  "is_all_day": "boolean",
  "max_frequency": "integer",
  "frequency_period": "daily | weekly | monthly | yearly",
  "start_date": "YYYY-MM-DD",
  "end_date": "YYYY-MM-DD",
  "occurrence_pattern": "once | rolling | fixed_dates",
  "recurrence_interval": "integer",
  "status": "active | inactive | override | conflicted"
}
```

### **Service Rules Management**

#### **GET /api/planner/service-rules/**
**List service rules**
- **Purpose**: Manage service-specific scheduling rules
- **Filtering**: By planner, service, preference type
- **Integration**: Links with core service definitions

#### **POST /api/planner/service-rules/**
**Create service rule**
- **Purpose**: Define service-specific availability patterns
- **Validation**: Service existence verification and rule validation

#### **GET/PUT/PATCH/DELETE /api/planner/service-rules/{id}/**
**Manage specific service rule**
- **Operations**: Complete service rule lifecycle management
- **Integration**: Maintains service catalog synchronization

**Service Rule Properties**:
```json
{
  "service": "UUID reference",
  "rule_name": "string",
  "priority": "integer",
  "preference_type": "availability | restriction",
  "weekdays": [0,1,2,3,4,5,6],
  "time_constraints": "same as category rules",
  "frequency_limits": "same as category rules",
  "date_ranges": "same as category rules"
}
```

### **Time-Off Management**

#### **GET /api/planner/time-offs/**
**List time offs**
- **Purpose**: Manage scheduled downtime periods
- **Filtering**: By planner, date range, recurrence status
- **Planning**: Supports both one-time and recurring time-offs

#### **POST /api/planner/time-offs/**
**Create time off**
- **Purpose**: Schedule planned unavailability periods
- **Flexibility**: Supports various recurrence patterns
- **Validation**: Prevents conflicting time-off periods

#### **GET/PUT/PATCH/DELETE /api/planner/time-offs/{id}/**
**Manage specific time off**
- **Operations**: Full time-off lifecycle management
- **Recurrence**: Handles complex recurring patterns

**Time-Off Properties**:
```json
{
  "title": "string",
  "start_date": "YYYY-MM-DD",
  "end_date": "YYYY-MM-DD (optional)",
  "start_time": "HH:MM (optional for all-day)",
  "end_time": "HH:MM (optional for all-day)",
  "is_all_day": "boolean",
  "is_recurring": "boolean",
  "recurrence_frequency": "daily | weekly | monthly | yearly",
  "recurrence_interval": "integer",
  "recurrence_weekdays": [0,1,2,3,4,5,6],
  "recurrence_end_date": "YYYY-MM-DD"
}
```

---

## 🛠️ 3. PLANNER UTILITIES

### **GET /api/planner/utilities/choices/**
**Get planner choices**
- **Purpose**: Retrieve available configuration options
- **Response**: All valid enum values for dropdowns and validation
- **Frontend**: Essential for form building and validation

**Response Structure**:
```json
{
  "recurrence_frequencies": ["daily", "weekly", "monthly", "yearly"],
  "preference_types": ["availability", "restriction"],
  "weekday_choices": [
    {"value": 0, "label": "Monday"},
    {"value": 1, "label": "Tuesday"},
    // ... etc
  ]
}
```

### **GET /api/planner/utilities/organization/{organization_id}/**
**Get organization planner**
- **Purpose**: Retrieve organization-level planner configuration
- **Inheritance**: Shows base-level planning rules
- **Structure**: Foundation for location and station inheritance

### **GET /api/planner/utilities/location/{location_id}/**
**Get location planner**
- **Purpose**: Retrieve location-specific planner with inheritance info
- **Inheritance**: Shows organization inheritance and local overrides
- **Hierarchy**: Intermediate level in planning hierarchy

### **GET /api/planner/utilities/station/{station_id}/**
**Get location station planner**
- **Purpose**: Retrieve station-specific planner with full inheritance chain
- **Inheritance**: Shows complete inheritance from organization → location → station
- **Granularity**: Most specific level of planning control

**Inheritance Response Pattern**:
```json
{
  "has_own_planner": "boolean",
  "inherits_from_organization": "boolean",
  "inherits_from_location": "boolean (station only)",
  "time_offs": "inherited/local rules",
  "service_preferences": "inherited/local rules",
  "category_preferences": "inherited/local rules"
}
```

---

## 🏗️ System Architecture

### **Hierarchical Inheritance**
```
Organization Planner (Base Level)
    ↓ inherits from
Location Planner (Intermediate Level)
    ↓ inherits from  
Station Planner (Granular Level)
```

### **Rule Priority System**
1. **Lower numbers = Higher priority** (Priority 1 = highest)
2. **Station rules override Location rules**
3. **Location rules override Organization rules**
4. **Explicit restrictions override inherited availability**

### **Rule Types**
- **Availability Rules**: Define when booking is allowed
- **Restriction Rules**: Define when booking is blocked
- **Time-Off Rules**: Define unavailable periods
- **Category Rules**: Client category-specific rules
- **Service Rules**: Service-specific rules

### **Status Management**
- **Active**: Rule is currently enforced
- **Inactive**: Rule is disabled but preserved
- **Override**: Temporary status change
- **Conflicted**: Rule conflicts detected

---

## 🚀 Key Features

### **Advanced Filtering & Search**
- Multi-criteria filtering across all endpoints
- Full-text search capabilities
- Performance-optimized field inclusion/exclusion

### **Conflict Detection**
- Automatic rule conflict identification
- Validation endpoint for configuration checking
- Priority-based conflict resolution

### **Inheritance Management**
- Three-tier inheritance hierarchy
- Override capabilities at each level
- Inheritance tracking and visualization

### **Recurrence Patterns**
- Complex recurring time-off patterns
- Rolling vs fixed date patterns
- Weekday-specific recurrence

### **Integration Points**
- Core service catalog integration
- Client category system integration
- Organization structure integration

This planner system provides enterprise-grade scheduling management with the flexibility to handle complex business rules while maintaining data integrity and performance optimization.