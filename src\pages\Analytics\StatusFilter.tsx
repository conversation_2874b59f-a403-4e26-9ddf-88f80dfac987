
import React from "react";
import { dummyFilterData } from './data';
import MultiSelect from "@/components/ui-extended/multi-select";
import { toTitleCase } from "@/utils/functions";
import { useAnalyticsStore } from './useAnalyticsStore';

const StatusFilter: React.FC = () => {
  const { analyticsQuery, setAnalyticsQuery } = useAnalyticsStore();

	return (
		<MultiSelect
			width="100%"
      options={dummyFilterData.statuses}
			placeholder="All Statuses"
			className="h-9"
			selected={
				analyticsQuery?.statuses?.map((item) => ({
					value: item.toString(),
					label: toTitleCase(item.toString().split("_").join(" ")),
				})) ?? []
			}
			onSelect={(option) => {
				setAnalyticsQuery({
					statuses: option.map((item) => item.value),
				});
			}}
			onUnselect={(selectedOption) =>
				setAnalyticsQuery({
					statuses: analyticsQuery?.statuses?.filter(
						(s) => s !== selectedOption.value
					),
				})
			}
			textContainerClassName="text-left justify-between"
		/>
	);
};

export default StatusFilter;
