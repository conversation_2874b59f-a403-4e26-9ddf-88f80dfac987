import { useState, useMemo } from "react";
import { X, Upload, User, Search, Info, HelpCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import { InputPhone } from "@/components/common/InputPhone";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	SheetHeader,
	SheetTitle,
} from "@/components/ui/sheet";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { Checkbox } from "@/components/common/Checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { useCreateMember } from "../../hooks/useMembers";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useAllStations } from "../../hooks/useStations";
import { useLocations } from "../../hooks/useLocations";
import type { CreateMemberRequest, MemberRole } from "../../api/membersApi";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";

interface AddMemberSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (data: any) => void;
}

interface MemberFormData {
	memberInformation: {
		firstName: string;
		lastName: string;
		email: string;
		phoneNumber: string;
	};
	socialRoles: string[];
	roleDetails: {
		[key: string]: {
			stationIds: number[];
			locationIds: number[];
		};
	};
}

export function AddMemberSheet({
	open,
	onOpenChange,
	onSubmit,
}: AddMemberSheetProps) {
	const { organizationId } = useOrganizationContext();

	// Fetch locations for role assignment
	const locationsResponse = useLocations(
		{},
		typeof organizationId === "number" ? organizationId : undefined
	);
	const locations = locationsResponse?.data || [];

	// Create member mutation
	const createMemberMutation = useCreateMember({
		organizationId: String(organizationId || ""),
		onSuccess: (data) => {
			toast.success("Team member added successfully!");
			onSubmit?.(data);
			onOpenChange(false);
			resetForm();
		},
		onError: (error) => {
			toast.error("Failed to add team member: " + error.message);
		},
	});

	const [formData, setFormData] = useState<MemberFormData>({
		memberInformation: {
			firstName: "",
			lastName: "",
			email: "",
			phoneNumber: "",
		},
		socialRoles: [],
		roleDetails: {},
	});

	const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
	const [searchTerms, setSearchTerms] = useState({
		locations: "",
		stations: "",
	});

	const socialRoleOptions = [
		{
			name: "Organization Manager",
			backendRole: "BUSINESS_MANAGER" as const,
			description:
				"Ultimate access for all system aspects, user management, system settings, data oversight, and full platform operation across all locations.",
			tooltipContent: (
				<div className="max-w-xs space-y-2">
					<div>
						<p className="font-medium">Access to -</p>
						<p>All Locations, All Stations, All Members</p>
					</div>
					<div>
						<p className="font-medium">Actions -</p>
						<p>
							Add, Edit, and Remove (
							<span className="text-blue-400">Locations</span>,{" "}
							<span className="text-blue-400">Team Members</span>,{" "}
							<span className="text-blue-400">Stations</span>,){" "}
							Edit Settings (
							<span className="text-blue-400">Business</span> +{" "}
							<span className="text-blue-400">Appointments</span>
							); Reset Account Details of Admins & Partners
						</p>
					</div>
				</div>
			),
		},
		{
			name: "Location Manager",
			backendRole: "LOCATION_MANAGER" as const,
			description:
				"Creates and oversees stations, waitlist, and schedule operations for selected location(s).",
			tooltipContent: (
				<div className="max-w-xs space-y-2">
					<div>
						<p className="font-medium">Access to -</p>
						<p>Selected Locations, All Stations, All Partners</p>
					</div>
					<div>
						<p className="font-medium">Actions -</p>
						<p>
							Add, Edit, and Remove (
							<span className="text-blue-400">Team Members</span>,{" "}
							<span className="text-blue-400">Stations</span>);{" "}
							Edit Settings(
							<span className="text-blue-400">
								Location +
							</span>,{" "}
							<span className="text-blue-400">Appointments</span>)
						</p>
					</div>
				</div>
			),
		},
		{
			name: "Station Manager",
			backendRole: "STATION_MANAGER" as const,
			description:
				"Creates and oversees waitlist and schedule operations for selected station(s).",
			tooltipContent: (
				<div className="max-w-xs space-y-2">
					<div>
						<p className="font-medium">Access to -</p>
						<p>Selected Stations, All Partners</p>
					</div>
					<div>
						<p className="font-medium">Actions -</p>
						<p>
							Add, Edit, and Remove (
							<span className="text-blue-400">
								Stations they manage
							</span>
							,{" "}
							<span className="text-blue-400">Appointments</span>,{" "}
							<span className="text-blue-400">Services</span>,{" "}
							<span className="text-blue-400">Forms</span>,{" "}
							<span className="text-blue-400">Categories</span>,{" "}
							<span className="text-blue-400">Stations</span>);{" "}
							Edit Settings(
							<span className="text-blue-400">
								Waitlist +{" "}
							</span>,{" "}
							<span className="text-blue-400">Schedule</span>)
						</p>
					</div>
				</div>
			),
		},
		{
			name: "Service Provider",
			backendRole: "SERVICE_PROVIDER" as const,
			description:
				"Creates and oversees waitlist and schedule operations for their tagged station(s).",
			tooltipContent: (
				<div className="max-w-xs space-y-2">
					<div>
						<p className="font-medium">Access to -</p>
						<p>All Locations, All Stations</p>
					</div>
					<div>
						<p className="font-medium">Actions -</p>
						<p>
							Add, Edit, and Remove
							<span className="text-blue-400">Appointments</span>;
							View (
							<span className="text-blue-400">
								All Appointments
							</span>
							,{" "}
							<span className="text-blue-400">All Services</span>,{" "}
							<span className="text-blue-400">All Locations</span>
							,{" "}
							<span className="text-blue-400">All Stations</span>,{" "}
							)
						</p>
					</div>
				</div>
			),
		},
		{
			name: "Team Member",
			backendRole: "TEAM_MEMBER" as const,
			description:
				"Can view all activity at selected station/location(s).",
			tooltipContent: (
				<div className="max-w-xs space-y-2">
					<div>
						<p className="font-medium">Access to -</p>
						<p>Selected Stations, Patients</p>
					</div>
					<div>
						<p className="font-medium">Actions -</p>
						<p>
							Add, Edit, and Remove (
							<span className="text-blue-400">Services</span>,{" "}
							<span className="text-blue-400">Forms</span>,{" "}
							<span className="text-blue-400">Appointments</span>,{" "}
							<span className="text-blue-400">Waitlist</span>,{" "}
							<span className="text-blue-400">Clients</span>
							); Add Client Notes
						</p>
					</div>
				</div>
			),
		},
	];

	// Filtered data based on search terms
	const filteredLocations = useMemo(() => {
		return locations.filter((location: any) =>
			location.name
				.toLowerCase()
				.includes(searchTerms.locations.toLowerCase())
		);
	}, [searchTerms.locations, locations]);

	// Fetch all stations for the organization
	const stationsResponse = useAllStations({
		organizationId:
			typeof organizationId === "number" ? organizationId : undefined,
	});
	const stations = stationsResponse?.data?.data || [];

	const filteredStations = useMemo(() => {
		return stations.filter((station) =>
			station.name
				.toLowerCase()
				.includes(searchTerms.stations.toLowerCase())
		);
	}, [searchTerms.stations]);

	const handleInputChange = (field: string, value: string) => {
		if (field.includes(".")) {
			const [section, subField] = field.split(".");
			setFormData((prev) => ({
				...prev,
				[section]: {
					...(prev[section as keyof MemberFormData] as object),
					[subField]: value,
				},
			}));
		} else {
			setFormData((prev) => ({
				...prev,
				[field]: value,
			}));
		}
	};

	const handleRoleToggle = (roleName: string) => {
		setFormData((prev) => {
			const isRoleSelected = prev.socialRoles.includes(roleName);
			const newSocialRoles = isRoleSelected
				? prev.socialRoles.filter((r) => r !== roleName)
				: [...prev.socialRoles, roleName];

			// If removing a role, also remove its details
			const newRoleDetails = { ...prev.roleDetails };
			if (isRoleSelected) {
				delete newRoleDetails[roleName];
			} else {
				// Initialize empty arrays for new role
				newRoleDetails[roleName] = {
					stationIds: [],
					locationIds: [],
				};
			}

			return {
				...prev,
				socialRoles: newSocialRoles,
				roleDetails: newRoleDetails,
			};
		});
	};

	const handleStationToggle = (role: string, stationId: number) => {
		setFormData((prev) => ({
			...prev,
			roleDetails: {
				...prev.roleDetails,
				[role]: {
					...prev.roleDetails[role],
					stationIds: prev.roleDetails[role]?.stationIds.includes(
						stationId
					)
						? prev.roleDetails[role].stationIds.filter(
							(id) => id !== stationId
						)
						: [
							...(prev.roleDetails[role]?.stationIds || []),
							stationId,
						],
				},
			},
		}));
	};

	const handleLocationToggle = (role: string, locationId: number) => {
		setFormData((prev) => ({
			...prev,
			roleDetails: {
				...prev.roleDetails,
				[role]: {
					...prev.roleDetails[role],
					locationIds: prev.roleDetails[role]?.locationIds.includes(
						locationId
					)
						? prev.roleDetails[role].locationIds.filter(
							(id) => id !== locationId
						)
						: [
							...(prev.roleDetails[role]?.locationIds || []),
							locationId,
						],
				},
			},
		}));
	};

	const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			const reader = new FileReader();
			reader.onload = (e) => {
				setAvatarPreview(e.target?.result as string);
			};
			reader.readAsDataURL(file);
		}
	};

	const resetForm = () => {
		setFormData({
			memberInformation: {
				firstName: "",
				lastName: "",
				email: "",
				phoneNumber: "",
			},
			socialRoles: [],
			roleDetails: {},
		});
		setAvatarPreview(null);
		setSearchTerms({
			locations: "",
			stations: "",
		});
	};

	const handleSubmit = () => {
		if (!organizationId) {
			toast.error("No organization selected");
			return;
		}

		// Convert form data to API format
		const roles: MemberRole[] = formData.socialRoles.map((roleName) => {
			const roleOption = socialRoleOptions.find(
				(r) => r.name === roleName
			);
			const roleDetails = formData.roleDetails[roleName];

			const role: MemberRole = {
				role: roleOption?.backendRole || "TEAM_MEMBER",
			};

			// Add station_ids if applicable
			if (roleDetails?.stationIds && roleDetails.stationIds.length > 0) {
				role.station_ids = roleDetails.stationIds;
			}

			// Add location_ids if applicable
			if (
				roleDetails?.locationIds &&
				roleDetails.locationIds.length > 0
			) {
				role.location_ids = roleDetails.locationIds;
			}

			return role;
		});

		const createMemberData: CreateMemberRequest = {
			first_name: formData.memberInformation.firstName,
			last_name: formData.memberInformation.lastName,
			email: formData.memberInformation.email,
			phone_number: formData.memberInformation.phoneNumber,
			roles,
		};

		createMemberMutation.mutate(createMemberData);
	};

	const isFormValid =
		formData.memberInformation.firstName &&
		formData.memberInformation.lastName &&
		formData.memberInformation.email &&
		formData.socialRoles.length > 0;

	return (
		<TooltipProvider>
			<Sheet open={open} onOpenChange={onOpenChange}>
				<SheetContent className="z-[1003] w-[600px] overflow-y-auto px-8 py-10 sm:max-w-[600px]">
					<SheetHeader className="pb-6">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<SheetTitle className="text-xl font-semibold">
									Add New Member
								</SheetTitle>
							</div>
						</div>
					</SheetHeader>

					<div className="w-full space-y-6">
						{/* Member Information */}
						<div className="w-full space-y-4">
							<div className="flex items-center gap-2">
								<h3 className="text-lg font-medium">
									Member Information
								</h3>
							</div>
							{/* Avatar Upload
						<div className="flex items-center gap-4">
							<Avatar className="h-16 w-16">
								<AvatarImage src={avatarPreview || undefined} />
								<AvatarFallback className="bg-gray-100">
									<User className="h-6 w-6 text-gray-400" />
								</AvatarFallback>
							</Avatar>
							<div>
								<input
									type="file"
									accept="image/*"
									onChange={handleAvatarUpload}
									className="hidden"
									id="avatar-upload"
								/>
								<label htmlFor="avatar-upload">
									<Button
										variant="outline"
										className="cursor-pointer"
										asChild
									>
										<span>
											<Upload className="mr-2 h-4 w-4" />
											Upload Photo
										</span>
									</Button>
								</label>
							</div>
						</div> */}
							<InputText
								label="First Name *"
								variant={"with-label"}
								className="w-full"
								placeholder="e.g John"
								value={formData.memberInformation.firstName}
								onChange={(e) =>
									handleInputChange(
										"memberInformation.firstName",
										e.target.value
									)
								}
							/>
							<InputText
								label="Last Name *"
								variant={"with-label"}
								className="w-full"
								placeholder="e.g Doe"
								value={formData.memberInformation.lastName}
								onChange={(e) =>
									handleInputChange(
										"memberInformation.lastName",
										e.target.value
									)
								}
							/>
							<InputText
								label="Email Address *"
								placeholder="e.g. <EMAIL>"
								type="email"
								variant={"with-label"}
								value={formData.memberInformation.email}
								onChange={(e) =>
									handleInputChange(
										"memberInformation.email",
										e.target.value
									)
								}
							/>
							<div className="space-y-2">
								<div className="flex items-center gap-2">
									<label className="text-sm font-medium text-gray-900">
										Phone Number
									</label>
								</div>
								<InputPhone
									variant="with-country-dropdown"
									value={
										formData.memberInformation.phoneNumber
									}
									onChange={(value) =>
										handleInputChange(
											"memberInformation.phoneNumber",
											value
										)
									}
									defaultCountry="US"
									placeholder="Enter phone number"
									className="w-full"
									showFlag={true}
									format="international"
									searchable={true}
									showValidation={true}
								/>
							</div>
						</div>

						{/* Social Roles */}
						<div className="space-y-4">
							<div>
								<div className="flex items-center gap-2">
									<h3 className="text-lg font-medium">
										Social Roles
									</h3>
								</div>
								<p className="mt-1 text-sm text-gray-500">
									Choose the role(s) you want to assign to
									this member. Some roles may carry more
									permissions than others.
								</p>
							</div>

							<div className="space-y-3">
								{socialRoleOptions.map((roleOption) => (
									<div
										key={roleOption.name}
										className="space-y-3 rounded-md border p-3"
									>
										<div className="flex items-center space-x-3">
											<Checkbox
												checked={formData.socialRoles.includes(
													roleOption.name
												)}
												onCheckedChange={() =>
													handleRoleToggle(
														roleOption.name
													)
												}
											/>
											<div className="flex-1">
												<div className="flex items-center gap-2">
													<label className="cursor-pointer text-sm font-medium text-gray-900">
														{roleOption.name}
													</label>
													<Tooltip>
														<TooltipTrigger asChild>
															<Info className="h-3 w-3 cursor-help text-gray-400" />
														</TooltipTrigger>
														<TooltipContent className="w-40 shadow-md">
															{
																roleOption.tooltipContent
															}
														</TooltipContent>
													</Tooltip>
												</div>
												<p className="text-xs text-gray-500">
													{roleOption.description}
												</p>
											</div>
										</div>

										{/* Show dropdown options for specific roles */}
										{formData.socialRoles.includes(
											roleOption.name
										) && (
												<div className="ml-8 space-y-2">
													{/* Station assignment for roles that need stations */}
													{(roleOption.name ===
														"Station Manager" ||
														roleOption.name ===
														"Team Member") && (
															<>
																<div className="flex items-center gap-2">
																	<p className="text-sm font-medium text-gray-700">
																		Select Stations:
																	</p>
																	<Tooltip>
																		<TooltipTrigger
																			asChild
																		>
																			<Info className="h-3 w-3 cursor-help text-gray-400" />
																		</TooltipTrigger>
																		<TooltipContent>
																			<p>
																				Choose
																				which
																				stations
																				this
																				member
																				will
																				have
																				access
																				to
																			</p>
																		</TooltipContent>
																	</Tooltip>
																</div>
																<div className="relative">
																	<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
																	<Input
																		placeholder="Search stations..."
																		className="pl-10"
																		value={
																			searchTerms.stations
																		}
																		onChange={(
																			e
																		) => {
																			setSearchTerms(
																				(
																					prev
																				) => ({
																					...prev,
																					stations:
																						e
																							.target
																							.value,
																				})
																			);
																		}}
																	/>
																</div>
																<div className="max-h-32 space-y-2 overflow-y-auto">
																	{stationsResponse?.isLoading ? (
																		<div className="space-y-2 py-2">
																			<Skeleton className="h-4 w-full" />
																			<Skeleton className="h-4 w-3/4" />
																			<Skeleton className="h-4 w-1/2" />
																		</div>
																	) : stationsResponse?.error ? (
																		<p className="text-sm text-red-500 italic">
																			Error
																			loading
																			stations
																		</p>
																	) : filteredStations.length >
																		0 ? (
																		filteredStations.map(
																			(
																				station
																			) => (
																				<div
																					key={
																						station.id
																					}
																					className="flex items-center space-x-2"
																				>
																					<Checkbox
																						checked={
																							formData.roleDetails[
																								roleOption
																									.name
																							]?.stationIds.includes(
																								station.id
																							) ||
																							false
																						}
																						onCheckedChange={() =>
																							handleStationToggle(
																								roleOption.name,
																								station.id
																							)
																						}
																					/>
																					<label className="cursor-pointer text-sm text-gray-600">
																						{
																							station.name
																						}
																					</label>
																				</div>
																			)
																		)
																	) : (
																		<p className="text-sm text-gray-500 italic">
																			{searchTerms.stations
																				? "No stations found matching your search"
																				: "No stations available"}
																		</p>
																	)}
																</div>
															</>
														)}

													{/* Location assignment for roles that need locations */}
													{(roleOption.name ===
														"Location Manager" ||
														roleOption.name ===
														"Team Member") && (
															<>
																<div className="flex items-center gap-2">
																	<p className="text-sm font-medium text-gray-700">
																		Select
																		Locations:
																	</p>
																	<Tooltip>
																		<TooltipTrigger
																			asChild
																		>
																			<Info className="h-3 w-3 cursor-help text-gray-400" />
																		</TooltipTrigger>
																		<TooltipContent>
																			<p>
																				Choose
																				which
																				locations
																				this
																				member
																				will
																				have
																				access
																				to
																			</p>
																		</TooltipContent>
																	</Tooltip>
																</div>
																<div className="relative">
																	<Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
																	<Input
																		placeholder="Search locations..."
																		className="pl-10"
																		value={
																			searchTerms.locations
																		}
																		onChange={(
																			e
																		) => {
																			setSearchTerms(
																				(
																					prev
																				) => ({
																					...prev,
																					locations:
																						e
																							.target
																							.value,
																				})
																			);
																		}}
																	/>
																</div>
																<div className="max-h-32 space-y-2 overflow-y-auto">
																	{filteredLocations.map(
																		(
																			location: any
																		) => (
																			<div
																				key={
																					location.id
																				}
																				className="flex items-center space-x-2"
																			>
																				<Checkbox
																					checked={
																						formData.roleDetails[
																							roleOption
																								.name
																						]?.locationIds.includes(
																							parseInt(
																								location.id
																							)
																						) ||
																						false
																					}
																					onCheckedChange={() =>
																						handleLocationToggle(
																							roleOption.name,
																							parseInt(
																								location.id
																							)
																						)
																					}
																				/>
																				<label className="cursor-pointer text-sm text-gray-600">
																					{
																						location.name
																					}
																				</label>
																			</div>
																		)
																	)}
																	{filteredLocations.length ===
																		0 &&
																		searchTerms.locations && (
																			<p className="text-sm text-gray-500 italic">
																				No
																				locations
																				found
																			</p>
																		)}
																</div>
															</>
														)}

													{/* Organization Manager and Service Manager don't need specific assignments */}
													{(roleOption.name ===
														"Organization Manager" ||
														roleOption.name ===
														"Service Manager") && (
															<p className="ml-2 text-sm text-gray-500 italic">
																This role has access to
																all{" "}
																{roleOption.name ===
																	"Organization Manager"
																	? "locations and stations"
																	: "services"}{" "}
																automatically.
															</p>
														)}
												</div>
											)}
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Footer Actions */}
					<div className="flex justify-end gap-3 border-t pt-6">
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									variant="outline"
									onClick={() => onOpenChange(false)}
								>
									Close
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								<p>Cancel and close without saving</p>
							</TooltipContent>
						</Tooltip>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									onClick={handleSubmit}
									disabled={
										!isFormValid ||
										createMemberMutation.isPending
									}
									className="bg-blue-600 hover:bg-blue-700"
								>
									{createMemberMutation.isPending
										? "Adding..."
										: "Send Invite"}
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								<p>
									{!isFormValid
										? "Please fill in required fields (First Name, Last Name, Email, and at least one role)"
										: "Send invitation email to the new member"}
								</p>
							</TooltipContent>
						</Tooltip>
					</div>
				</SheetContent>
			</Sheet>
		</TooltipProvider>
	);
}
