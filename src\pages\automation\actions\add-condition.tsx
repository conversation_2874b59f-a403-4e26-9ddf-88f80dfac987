import { But<PERSON> } from "@/components/ui/Button/Button";
import { cn } from "@/lib/utils";
import { X } from "lucide-react";
import type { EditorCanvasCardMetaType, EditorNode } from "../libs/type";
import { Checkbox } from "@/components/common/Checkbox";
import { useForm, type UseFormReturn } from "react-hook-form";
import { AddConditionSchema, type AddConditionSchemaType } from "../schema/add-condition";
import { useState, useCallback, useEffect } from "react";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { zodResolver } from "@hookform/resolvers/zod";
import type { ConditionTypesData } from "@/features/automation/api/conditions";
import { useGetAppointmentStatus, useGetBusinessCategories, useGetProviders } from "@/features/automation/hooks/useConditions";
import { useGetClientsStatus } from "@/features/automation/hooks/useClient";
import { RefactorMultiSelect } from "@/pages/schedules/components/custom-select";

type AddConditionProps = {
    open: boolean;
    onClose?: () => void;
    onUpdateMetadata?: (metadata: EditorCanvasCardMetaType) => void;
    onNext: () => void;
    selectedNode: EditorNode;
    isLoading: boolean;
    conditionTypes: ConditionTypesData[]
}

type Step2ComponentProps = {
    conditionForm: UseFormReturn<AddConditionSchemaType>,
    category: {
        loading: boolean;
        data: {
            label: string;
            value: string;
        }[]
    },
    provider: {
        loading: boolean;
        data: {
            label: string;
            value: string;
        }[]
    }
    appointmentStatus: {
        loading: boolean;
        data: {
            label: string;
            value: string;
        }[]
    }
    client: {
        loading: boolean;
        data: {
            label: string;
            value: string;
        }[]
    }
}

const Step2Component = ({ conditionForm, category, provider, appointmentStatus, client }: Step2ComponentProps) => {
    return (
        <div className="flex flex-col gap-y-5">
            {conditionForm.watch("condition")?.includes("Category") && (
                <div className="flex flex-col gap-y-1.5">
                    <label htmlFor="category" className="text-[#18181B] text-base">Select Categories</label>
                    <RefactorMultiSelect
                        setValue={(value) => conditionForm.setValue("category", value as string[])}
                        value={conditionForm.watch("category") || []}
                        placeholder={
                            category.loading
                                ? "Loading Categories..."
                                : "Select Categories"
                        }
                        label="Select Categories"
                        id="category"
                        options={category.data.map((item) => item.value)}
                    />
                    {conditionForm.formState.errors.category && (
                        <p className="text-red-500 text-sm">{conditionForm.formState.errors.category.message}</p>
                    )}
                </div>
            )}
            {conditionForm.watch("condition")?.includes("Date & Time") && (
                <div className="flex flex-col gap-y-1.5">
                    <label htmlFor="dateTime" className="text-[#18181B] text-base">Date Check</label>
                    <div className="flex flex-col gap-y-3">
                        <div className="grid grid-cols-2 items-center gap-x-2 gap-y-4">
                            <Select
                                value="before"
                                onValueChange={() => { }}
                            >
                                <SelectTrigger className="w-full py-5">
                                    <SelectValue placeholder="Select Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value="before">Before</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>

                            <DatePicker
                                onChange={(date) => {
                                    conditionForm.setValue("dateTime", {
                                        ...conditionForm.watch("dateTime"),
                                        before: date as Date,
                                    })
                                }}
                                value={conditionForm.watch("dateTime")?.before}
                                placeholder="Pick a date"
                                size="md"
                                variant="default"
                                className="w-full py-5"
                            />

                        </div>
                        <div className="grid grid-cols-2 items-center gap-x-2 gap-y-4">
                            <Select
                                value="after"
                                onValueChange={() => { }}
                            >
                                <SelectTrigger className="w-full py-5">
                                    <SelectValue placeholder="Select Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value="after">After</SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>

                            <DatePicker
                                onChange={(date) => {
                                    conditionForm.setValue("dateTime", {
                                        ...conditionForm.watch("dateTime"),
                                        after: date as Date
                                    })
                                }}
                                value={conditionForm.watch("dateTime")?.after}
                                placeholder="Pick a date"
                                size="md"
                                variant="default"
                                className="w-full py-5"
                            />

                        </div>
                    </div>
                    {conditionForm.formState.errors.dateTime && (
                        <p className="text-red-500 text-sm">{conditionForm.formState.errors.dateTime.message}</p>
                    )}
                </div>
            )}
            {conditionForm.watch("condition")?.includes("Registration") && (
                <div className="flex flex-col gap-y-1.5">
                    <label htmlFor="" className="text-[#27272A] text-sm font-medium">Period Type</label>
                    <RadioGroup className="flex items-center gap-x-3 mt-0.5"
                        value={conditionForm.watch("registration")}
                        onValueChange={(value) => conditionForm.setValue(`registration`, value as "registered" | "not_registered" | "all")}>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="registered" id="registered" />
                            <Label htmlFor="registered">Registered</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="not_registered" id="not_registered" />
                            <Label htmlFor="not_registered">Not Registered</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="all" id="all" />
                            <Label htmlFor="all">All</Label>
                        </div>
                    </RadioGroup>
                    {conditionForm.formState.errors.registration && (
                        <p className="text-red-500 text-sm">{conditionForm.formState.errors.registration.message}</p>
                    )}
                </div>
            )}
            {conditionForm.watch("condition")?.includes("Provider") && (
                <div className="flex flex-col gap-y-1.5">
                    <label htmlFor="location" className="text-[#18181B] text-base">Select Stations</label>
                    <RefactorMultiSelect
                        options={provider.data.map((item) => item.value)}
                        value={conditionForm.watch("provider") || []}
                        setValue={(value) => conditionForm.setValue("provider", value as string[])}
                        placeholder={
                            category.loading
                                ? "Loading Providers..."
                                : "Select Providers"
                        }
                        label="Provider"
                    />
                    {conditionForm.formState.errors.provider && (
                        <p className="text-red-500 text-sm">{conditionForm.formState.errors.provider.message}</p>
                    )}
                </div>
            )}
            {conditionForm.watch("condition")?.includes("Appointment Status") && (
                <div className="flex flex-col gap-y-1.5">
                    <label htmlFor="appointmentStatus" className="text-[#18181B] text-base">Select Appointment Status</label>
                    <RefactorMultiSelect
                        options={appointmentStatus.data.map((item) => item.value)}
                        setValue={(value) => conditionForm.setValue("appointmentStatus", value as string[])}
                        value={conditionForm.watch("appointmentStatus") || []}
                        placeholder={
                            appointmentStatus.loading
                                ? "Loading Appointment Status..."
                                : "Select Appointment Status"
                        }
                        label="Appointment Status"
                    />
                    {conditionForm.formState.errors.appointmentStatus && (
                        <p className="text-red-500 text-sm">{conditionForm.formState.errors.appointmentStatus.message}</p>
                    )}
                </div>
            )}
            {conditionForm.watch("condition")?.includes("Patient Status") && (
                <div className="flex flex-col gap-y-1.5">
                    <label htmlFor="patientStatus" className="text-[#18181B] text-base">Select Patient Status</label>

                    <Select onValueChange={(value) => conditionForm.setValue("patientStatus", value as string)} value={conditionForm.watch("patientStatus") || ""}>
                        <SelectTrigger className="w-full bg-white">
                            <SelectValue placeholder={client.loading ?
                                "Loading Patient Status..."
                                : "Select Patient Status"} />
                        </SelectTrigger>
                        <SelectContent>
                            {client.data.map((opt, index) => (
                                <SelectItem key={index} value={opt.value}>{opt.label}</SelectItem>
                            ))}
                        </SelectContent>
                    </Select>

                    {conditionForm.formState.errors.patientStatus && (
                        <p className="text-red-500 text-sm">{conditionForm.formState.errors.patientStatus.message}</p>
                    )}
                </div>
            )}
        </div>
    )
}

export default function AddCondition({
    open, onClose, onUpdateMetadata, selectedNode, isLoading, conditionTypes
}: AddConditionProps) {
    const [currentStep, setCurrentStep] = useState(1);
    const [fullyOpen, setFullyOpen] = useState(false);

    const [selectedConditions, setSelectedConditions] = useState<string[]>([]);

    const conditionForm = useForm<AddConditionSchemaType>({
        resolver: zodResolver(AddConditionSchema),
    })

    useEffect(() => {
        if (selectedNode.data.metadata.type === "Condition") {
            conditionForm.reset({
                condition: selectedNode.data.metadata.condition || [],
                category: selectedNode.data.metadata.category || [],
                dateTime: selectedNode.data.metadata.dateTime || {},
                registration: selectedNode.data.metadata.registration || undefined,
                location: selectedNode.data.metadata.location || [],
                provider: selectedNode.data.metadata.provider || [],
                service: selectedNode.data.metadata.service || [],
                appointmentStatus: selectedNode.data.metadata.appointmentStatus || [],
                patientStatus: selectedNode.data.metadata.patientStatus || undefined,
            });
            setCurrentStep(1);
            setSelectedConditions(selectedNode.data.metadata?.condition || []);
        }
    }, [selectedNode]);

    useEffect(() => {
        if (open) {
            setTimeout(() => {
                setFullyOpen(open);
            }, 10);
        }
    }, [open]);

    // Map condition to form field(s) to clear
    const conditionFieldMap: Record<string, (form: UseFormReturn<AddConditionSchemaType>) => void> = {
        "Category": (form) => form.setValue("category", []),
        "Date & Time": (form) => form.setValue("dateTime", {}),
        "Patient Status": (form) => form.setValue("patientStatus", undefined),
        "Registration": (form) => form.setValue("registration", undefined),
        "Location": (form) => form.setValue("location", []),
        "Provider": (form) => form.setValue("provider", []),
        "Service": (form) => form.setValue("service", []),
        "Appointment Status": (form) => form.setValue("appointmentStatus", []),
    };

    // Memoized handler for checkbox change
    const handleCheckedChange = useCallback((condition: string, checked: boolean) => {
        if (checked) {
            const newSelected = [...selectedConditions, condition];
            setSelectedConditions(newSelected);
            conditionForm.setValue("condition", [...(conditionForm.watch("condition") || []), condition]);
        } else {
            const newSelected = selectedConditions.filter((c) => c !== condition);
            setSelectedConditions(newSelected);
            conditionForm.setValue("condition", conditionForm.watch("condition")?.filter((c) => c !== condition) || []);
            // Clear the form state for this condition
            if (conditionFieldMap[condition]) {
                conditionFieldMap[condition](conditionForm);
            }
        }
        // Always update node metadata after change
        if (onUpdateMetadata) {
            const updatedMetadata = {
                type: 'Condition' as const,
                condition: conditionForm.getValues("condition"),
                category: conditionForm.getValues("category"),
                dateTime: conditionForm.getValues("dateTime"),
                registration: conditionForm.getValues("registration"),
                location: conditionForm.getValues("location"),
                provider: conditionForm.getValues("provider"),
                patientStatus: conditionForm.getValues("patientStatus"),
                service: conditionForm.getValues("service"),
                appointmentStatus: conditionForm.getValues("appointmentStatus"),
            };
            onUpdateMetadata(updatedMetadata);
        }
    }, [selectedConditions, conditionForm, onUpdateMetadata]);

    const onSubmit = () => {
        const updatedMetadata = {
            type: 'Condition' as const,
            condition: conditionForm.watch("condition"),
            category: conditionForm.watch("category"),
            dateTime: conditionForm.watch("dateTime"),
            registration: conditionForm.watch("registration"),
            location: conditionForm.watch("location"),
            provider: conditionForm.watch("provider"),
            service: conditionForm.watch("service"),
            patientStatus: conditionForm.watch("patientStatus"),
            appointmentStatus: conditionForm.watch("appointmentStatus"),
        };

        if (onUpdateMetadata) {
            onUpdateMetadata(updatedMetadata);
        }
    }

    // for conditions data, categories, providers, etc
    const { data: categoryData, isLoading: categoryLoading } = useGetBusinessCategories({
        enabled: conditionForm.watch("condition")?.includes("Category") ?? false,
    })

    const { data: providerData, isLoading: providerLoading } = useGetProviders({
        enabled: conditionForm.watch("condition")?.includes("Provider") ?? false,
    });

    const { data: appointmentStatusData, isLoading: appointmentStatusLoading } = useGetAppointmentStatus({
        enabled: conditionForm.watch("condition")?.includes("Appointment Status") ?? false,
    })

    const { data: clientsStatus, isLoading: clientsStatusLoading } = useGetClientsStatus({
        enabled: conditionForm.watch("condition")?.includes("Patient Status") ?? false,
    })

    const categoryOptions = categoryData?.data.map((key) => ({
        label: key.name,
        value: key.name,
    })) ?? [];

    const providerOptions = providerData?.data.map((key) => ({
        label: key.name,
        value: key.name,
    })) ?? [];

    const appointmentStatusOptions = appointmentStatusData?.data.map((key) => ({
        label: key.label,
        value: key.label,
    })) ?? [];

    const clientsStatusOptions = clientsStatus?.data.map((key) => ({
        label: key.name,
        value: key.name,
    })) ?? [];

    return (
        <div className={cn("z-50 fixed top-5 right-[4%] w-[30rem] bg-white border border-[#00589340] shadow-[0px_2px_4px_-1px_#0000000F,0px_0px_6px_-1px_#0000001A] transition-transform duration-300 ease-in-out will-change-transform rounded-xl py-5", fullyOpen ? "translate-x-0" : "translate-x-[115%]")}>
            <div className="flex items-center justify-between">
                <h1 className="text-[#27272A] text-xl font-semibold ml-6">Add Condition</h1>
                <Button
                    variant="ghost"
                    className="!px-0 w-11 h-10.5 rounded-lg cursor-pointer mr-3" onClick={onClose}>
                    <X className="text-base" color="#27272A" />
                </Button>
            </div>
            <div className="min-h-[78vh] max-h-[78vh] h-full flex flex-col justify-between px-5 mt-9">
                <div>
                    <div className="flex items-center justify-between gap-x-2">
                        <button onClick={() => setCurrentStep(1)} className={cn(
                            "cursor-pointer font-normal text-xs border border-[#005893] rounded-full size-6 flex items-center justify-center transition-all duration-300",
                            currentStep === 1 ? "bg-[#005893] text-white" : "bg-white text-[#005893]"
                        )}>01</button>
                        <div className="flex-1 flex items-center gap-x-4">
                            <span className={cn(
                                "font-normal text-sm transition-colors duration-300",
                                currentStep === 1 ? "text-[#005893]" : "text-[#A1A1AA]"
                            )}>Select Check Items</span>
                            <div className={cn(
                                "flex-1 h-[1.5px] transition-all duration-300",
                                currentStep === 1 ? "bg-[#E4E4E7] w-full" : "bg-[#005893] w-full"
                            )}></div>
                        </div>
                        <span className={cn(
                            "font-normal text-sm transition-colors duration-300",
                            currentStep === 2 ? "text-[#005893]" : "text-[#A1A1AA] hidden"
                        )}>Set Condition</span>
                        <button
                            onClick={() => setCurrentStep(2)}
                            disabled={selectedConditions.length === 0}
                            className={cn(
                                "cursor-pointer font-normal text-xs border border-[#005893] rounded-full size-6 flex items-center justify-center transition-all duration-300",
                                currentStep === 2 ? "bg-[#005893] text-white" : "bg-white text-[#005893]"
                            )}>02</button>
                    </div>
                    {isLoading ? (
                        <div role="status" className="min-h-[50vh] flex items-center justify-center">
                            <svg aria-hidden="true" className="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor" />
                                <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill" />
                            </svg>
                            <span className="sr-only">Loading...</span>
                        </div>
                    ) : (
                        <div className="relative min-h-[65vh] max-h-[65vh] overflow-y-auto mt-9 scrollbar-hide">
                            <div className={cn(
                                "absolute inset-0 transition-transform duration-300",
                                currentStep === 1 ? "translate-x-0" : "-translate-x-full"
                            )}>
                                <div className="flex flex-col gap-y-7">
                                    {conditionTypes.map((condition) => (
                                        <label htmlFor={condition.uuid} key={condition.uuid} className="flex items-center gap-x-3 cursor-pointer">
                                            <Checkbox
                                                id={condition.uuid}
                                                checked={selectedConditions.includes(condition.name)}
                                                onCheckedChange={(checked) => handleCheckedChange(condition.name, checked as boolean)}
                                            />
                                            {condition.display_name}
                                        </label>
                                    ))}
                                </div>
                            </div>
                            <div className={cn(
                                "absolute inset-0 transition-transform duration-300",
                                currentStep === 2 ? "translate-x-0" : "translate-x-full"
                            )}>
                                <Step2Component
                                    conditionForm={conditionForm}
                                    category={{
                                        loading: categoryLoading,
                                        data: categoryOptions,
                                    }}
                                    provider={{
                                        loading: providerLoading,
                                        data: providerOptions,
                                    }}
                                    appointmentStatus={{
                                        loading: appointmentStatusLoading,
                                        data: appointmentStatusOptions,
                                    }}
                                    client={{
                                        loading: clientsStatusLoading,
                                        data: clientsStatusOptions,
                                    }}
                                />
                            </div>
                        </div>
                    )}
                </div>
                <div className="flex justify-end">
                    {currentStep === 1 && (
                        <Button className="cursor-pointer" disabled={selectedConditions.length === 0} onClick={() => setCurrentStep(2)}>
                            Continue
                        </Button>
                    )}
                    {currentStep === 2 && (
                        <div className="pt-4">
                            <Button className="cursor-pointer" onClick={conditionForm.handleSubmit(onSubmit)}>
                                Add Condition
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}