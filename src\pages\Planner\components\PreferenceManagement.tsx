import React, { useState } from 'react';
import { ChevronRight, ChevronLeft, Search, Plus, Pencil, Trash2, Settings, SlidersHorizontal, Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent } from '@/components/common/Tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';

interface Preference {
    id: string;
    title: string;
    type: string[];
    apply: string;
    taggedProviders?: string;
}

interface ServicePreference {
    id: string;
    serviceName: string;
    preferenceTypes: string[];
    numberOfRules: number;
}

interface CategoryPreference {
    id: string;
    categoryName: string;
    preferenceTypes: string[];
    numberOfRules: number;
}

interface PreferencesManagementProps {
    level: 'organization' | 'location' | 'provider';
    entityName: string;
    onAddOrganizationPreference: () => void;
    onAddServicePreference: () => void;
    onAddCategoryPreference: () => void;
    onEditPreference: (preferenceId: string, type: 'organization' | 'service' | 'category') => void;
    onViewPreference?: (preferenceId: string, type: 'organization' | 'service' | 'category') => void;
    onViewServiceRules?: (service: ServicePreference) => void;
    onBack?: () => void;
}

export const PreferencesManagement: React.FC<PreferencesManagementProps> = ({
    level,
    entityName,
    onAddOrganizationPreference,
    onAddServicePreference,
    onAddCategoryPreference,
    onEditPreference,
    onViewPreference,
    onViewServiceRules,
    onBack
}) => {
    const [activeTab, setActiveTab] = useState('organization');
    const [searchTerm, setSearchTerm] = useState('');

    // Level-aware tab configuration
    const getTabLabel = (tabValue: string) => {
        switch (level) {
            case 'location':
                return tabValue === 'organization' ? 'Location Preferences' :
                    tabValue === 'service' ? 'Service Preferences' :
                        'Category Preferences';
            case 'provider':
                return tabValue === 'organization' ? 'Provider Preferences' :
                    tabValue === 'service' ? 'Service Preferences' :
                        'Category Preferences';
            default:
                return tabValue === 'organization' ? 'Organization Preferences' :
                    tabValue === 'service' ? 'Service Preferences' :
                        'Category Preferences';
        }
    };



    // Sample data for organization preferences
    const organizationPreferences: Preference[] = [
        {
            id: '1',
            title: 'Maximum Appointments: 10 /day',
            type: ['Availability', 'Frequency'],
            apply: 'Cumulative',
            taggedProviders: '',
        },
        {
            id: '2',
            title: 'Blood Work Max',
            type: ['Frequency'],
            apply: 'Individual',
            taggedProviders: '32',
        },
        {
            id: '3',
            title: 'Time Off',
            type: ['Unavailability'],
            apply: '',
            taggedProviders: 'All',
        },
    ];

    // Sample data for service preferences - level-aware
    const getServicePreferences = (): ServicePreference[] => {
        const baseServices = [
            {
                id: '1',
                serviceName: 'General Medical Test',
                preferenceTypes: ['Availability', 'Frequency'],
                numberOfRules: 2,
            },
            {
                id: '2',
                serviceName: 'Blood Work',
                preferenceTypes: ['Availability', 'Frequency'],
                numberOfRules: 2,
            },
            {
                id: '3',
                serviceName: 'Immunization',
                preferenceTypes: ['Availability'],
                numberOfRules: 2,
            },
        ];

        // For location level, filter to location-specific services
        if (level === 'location') {
            return baseServices.filter(service =>
                ['Blood Work', 'Immunization'].includes(service.serviceName)
            );
        }

        return baseServices;
    };

    const servicePreferences = getServicePreferences();

    // Sample data for category preferences
    const categoryPreferences: CategoryPreference[] = [
        {
            id: '1',
            categoryName: 'Laboratory Tests',
            preferenceTypes: ['Availability', 'Frequency'],
            numberOfRules: 3,
        },
        {
            id: '2',
            categoryName: 'Imaging Services',
            preferenceTypes: ['Availability'],
            numberOfRules: 1,
        },
        {
            id: '3',
            categoryName: 'Consultations',
            preferenceTypes: ['Availability', 'Frequency'],
            numberOfRules: 4,
        },
    ];

    const getTypeColor = (type: string) => {
        switch (type.toLowerCase()) {
            case 'availability':
                return 'bg-green-100 text-green-800';
            case 'frequency':
                return 'bg-blue-100 text-blue-800';
            case 'unavailability':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const handleDeletePreference = (preferenceId: string, type: 'organization' | 'service' | 'category') => {
        console.log('Deleting preference:', preferenceId, 'of type:', type);
    };

    const filteredOrgPreferences = organizationPreferences.filter(pref =>
        pref.title.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const filteredServicePreferences = servicePreferences.filter(pref =>
        pref.serviceName.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const filteredCategoryPreferences = categoryPreferences.filter(pref =>
        pref.categoryName.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const renderEmptyState = (type: string, onAdd: () => void) => (
        <TableRow>
            <TableCell colSpan={6} className="text-center py-8">
                <div className="flex flex-col items-center space-y-4">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                        <Search className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="text-center">
                        <h3 className="text-lg font-medium text-gray-900 mb-1">
                            {searchTerm ? `No ${type} preferences found` : `No ${type} Preferences Added`}
                        </h3>
                        <p className="text-gray-500">
                            {searchTerm
                                ? `No ${type} preferences found matching your search.`
                                : 'Add Preferences to customize your experience.'
                            }
                        </p>
                        {!searchTerm && (
                            <Button
                                onClick={onAdd}
                                className="mt-4 bg-blue-600 hover:bg-blue-700"
                            >
                                Add Preference
                            </Button>
                        )}
                    </div>
                </div>
            </TableCell>
        </TableRow>
    );

    // Configure tabs for your custom Tabs component
    const tabItems = [
        {
            value: 'organization',
            label: getTabLabel('organization'),
            count: organizationPreferences.length
        },
        {
            value: 'service',
            label: getTabLabel('service'),
            count: servicePreferences.length
        },
        {
            value: 'category',
            label: getTabLabel('category'),
            count: categoryPreferences.length
        }
    ];

    return (
        <div className="space-y-6 pt-6">
            <div className="px-0">
                <Tabs
                    items={tabItems}
                    value={activeTab}
                    onValueChange={setActiveTab}
                >
                    {/* Organization/Location-Wide/Provider-Wide Preferences Tab */}
                    <TabsContent value="organization" className="mt-0">

                        <div className="flex flex-col gap-y-4 py-4">
                            <div className="flex items-center justify-between ">
                                <h2 className="text-2xl font-semibold">{getTabLabel('organization')}</h2>
                                <div className="flex items-center space-x-3">
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                        <Input
                                            placeholder="Search"
                                            className="pl-10 w-64"
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                        />
                                    </div>
                                    <Button
                                        variant="default"
                                        className='cursor-pointer'
                                        onClick={onAddOrganizationPreference}
                                    >
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add Preference
                                    </Button>
                                </div>
                            </div>
                            <div className='rounded-lg border border-[#E4E4E7] p-0.5'>
                                <Table>
                                    <TableHeader>
                                        <TableRow className="bg-gray-50 h-12 text-[#71717A] hover:bg-transparent hover">
                                            <TableHead className="text-[#71717A]"><Checkbox /></TableHead>
                                            <TableHead className="text-[#71717A]">Preference Title</TableHead>
                                            <TableHead className="text-[#71717A]">Preference Type</TableHead>
                                            <TableHead className="text-[#71717A]">Apply</TableHead>
                                            <TableHead className=" text-[#71717A]">Tagged Providers</TableHead>
                                            <TableHead className="text-[#71717A] text-center">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {filteredOrgPreferences.length === 0 ? (
                                            renderEmptyState('organization', onAddOrganizationPreference)
                                        ) : (
                                            filteredOrgPreferences.map((preference) => (
                                                <TableRow key={preference.id} className="hover:bg-gray-50 h-16">
                                                    <TableCell><Checkbox /></TableCell>
                                                    <TableCell className="font-medium">{preference.title}</TableCell>
                                                    <TableCell>
                                                        <div className="flex space-x-1">
                                                            {preference.type.map((type) => (
                                                                <Badge key={type} className={getTypeColor(type)} variant="secondary">
                                                                    {type}
                                                                </Badge>
                                                            ))}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell><Badge variant="secondary">
                                                        {preference.apply}
                                                    </Badge></TableCell>
                                                    <TableCell><Badge variant="secondary">
                                                        {preference.taggedProviders}
                                                    </Badge></TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex items-center justify-end space-x-2">
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                className="h-8 w-8 p-0"
                                                                onClick={() => onEditPreference(preference.id, 'organization')}
                                                                title="Edit preference"
                                                            >
                                                                <Pencil className="h-4 w-4 text-[#71717A]" />
                                                            </Button>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                className="h-8 w-8 p-0  hover:text-red-700"
                                                                onClick={() => handleDeletePreference(preference.id, 'organization')}
                                                                title="Delete preference"
                                                            >
                                                                <Trash2 className="h-4 w-4 text-[#71717A]" />
                                                            </Button>

                                                            <Button onClick={() => onViewPreference?.(preference.id, 'organization')} variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                                <ChevronRight className="h-4 w-4 text-[#71717A]" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>

                        </div>

                    </TabsContent>

                    {/* Service Preferences Tab */}
                    <TabsContent value="service" className="mt-0">
                        <div className="flex flex-col gap-y-4 py-4">
                            <div className="flex items-center justify-between ">
                                <h2 className="text-2xl font-semibold">{getTabLabel('service')}</h2>
                                <div className="flex items-center space-x-3">
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                        <Input
                                            placeholder="Search"
                                            className="pl-10 w-64"
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                        />
                                    </div>
                                    <Button
                                        className="bg-primary hover:bg-primary/70"
                                        onClick={onAddServicePreference}
                                    >
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add Preference
                                    </Button>
                                </div>
                            </div>
                            <div className='rounded-lg border border-[#E4E4E7] p-0.5'>
                                <Table>
                                    <TableHeader>
                                        <TableRow className="bg-gray-50 h-12 text-[#71717A] hover:bg-transparent">
                                            <TableHead className="text-[#71717A]"><Checkbox /></TableHead>
                                            <TableHead className="text-[#71717A]">Service Name</TableHead>
                                            <TableHead className="text-[#71717A]">Preference Types</TableHead>
                                            <TableHead className="text-[#71717A]">No.of Rules</TableHead>
                                            <TableHead className="text-[#71717A] text-center">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {filteredServicePreferences.length === 0 ? (
                                            renderEmptyState('service', onAddServicePreference)
                                        ) : (
                                            filteredServicePreferences.map((service) => (
                                                <TableRow
                                                    key={service.id}
                                                    className="hover:bg-gray-50 h-16 cursor-pointer"
                                                    onClick={() => onViewServiceRules && onViewServiceRules(service)}
                                                >
                                                    <TableCell onClick={(e) => e.stopPropagation()}>
                                                        <Checkbox />
                                                    </TableCell>
                                                    <TableCell className="font-medium">{service.serviceName}</TableCell>
                                                    <TableCell>
                                                        <div className="flex space-x-1">
                                                            {service.preferenceTypes.map((type) => (
                                                                <Badge key={type} className={getTypeColor(type)} variant="secondary">
                                                                    {type}
                                                                </Badge>
                                                            ))}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell><Badge variant="secondary">
                                                        {service.numberOfRules}
                                                    </Badge></TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex items-center justify-end space-x-2">
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                className="h-8 w-8 p-0"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    onEditPreference(service.id, 'service');
                                                                }}
                                                                title="Edit preference"
                                                            >
                                                                <Pencil className="h-4 w-4 text-[#71717A]" />
                                                            </Button>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                className="h-8 w-8 p-0  hover:text-red-700"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleDeletePreference(service.id, 'service');
                                                                }}
                                                                title="Delete preference"
                                                            >
                                                                <Trash2 className="h-4 w-4 text-[#71717A]" />
                                                            </Button>
                                                            <Button onClick={() => onViewPreference?.(service.id, 'service')} variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                                <ChevronRight className="h-4 w-4 text-[#71717A]" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                    </TabsContent>

                    {/* Category Preferences Tab - FIXED */}
                    <TabsContent value="category" className="mt-0">
                        <div className="flex flex-col gap-y-4 py-4">
                            <div className="flex items-center justify-between ">
                                <h2 className="text-2xl font-semibold">{getTabLabel('category')}</h2>
                                <div className="flex items-center space-x-3">
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                        <Input
                                            placeholder="Search"
                                            className="pl-10 w-64"
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                        />
                                    </div>
                                    <Button
                                        variant="default"
                                        onClick={onAddCategoryPreference}
                                    >
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add Preference
                                    </Button>
                                </div>
                            </div>
                            <div className='rounded-lg border border-[#E4E4E7] p-0.5'>
                                <Table>
                                    <TableHeader>
                                        <TableRow className="bg-gray-50 h-12 text-[#71717A] hover:bg-transparent">
                                            <TableHead className="text-[#71717A]"><Checkbox /></TableHead>
                                            <TableHead className="text-[#71717A]">Category Name</TableHead>
                                            <TableHead className="text-[#71717A]">Preference Types</TableHead>
                                            <TableHead className="text-[#71717A]">No.of Rules</TableHead>
                                            <TableHead className="text-[#71717A] text-center">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {filteredCategoryPreferences.length === 0 ? (
                                            renderEmptyState('category', onAddCategoryPreference)
                                        ) : (
                                            filteredCategoryPreferences.map((category) => (
                                                <TableRow
                                                    key={category.id}
                                                    className="hover:bg-gray-50 h-16 cursor-pointer"
                                                    onClick={() => onViewServiceRules && onViewServiceRules(category as any)}
                                                >
                                                    <TableCell onClick={(e) => e.stopPropagation()}>
                                                        <Checkbox />
                                                    </TableCell>
                                                    <TableCell className="font-medium">{category.categoryName}</TableCell>
                                                    <TableCell>
                                                        <div className="flex space-x-1">
                                                            {category.preferenceTypes.map((type) => (
                                                                <Badge key={type} className={getTypeColor(type)} variant="secondary">
                                                                    {type}
                                                                </Badge>
                                                            ))}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell><Badge variant="secondary">
                                                        {category.numberOfRules}
                                                    </Badge></TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex items-center justify-end space-x-2">
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                className="h-8 w-8 p-0"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    onEditPreference(category.id, 'category');
                                                                }}
                                                                title="Edit preference"
                                                            >
                                                                <Pencil className="h-4 w-4 text-[#71717A]" />
                                                            </Button>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                className="h-8 w-8 p-0  hover:text-red-700"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleDeletePreference(category.id, 'category');
                                                                }}
                                                                title="Delete preference"
                                                            >
                                                                <Trash2 className="h-4 w-4 text-[#71717A]" />
                                                            </Button>

                                                            <Button onClick={() => onViewPreference?.(category.id, 'category')} variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                                <ChevronRight className="h-4 w-4 text-[#71717A]" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))
                                        )}
                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    );
};