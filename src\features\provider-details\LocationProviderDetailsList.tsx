import { useState, type FC } from "react";
import { MapPin } from "lucide-react";
import { useLocations } from "../locations/hooks";
import type { LocationsFilters } from "../locations/types";
import { cn } from "@/lib/utils";
import { Tabs, TabsContent } from "@/components/common/Tabs";
// import { LocationProviderOverviewTab } from "./LocationProviderOverviewTab"; // Commented out for future use
import { ServicesTab } from "../locations/components/ServicesTab";
import { ScheduleSettingsTab } from "../locations/components/ScheduleSettingsTab";
// import { WalkInSettingsTab } from "../locations/components/WalkInSettingsTab"; // Commented out for future use
import { FormsTab } from "./components/FormsTab";
import { PatientsTab } from "./components/PatientsTab";
import { AppointmentsTab } from "./components/AppointmentsTab";
import { TeamMembersTab } from "../locations/components/TeamMemberTabs";
export interface LocationProviderDetailsListProps {
	className?: string;
}

export const LocationProviderDetailsList: FC<
	LocationProviderDetailsListProps
> = ({ className }) => {
	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});

	const { error } = useLocations(filters);

	if (error) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="text-center">
					<MapPin className="mx-auto h-12 w-12 text-gray-400" />
					<h3 className="mt-2 text-sm font-medium text-gray-900">
						Error loading locations
					</h3>
					<p className="mt-1 text-sm text-gray-500">
						{error instanceof Error
							? error.message
							: "Something went wrong"}
					</p>
				</div>
			</div>
		);
	}

	const items = [
		// { value: "overview", label: "Overview" }, // Commented out for future use
		{ value: "appointments", label: "Appointments" },
		{ value: "patients", label: "Patients" },
		{ value: "services", label: "Services" },
		{ value: "forms", label: "Forms" },
		{ value: "team-members", label: "Team Members" },
		{ value: "schedule-settings", label: "Schedule Settings" },
		// { value: "walk-in-settings", label: "Walk-in Settings" }, // Commented out for future use
	];

	return (
		<div className={cn("flex flex-col gap-0.5 p-2", className)}>
			{/* Tabs */}
			<Tabs
				items={items}
				defaultValue="appointments"
				useRouting={true}
				searchParamKey="provider-details-tab"
			>
				{/* Commented out for future use */}
				{/* <TabsContent value="overview">
					<LocationProviderOverviewTab />
				</TabsContent> */}

				<TabsContent value="appointments">
					<AppointmentsTab />
				</TabsContent>
				<TabsContent value="patients">
					<PatientsTab />
				</TabsContent>
				<TabsContent value="services">
					<ServicesTab />
				</TabsContent>
				<TabsContent value="forms">
					<FormsTab />
				</TabsContent>
				<TabsContent value="team-members">
					<TeamMembersTab />
				</TabsContent>
				<TabsContent value="schedule-settings">
					<ScheduleSettingsTab />
				</TabsContent>

				{/* Commented out for future use */}
				{/* <TabsContent value="walk-in-settings">
					<WalkInSettingsTab />
				</TabsContent> */}
			</Tabs>
		</div>
	);
};
