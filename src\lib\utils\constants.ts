import { CONFIGS } from "@/lib/configs";

// API Configuration
export const API_BASE_URL =
	import.meta.env.VITE_API_BASE_URL || "http://localhost:3001/api";

// Application Routes
export const ROUTES = {
	HOME: "/",
	DASHBOARD: "/dashboard",
	CUSTOMERS: "/customers",
	WAITLIST: "/waitlist",
	SCHEDULE: "/schedule",
	ANALYTICS: "/analytics",
	SETTINGS: "/settings",
	PATIENTS: "/patients",
	AUTH: {
		SIGNIN: "/sign-in",
		SIGNUP: "/auth/signup",
		FORGOT_PASSWORD: "/forgot-password",
		RESET_PASSWORD: "/reset-password",
		MFA: "/2fa",
		CALLBACK: "/auth/callback",
	},
} as const;

// Theme Configuration
export const THEME = {
	DEFAULT: "light",
	DARK: "dark",
	LIGHT: "light",
} as const;

// Storage Keys
export const STORAGE_KEYS = {
	AUTH_TOKEN: "migranium_auth_token",
	USER_PREFERENCES: "migranium_user_preferences",
	THEME: "migranium_theme",
} as const;

// Validation Constants
export const VALIDATION = {
	EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
	PHONE_REGEX: /^\+?[\d\s\-()]+$/,
	MIN_PASSWORD_LENGTH: 8,
} as const;

// Pagination
export const PAGINATION = {
	DEFAULT_PAGE_SIZE: 10,
	PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
} as const;

export const WAITLIST_ENVIRONMENT_LINK =
	(CONFIGS.ENVIRONMENT as "dev" | "staging" | "production") === "dev"
		? "https://admin.dev.migranium.com"
		: CONFIGS.ENVIRONMENT === "staging"
			? "https://admin.staging.migranium.com"
			: CONFIGS.ENVIRONMENT === "production"
				? "https://admin.migranium.com"
				: "https://admin.dev.migranium.com";

export const SPACES_ENVIRONMENT_LINK =
	(CONFIGS.ENVIRONMENT as "dev" | "staging" | "production") === "dev"
		? "https://spaces.dev.migranium.com"
		: CONFIGS.ENVIRONMENT === "staging"
			? "https://spaces.staging.migranium.com"
			: CONFIGS.ENVIRONMENT === "production"
				? "https://spaces.migranium.com"
				: "https://spaces.dev.migranium.com";

export const LANDING_ENVIRONMENT_LINK =
	(CONFIGS.ENVIRONMENT as "dev" | "staging" | "production" | "local") ===
	"dev"
		? "https://dev.migranium.com"
		: CONFIGS.ENVIRONMENT === "staging"
			? "https://staging.migranium.com"
			: CONFIGS.ENVIRONMENT === "production"
				? "https://migranium.com"
				: "https://migranium.com";

// Auth Callback URL - Environment-based
export const AUTH_CALLBACK_URL =
	(CONFIGS.ENVIRONMENT as "dev" | "staging" | "production" | "local") === "dev"
		? "https://admin.dev.migranium.com/auth/callback"
		: CONFIGS.ENVIRONMENT === "staging"
			? "https://admin.staging.migranium.com/auth/callback"
			: CONFIGS.ENVIRONMENT === "production"
				? "https://admin.migranium.com/auth/callback"
				: "http://localhost:5173/auth/callback";

// Utility function to generate callback URL with token
export const generateAuthCallbackUrl = (token: string): string => {
	return `${AUTH_CALLBACK_URL}?token=${encodeURIComponent(token)}`;
};
