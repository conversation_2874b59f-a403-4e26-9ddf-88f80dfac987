import { Select, SelectTrigger, SelectValue, SelectContent, SelectGroup, SelectItem } from "@/components/ui/select";

interface ReusableSelectProps {
    placeholder: string;
    options: string[];
}

export default function ReusableSelect({ placeholder, options }: ReusableSelectProps) {
    return (
        <Select>
            <SelectTrigger className="w-full mt-4 py-5">
                <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
                <SelectGroup>
                    {options.map((option) => (
                        <SelectItem key={option} value={option}>{option}</SelectItem>
                    ))}
                </SelectGroup>
            </SelectContent>
        </Select>
    )
}