import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { organizationsApi } from "../api/organizationsApi";
import type {
	Organization,
	UpdateOperatingHoursRequest,
	UpdateScheduleSettingsRequest,
	UpdateScheduleOptimizerRequest,
	BusinessCategory,
} from "../api/organizationsApi";
import { toast } from "sonner";

// Get business categories
export const useBusinessCategories = () => {
	return useQuery({
		queryKey: ["business-categories"],
		queryFn: organizationsApi.getBusinessCategories,
		staleTime: 30 * 60 * 1000, // Cache for 30 minutes since categories rarely change
		cacheTime: 60 * 60 * 1000, // Keep in cache for 1 hour
	});
};

// Get a single organization by id
export const useOrganization = (id: string, enabled = true) => {
	return useQuery({
		queryKey: ["organization", id],
		queryFn: () => organizationsApi.getOrganization(id),
		enabled: enabled && !!id,
		staleTime: 5 * 60 * 1000,
	});
};

// Create organization mutation
export const useCreateOrganization = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: organizationsApi.createOrganization,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
			toast.success("Organization created successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to create organization");
		},
	});
};

// Update organization mutation
export const useUpdateOrganization = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			id,
			data,
		}: {
			id: string;
			data: Partial<Organization>;
		}) => organizationsApi.updateOrganization(id, data),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
			toast.success("Organization updated successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update organization");
		},
	});
};

// Delete organization mutation
export const useDeleteOrganization = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (id: string) => organizationsApi.deleteOrganization(id),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
			toast.success("Organization deleted successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to delete organization");
		},
	});
};

// Update organization theme mutation
export const useUpdateOrganizationTheme = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({ orgId, theme }: { orgId: string; theme: string }) =>
			organizationsApi.updateOrganizationTheme(orgId, theme),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["organizations"] });
			queryClient.invalidateQueries({ queryKey: ["organization"] });
			toast.success("Theme updated successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update theme");
		},
	});
};

// Organization Operating Hours hooks
export const useOrganizationOperatingHours = (
	organizationId: number | null,
	enabled = true
) => {
	return useQuery({
		queryKey: ["organization-operating-hours", organizationId],
		queryFn: () => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}
			return organizationsApi.getOrganizationOperatingHours(
				organizationId
			);
		},
		enabled: enabled && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
};

export const useUpdateOrganizationOperatingHours = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: ({
			organizationId,
			data,
		}: {
			organizationId: number;
			data: UpdateOperatingHoursRequest;
		}) =>
			organizationsApi.updateOrganizationOperatingHours(
				organizationId,
				data
			),
		onSuccess: (_, variables) => {
			// Invalidate the specific organization operating hours query
			queryClient.invalidateQueries({
				queryKey: [
					"organization-operating-hours",
					variables.organizationId,
				],
			});
			toast.success("Organization operating hours updated successfully");
		},
		onError: (error: any) => {
			toast.error(
				error?.message ||
					"Failed to update organization operating hours"
			);
		},
	});
};

// Schedule Settings hooks
export function useOrganizationScheduleSettings(
	organizationId: number,
	enabled = true
) {
	return useQuery({
		queryKey: ["organizationScheduleSettings", organizationId],
		queryFn: () =>
			organizationsApi.getOrganizationScheduleSettings(organizationId),
		enabled: enabled && !!organizationId,
		staleTime: 5 * 60 * 1000,
		refetchOnWindowFocus: false,
	});
}

export function useUpdateOrganizationScheduleSettings() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			organizationId,
			data,
		}: {
			organizationId: number;
			data: UpdateScheduleSettingsRequest;
		}) =>
			organizationsApi.updateOrganizationScheduleSettings(
				organizationId,
				data
			),
		onSuccess: (_, { organizationId }) => {
			// Invalidate and refetch organization schedule settings
			queryClient.invalidateQueries({
				queryKey: ["organizationScheduleSettings", organizationId],
			});
			toast.success(
				"Organization schedule settings updated successfully"
			);
		},
		onError: (error: any) => {
			toast.error(
				error?.message ||
					"Failed to update organization schedule settings"
			);
		},
	});
}

// Schedule Optimizer hooks
export function useOrganizationScheduleOptimizer(
	organizationId: number,
	enabled = true
) {
	return useQuery({
		queryKey: ["organizationScheduleOptimizer", organizationId],
		queryFn: () =>
			organizationsApi.getOrganizationScheduleOptimizer(organizationId),
		enabled: enabled && !!organizationId,
		staleTime: 5 * 60 * 1000,
		refetchOnWindowFocus: false,
	});
}

export function useUpdateOrganizationScheduleOptimizer() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			organizationId,
			data,
		}: {
			organizationId: number;
			data: UpdateScheduleOptimizerRequest;
		}) =>
			organizationsApi.updateOrganizationScheduleOptimizer(
				organizationId,
				data
			),
		onSuccess: (_, { organizationId }) => {
			// Invalidate and refetch organization schedule optimizer
			queryClient.invalidateQueries({
				queryKey: ["organizationScheduleOptimizer", organizationId],
			});
			toast.success(
				"Organization schedule optimizer updated successfully"
			);
		},
		onError: (error: any) => {
			toast.error(
				error?.message ||
					"Failed to update organization schedule optimizer"
			);
		},
	});
}
