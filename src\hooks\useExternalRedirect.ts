import { useSearchParams } from 'react-router';
import { generateAuthCallbackUrl } from '@/lib/utils/constants';

export const useExternalRedirect = () => {
	const [searchParams] = useSearchParams();

	const getRedirectUrl = (token: string, targetDomain?: string): string => {
		const baseUrl = targetDomain || '';
		const callbackUrl = generateAuthCallbackUrl(token);
		
		if (baseUrl) {
			// If target domain is provided, construct the full callback URL
			return `${baseUrl}/auth/callback?token=${encodeURIComponent(token)}`;
		}
		
		return callbackUrl;
	};

	const hasExternalRedirect = (): boolean => {
		return searchParams.has('redirect_to') || searchParams.has('target_domain');
	};

	const getTargetDomain = (): string | null => {
		return searchParams.get('target_domain') || searchParams.get('redirect_to');
	};

	const redirectToExternalApp = (token: string, targetDomain?: string): void => {
		const redirectUrl = getRedirectUrl(token, targetDomain);
		window.location.href = redirectUrl;
	};

	return {
		getRedirectUrl,
		hasExternalRedirect,
		getTargetDomain,
		redirectToExternalApp,
	};
}; 