import { Switch } from "@/components/ui/switch";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import React, { useState, useEffect } from "react";
import { useGetSSOConfig, useEnableSSO, useUpdateSSO, useDeleteSSO } from "@/hooks/useSSOManagement";
import type { SSOConfig, SSOConfigItem } from "@/types/auth";
import { useUIStore } from "@/stores/uiStore";

const SamlSsoSettings: React.FC = () => {
	const [enabledSAMLSSO, setEnableSAMLSSO] = useState(false);
	const [enabledPatientSSO, setEnabledPatientSSO] = useState(false);
	const [entityId, setEntityId] = useState("");
	const [loginUrl, setLoginUrl] = useState("");
	const [logoutUrl, setLogoutUrl] = useState("");
	const [emailField, setEmailField] = useState("");
	const [certificate, setCertificate] = useState("");

	// API hooks
	const { data: ssoConfigData, isLoading: isLoadingConfig, error: configError, refetch: refetchSSO } = useGetSSOConfig();
	const enableSSOConnection = useEnableSSO();
	const updateSSOConnection = useUpdateSSO();
	const deleteSSOConnection = useDeleteSSO();
	const { addToast } = useUIStore();

	// Load existing SSO configuration on component mount
	useEffect(() => {
		if (ssoConfigData?.success && ssoConfigData.data && Array.isArray(ssoConfigData.data)) {
			setEnableSAMLSSO(true);
			const configMap = ssoConfigData.data.reduce((acc, item) => {
				acc[item.key] = item.value;
				return acc;
			}, {} as Record<string, string>);

			setEntityId(configMap['entity_id'] || "");
			setLoginUrl(configMap['login_url'] || "");
			setLogoutUrl(configMap['logout_url'] || "");
			setEmailField("");
			setCertificate("");
		} else if (ssoConfigData?.success && (!ssoConfigData.data || ssoConfigData.data.length === 0)) {
			setEnableSAMLSSO(false);
		}
	}, [ssoConfigData]);

	useEffect(() => {
		if (configError) {
			addToast({
				type: "error",
				title: "Error",
				message: "Failed to load SSO configuration",
			});
		}
	}, [configError, addToast]);

	const handleCancel = () => {
		if (ssoConfigData?.data && Array.isArray(ssoConfigData.data)) {
			const configMap = ssoConfigData.data.reduce((acc, item) => {
				acc[item.key] = item.value;
				return acc;
			}, {} as Record<string, string>);

			setEntityId(configMap['entity_id'] || "");
			setLoginUrl(configMap['login_url'] || "");
			setLogoutUrl(configMap['logout_url'] || "");
			setEmailField("");
			setCertificate("");
		} else {
			setEnabledPatientSSO(false);
			setEntityId("");
			setLoginUrl("");
			setLogoutUrl("");
			setEmailField("");
			setCertificate("");
		}
	};

	const handleToggleSSO = async (enabled: boolean) => {
		if (enabled) {
			setEnableSAMLSSO(true);
		} else {
			try {
				await deleteSSOConnection.mutateAsync();
				setEnableSAMLSSO(false);
				setEnabledPatientSSO(false);
				setEntityId("");
				setLoginUrl("");
				setLogoutUrl("");
				setEmailField("");
				setCertificate("");

				addToast({
					type: "success",
					title: "SSO Disabled",
					message: "SSO has been disabled and configuration deleted for this organization",
				});
			} catch (error) {
				setEnableSAMLSSO(true);
				addToast({
					type: "error",
					title: "Error",
					message: "Failed to disable SSO configuration",
				});
			}
		}
	};

	const handleSetupSSO = async () => {
		if (!entityId || !loginUrl || !logoutUrl || !emailField || !certificate) {
			addToast({
				type: "error",
				title: "Validation Error",
				message: "Please fill in all required fields",
			});
			return;
		}

		const isExistingConfig = ssoConfigData?.data && Array.isArray(ssoConfigData.data) && ssoConfigData.data.length > 0;
		const ssoConfigPayload = {
			entityid: entityId,
			loginurl: loginUrl,
			logouturl: logoutUrl,
			x509cert: certificate,
			email_identification_field: emailField,
		};

		try {
			if (isExistingConfig) {
				await updateSSOConnection.mutateAsync(ssoConfigPayload);
				addToast({
					type: "success",
					title: "Success",
					message: "SSO configuration updated successfully",
				});
			} else {
				await enableSSOConnection.mutateAsync(ssoConfigPayload);
				addToast({
					type: "success",
					title: "Success",
					message: "SSO configuration enabled successfully",
				});
			}
			refetchSSO();
		} catch (error) {
			addToast({
				type: "error",
				title: "Error",
				message: isExistingConfig ? "Failed to update SSO configuration" : "Failed to enable SSO configuration",
			});
		}
	};

	if (isLoadingConfig) {
		return (
			<div>
				<h2 className="mb-4 text-xl font-semibold">SAML single sign-on (SSO)</h2>
				<div className="flex items-center justify-center py-8">
					<Loader2 className="h-8 w-8 animate-spin" />
					<span className="ml-2">Loading SSO configuration...</span>
				</div>
			</div>
		);
	}

	return (
		<div>
			<h2 className="mb-4 text-xl font-semibold">SAML single sign-on (SSO)</h2>

			{/* Enable SAML SSO */}
			<div className="mb-4 flex items-center justify-between gap-2">
				<span className="font-medium">Enable SAML SSO</span>
				<div className="flex items-center">
					<Switch
						checked={enabledSAMLSSO}
						onCheckedChange={handleToggleSSO}
						className="ml-2"
					/>
					<span className="ml-2 font-semibold text-gray-500">
						{enabledSAMLSSO ? "On" : "Off"}
					</span>
				</div>
			</div>
			<p className="text-sm text-gray-600 mb-6">
				Workspace members can log in with SAML SSO if their email address uses a verified domain.
			</p>

			{enabledSAMLSSO && (
				<>
					{/* SSO Configuration URLs */}
					{ssoConfigData?.data && Array.isArray(ssoConfigData.data) && (
						<div className="mb-6">
							<div className="flex items-center justify-between mb-4">
								<h3 className="font-medium">SSO Configuration URLs</h3>
								<Button
									variant="outline"
									size="sm"
									onClick={() => refetchSSO()}
									disabled={isLoadingConfig}
								>
									Refresh
								</Button>
							</div>
							<p className="text-sm text-gray-600 mb-4">
								Use these URLs to configure your identity provider:
							</p>
							<div className="space-y-3">
								{ssoConfigData.data.map((item) => (
									<div key={item.key}>
										<Label className="text-sm font-medium">{item.label}</Label>
										<Input value={item.value} readOnly className="mt-1 bg-gray-50" />
									</div>
								))}
							</div>
						</div>
					)}

					{/* Enable Patient SSO */}
					<div className="mb-6">
						<div className="mb-4 flex items-center justify-between gap-2">
							<span className="font-medium">Enable Patient SSO</span>
							<div className="flex items-center">
								<Switch
									checked={enabledPatientSSO}
									onCheckedChange={setEnabledPatientSSO}
									className="ml-2"
								/>
								<span className="ml-2 font-semibold text-gray-500">
									{enabledPatientSSO ? "On" : "Off"}
								</span>
							</div>
						</div>
						<p className="text-sm text-gray-600">
							If enabled, patients can sign in using this organization's SSO.
						</p>
					</div>

					{/* SSO Attribute Mapping */}
					<div>
						<h3 className="font-medium mb-4">SSO Attribute Mapping</h3>
						<p className="text-sm text-gray-600 mb-6">
							Fill out the fields to setup SSO(SAML single sign-on)
						</p>

						<div className="space-y-4">
							<div>
								<Label htmlFor="entityId" className="text-sm font-medium">Entity ID</Label>
								<Input
									id="entityId"
									value={entityId}
									onChange={(e) => setEntityId(e.target.value)}
									className="mt-1"
									placeholder="Enter Entity ID"
								/>
							</div>

							<div>
								<Label htmlFor="loginUrl" className="text-sm font-medium">Login URL</Label>
								<Input
									id="loginUrl"
									value={loginUrl}
									onChange={(e) => setLoginUrl(e.target.value)}
									className="mt-1"
									placeholder="Enter Login URL"
								/>
							</div>

							<div>
								<Label htmlFor="logoutUrl" className="text-sm font-medium">Logout URL</Label>
								<Input
									id="logoutUrl"
									value={logoutUrl}
									onChange={(e) => setLogoutUrl(e.target.value)}
									className="mt-1"
									placeholder="Enter Logout URL"
								/>
							</div>

							<div>
								<Label htmlFor="emailField" className="text-sm font-medium">Email Identification Field</Label>
								<Input
									id="emailField"
									value={emailField}
									onChange={(e) => setEmailField(e.target.value)}
									className="mt-1"
									placeholder="Enter Email Identification Field"
								/>
							</div>

							<div>
								<Label htmlFor="certificate" className="text-sm font-medium">X.509 Certificate</Label>
								<Textarea
									id="certificate"
									value={certificate}
									onChange={(e) => setCertificate(e.target.value)}
									className="mt-1 min-h-[100px]"
									placeholder="Enter X.509 Certificate"
								/>
							</div>
						</div>

						<div className="flex justify-end gap-3 mt-8">
							<Button
								variant="outline"
								onClick={handleCancel}
								className="px-6"
								disabled={enableSSOConnection.isPending || updateSSOConnection.isPending}
							>
								Cancel
							</Button>
							<Button
								onClick={handleSetupSSO}
								className="migranium-button px-6"
								disabled={enableSSOConnection.isPending || updateSSOConnection.isPending}
							>
								{(enableSSOConnection.isPending || updateSSOConnection.isPending) ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										{(ssoConfigData?.data && Array.isArray(ssoConfigData.data) && ssoConfigData.data.length > 0) ? "Updating..." : "Enabling..."}
									</>
								) : (
									(ssoConfigData?.data && Array.isArray(ssoConfigData.data) && ssoConfigData.data.length > 0) ? "Update SSO" : "Setup SSO"
								)}
							</Button>
						</div>
					</div>
				</>
			)}
		</div>
	);
};

export default SamlSsoSettings;
