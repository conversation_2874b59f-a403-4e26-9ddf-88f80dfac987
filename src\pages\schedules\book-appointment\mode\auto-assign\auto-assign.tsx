import { Label } from "@/components/ui/label";
import { RefactorMultiSelect } from "@/pages/schedules/components/custom-select";

import { useLocationServices } from "@/features/locations/hooks/useServices";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { getFormattedHeader } from "@/pages/schedules/utils";
import { FaAngleRight, FaAngleLeft, FaCalendar, FaClock } from "react-icons/fa6";
import { FiUser } from "react-icons/fi";
import { GoCheckCircleFill } from "react-icons/go";
import { MdOutlineVideocam } from "react-icons/md";
import { PiSpeakerHighThin } from "react-icons/pi";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
} from "@/components/ui/pagination"
import { IoCalendar } from "react-icons/io5";
import { RefreshCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import clsx from "clsx";
import calendarImage from "@/pages/schedules/assets/calendar.png"
import { ToggleButton } from "@/components/common/ToggleButton";

export default function AutoAssign({
    back,
    next,
    locationId
}: { back: () => void; next: () => void; locationId?: string }) {
    const { organizationId } = useOrganizationContext();
    const [timeFrameSelected, setTimeFrameSelected] = useState<number | null>(null);
    const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
    const [selectedServices, setSelectedServices] = useState<string[]>([]);

    // Fetch services for the current location
    const { data: servicesResponse, isLoading: isLoadingServices } = useLocationServices({
        locationId,
        organizationId: organizationId || undefined,
        enabled: !!locationId && !!organizationId
    });

    // Get full services data and create dropdown options
    const servicesData = servicesResponse?.data || [];
    const services = servicesData.map(service => service.name);

    // Find selected service object
    const selectedServiceName = selectedServices[0] || "";
    const selectedService = servicesData.find(service => service.name === selectedServiceName);

    // Get available appointment methods from selected service
    const availableMethodNames = selectedService?.appointment_methods?.map(method => method.name.toLowerCase().replace(" ", "-")) || [];

    // Get stations for the current location from the selected service
    const currentLocationSelection = selectedService?.location_selections?.find(
        selection => selection.location_id.toString() === locationId
    );
    const locationStations = currentLocationSelection?.stations || [];
    const stationNames = locationStations.map(station => station.name);

    // Use stations as providers (no fallback to hardcoded providers)
    const availableProviders = stationNames;
    // Reset selected types when service changes and default to in-person if available
    useEffect(() => {
        if (selectedService) {
            const hasInPerson = availableMethodNames.includes("in-person");
            setSelectedTypes(hasInPerson ? ["in-person"] : []);
        } else {
            setSelectedTypes([]);
        }
    }, [selectedServiceName, availableMethodNames.join(',')]);

    const toggleSelection = (type: string) => {
        // Only allow single selection for appointment methods
        setSelectedTypes([type]);
    };

    // Check if appointment method is available for selected service
    const isMethodAvailable = (methodId: string) => {
        if (!selectedService) return false; // Disable all if no service selected
        return availableMethodNames.includes(methodId);
    };

    const meetingTypes = [
        { id: "in-person", label: "In-Person", icon: FiUser },
        { id: "audio", label: "Audio", icon: PiSpeakerHighThin },
        { id: "video", label: "Video", icon: MdOutlineVideocam },
    ];

    return (
        <div className="mx-5 mt-5">
            <div className="space-y-6">

                <div className="w-full flex flex-col gap-2">
                    <Label htmlFor="services" className="text-[#18181B] text-sm font-normal">Select Service</Label>

                    <RefactorMultiSelect
                        value={selectedServices}
                        setValue={(value) => setSelectedServices(Array.isArray(value) ? [value[0]].filter(Boolean) : value ? [value] : [])}
                        placeholder={isLoadingServices ? "Loading services..." : "Select a service"}
                        label="Services"
                        options={services}
                    />
                </div>

                <div className="flex flex-col gap-3">
                    <Label htmlFor="appointmentType" className="text-[#18181B] text-sm font-normal">Appointment Method</Label>
                    <div className="flex flex-wrap gap-2">
                        {meetingTypes.map((type) => (
                            <ToggleButton
                                key={type.id}
                                label={type.label}
                                icon={type.icon as any}
                                isSelected={selectedTypes.includes(type.id)}
                                onClick={() => toggleSelection(type.id)}
                                disabled={!isMethodAvailable(type.id)}
                                className="py-5"
                            />
                        ))}
                    </div>
                </div>

            </div>

            <div className="mt-16 mb-5">
                <div className="grid grid-cols-2 gap-3">

                    <div className="space-y-4">
                        <Label htmlFor="date_preferred_time" className="text-[#323539] text-sm font-normal border-b border-[#E4E4E7] pb-5">Selected Date And Preferred Time Slot</Label>

                        <div className="flex items-center justify-between mb-3">

                            <FaAngleLeft />
                            <div className="flex-1 flex items-center justify-center gap-x-1 text-center tracking-wider">
                                <h1 className="text-base font-medium">
                                    {getFormattedHeader(new Date(), "day")}
                                </h1>
                                <span
                                    className="bg-[#F4F4F5] ml-1 p-2 rounded-md"
                                >
                                    <IoCalendar />
                                </span>
                            </div>
                            <FaAngleRight />

                        </div>

                        <div>
                            <div className="border border-[#E5E5E7] text-center py-3 rounded-lg">
                                <h1 className="text-base">Monarch Corps</h1>

                                <div className="grid grid-cols-2 gap-x-2 gap-y-3 mt-4 px-4">

                                    {Array.from({ length: 10 }).map((_, index) => (
                                        <button
                                            key={index}
                                            tabIndex={0}
                                            className={clsx("text-nowrap cursor-pointer py-3 px-2.5 border border-[#E4E4E7] rounded-lg text-xs font-normal transition-all duration-300", timeFrameSelected === index ? "bg-[#005893] text-[#EEF8FE]" : " bg-[#FAFAFA] text-[#27272A]")}
                                            onClick={() => {
                                                if (timeFrameSelected == index) { // the user is clicking on the same frame so set it to null
                                                    return setTimeFrameSelected(null);
                                                }
                                                setTimeFrameSelected(index)
                                            }}
                                        >
                                            <span>09:15 AM - 9:30 AM</span>
                                        </button>
                                    ))}

                                </div>

                                <div className="mt-6">
                                    <Pagination>
                                        <PaginationContent>
                                            <PaginationItem>
                                                <FaAngleLeft />
                                            </PaginationItem>
                                            <PaginationItem>
                                                <PaginationLink href="#">1</PaginationLink>
                                            </PaginationItem>
                                            <PaginationItem className="bg-white border rounded-lg">
                                                <PaginationLink href="#">2</PaginationLink>
                                            </PaginationItem>
                                            <PaginationItem>
                                                <PaginationEllipsis />
                                            </PaginationItem>
                                            <PaginationItem>
                                                <PaginationLink href="#">3</PaginationLink>
                                            </PaginationItem>
                                            <PaginationItem>
                                                <FaAngleRight />
                                            </PaginationItem>
                                        </PaginationContent>
                                    </Pagination>
                                </div>

                            </div>
                        </div>

                    </div>

                    {timeFrameSelected !== null ? (
                        <div className="space-y-4">
                            <Label htmlFor="appointmentDate" className="text-[#323539] text-sm font-normal border-b border-[#E4E4E7] pb-5">Providers</Label>

                            <div className="space-y-2 max-h-[24rem] overflow-scroll scrollbar-hide">
                                {availableProviders.length === 0 ? (
                                    <div className="text-center py-8 text-[#71717A]">
                                        <p>No providers available for this service at the current location.</p>
                                        <p className="text-sm mt-1">Please select a different service.</p>
                                    </div>
                                ) : (
                                    availableProviders.map((provider, index) => (
                                        <div
                                            key={provider}
                                            className="w-full border border-[#E4E4E7] py-2 px-3 rounded-[10px] flex items-start gap-x-1"
                                        >
                                            <div className="shrink-0 size-12 bg-[#E4E4E7] text-[#A1A1AA] rounded-full grid place-content-center">
                                                {provider.substring(0, 2).toUpperCase()}
                                            </div>

                                            <div className="space-y-1.5">
                                                <h1 className="font-medium text-base">{provider}</h1>
                                                <div className="flex gap-x-2">
                                                    <p className="text-nowrap text-xs text-[#27272A] opacity-60 flex items-center gap-x-1">
                                                        <FaCalendar />
                                                        12 Apr 2025
                                                    </p>
                                                    <p className="text-nowrap text-xs text-[#27272A] opacity-60 flex items-center gap-x-1">
                                                        <FaClock />
                                                        09:15 AM - 9:30 AM
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    ))
                                )}
                            </div>

                            <div className="flex justify-center">
                                <Button variant="outline" className="bg-[#F4F4F5] text-[#A1A1AA] py-5 cursor-pointer">
                                    <RefreshCcw />
                                    Refresh List
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <div className="grid grid-flow-row place-content-center">
                            <img
                                src={calendarImage}
                                alt="Calendar"
                                className="scale-70"
                            />
                            <h1 className="text-base">Select a Time Slot to View Providers</h1>
                        </div>
                    )}
                </div>
            </div>


            <div className="flex items-center justify-end gap-x-3">
                <Button
                    type="button"
                    variant="outline"
                    onClick={back}
                    className="bg-[#F4F4F5] cursor-pointer"
                >
                    Back
                </Button>
                <Button
                    type="button"
                    className="cursor-pointer"
                    onClick={next}
                >
                    Next
                </Button>
            </div>

        </div>
    )
}