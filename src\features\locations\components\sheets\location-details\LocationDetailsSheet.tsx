import React, { useState } from "react";
import {
	Map<PERSON>in,
	Users,
	Hospital,
	Trash2,
	WandSparkles,
	Check,
	Power,
	Edit,
	Copy,
	Send,
	Download,
	Settings,
	X,
} from "lucide-react";
import {
	<PERSON>et,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	She<PERSON><PERSON>ooter,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { StarRating } from "@/components/common/StarRating";
import { QRCode } from "@/components/common/QRCode";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useLocation } from "@/features/locations/hooks/useLocation";
import type { Location } from "@/features/locations/types";
import { Skeleton } from "@/components/ui/skeleton";

interface LocationDetailsSheetProps {
	open: boolean;
	onClose: () => void;
	locationId?: string;
	onSendBookingLink?: () => void;
}

export const LocationDetailsSheet: React.FC<LocationDetailsSheetProps> = ({
	open,
	onClose,
	locationId,
	onSendBookingLink,
}) => {
	const [activeTab, setActiveTab] = useState("schedule");
	const [copiedText, setCopiedText] = useState(false);

	const { organizationId } = useOrganizationContext();
	const { data: locationData, isLoading } = useLocation(
		locationId || "",
		!!locationId
	);

	// Use location data when available, otherwise show loading or empty state
	const orgData = locationData
		? {
			id: locationData.id,
			name: locationData.name,
			address: locationData.address,
			city: locationData.city,
			state: locationData.state,
			country: locationData.country,
			rating: 4, // TODO: Get rating from API
			locationsCount: 1, // This is a single location
			providersCount: locationData.stations?.length || 0,
			averageWaitTime: "2 hr 30 mins", // TODO: Get from API if available
			logo: "/api/placeholder/85/85", // TODO: Get from organization/location
			description:
				locationData.description || "No description available",
			servicesCount: locationData.service_count || 0,
			websiteUrl: `https://app.migranium.com/schedule/${locationData.name.toLowerCase().replace(/\s+/g, "-")}-${locationData.id}`,
			isActive: locationData.isActive,
			hasVisibility: true, // TODO: Get from location settings
			hasQRCode: true, // TODO: Get from location settings
			hasWebLink: false, // TODO: Get from location settings
		}
		: null;

	const handleCopy = async (text: string) => {
		try {
			await navigator.clipboard.writeText(text);
			setCopiedText(true);
			setTimeout(() => setCopiedText(false), 2000);
		} catch (err) {
			console.error("Failed to copy text: ", err);
		}
	};

	const handleEdit = () => {
		console.log("Edit location:", orgData?.name);
	};

	const handleSend = () => {
		onClose();
		onSendBookingLink?.();
	};

	const handleDownload = () => {
		console.log("Download QR code");
	};

	const handleLocationSettings = () => {
		console.log("Open location settings");
	};

	// Show loading state
	if (isLoading) {
		return (
			<Sheet open={open} onOpenChange={onClose}>
				<SheetContent className="z-[1003] w-full !max-w-[525px] overflow-y-auto [&>button]:hidden">
					{/* Header Skeleton */}
					<div className="flex items-center justify-between space-y-0 px-6 pb-0">
						<Skeleton className="h-6 w-32" />
						<div className="flex items-center gap-2">
							<Skeleton className="h-10 w-16" />
							<Skeleton className="h-10 w-10" />
						</div>
					</div>

					{/* Content Skeletons */}
					<div className="flex flex-1 flex-col gap-6 px-6">
						{/* Location Info Card Skeleton */}
						<div className="rounded-lg bg-white p-5 shadow-[0px_4px_20px_0px_rgba(173,186,195,0.16)]">
							<div className="flex gap-3">
								{/* Avatar Skeleton */}
								<Skeleton className="h-[85px] w-[85px] rounded-full" />

								{/* Details Skeleton */}
								<div className="flex flex-1 flex-col justify-center space-y-2">
									<Skeleton className="h-6 w-64" />
									<Skeleton className="h-4 w-48" />
									<Skeleton className="h-4 w-24" />
								</div>
							</div>

							{/* Description Skeleton */}
							<div className="mt-5 space-y-2">
								<Skeleton className="h-3 w-full" />
								<Skeleton className="h-3 w-3/4" />
							</div>

							{/* Stats Skeleton */}
							<div className="mt-5 flex gap-3">
								<Skeleton className="h-8 w-20" />
								<Skeleton className="h-8 w-20" />
							</div>
						</div>

						{/* QR Code Section Skeleton */}
						<div className="rounded-lg bg-white p-5 shadow-[0px_4px_20px_0px_rgba(173,186,195,0.25)]">
							<div className="space-y-5">
								{/* Tabs and Settings Skeleton */}
								<div className="flex items-center justify-between">
									<Skeleton className="h-10 w-32" />
									<Skeleton className="h-10 w-10" />
								</div>

								{/* Status Badges Skeleton */}
								<div className="flex gap-2 px-4">
									<Skeleton className="h-6 w-16" />
									<Skeleton className="h-6 w-20" />
									<Skeleton className="h-6 w-18" />
									<Skeleton className="h-6 w-20" />
								</div>

								{/* QR Code Skeleton */}
								<div className="flex justify-center">
									<Skeleton className="h-32 w-32" />
								</div>

								{/* Instructions Skeleton */}
								<Skeleton className="mx-auto h-4 w-64" />

								{/* URL Skeleton */}
								<Skeleton className="h-12 w-full rounded-lg" />

								{/* Action Buttons Skeleton */}
								<div className="flex justify-end gap-2">
									<Skeleton className="h-10 w-10" />
									<Skeleton className="h-10 w-10" />
									<Skeleton className="h-10 w-10" />
									<Skeleton className="h-10 w-10" />
								</div>
							</div>
						</div>
					</div>

					{/* Footer Skeleton */}
					<div className="mt-6 flex justify-between gap-3 px-6">
						<Skeleton className="h-10 w-10" />
						<div className="flex gap-3">
							<Skeleton className="h-10 w-20" />
							<Skeleton className="h-10 w-32" />
						</div>
					</div>
				</SheetContent>
			</Sheet>
		);
	}

	// Show error state if no location data
	if (!orgData) {
		return (
			<Sheet open={open} onOpenChange={onClose}>
				<SheetContent className="z-[1003] w-full !max-w-[525px] overflow-y-auto [&>button]:hidden">
					<div className="flex h-full items-center justify-center">
						<div className="text-center">
							<Hospital className="mx-auto mb-4 h-12 w-12 text-gray-400" />
							<p className="text-gray-500">Location not found</p>
							<Button onClick={onClose} className="mt-4">
								Close
							</Button>
						</div>
					</div>
				</SheetContent>
			</Sheet>
		);
	}

	return (
		<Sheet open={open} onOpenChange={onClose}>
			<SheetContent className="z-[1003] w-full !max-w-[525px] overflow-y-auto [&>button]:hidden">
				{/* Header */}
				<SheetHeader className="flex-row items-center justify-between space-y-0 px-6 pb-0">
					<SheetTitle className="text-lg font-semibold text-zinc-800">
						Location details
					</SheetTitle>
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							size="sm"
							className="h-10 gap-2"
							onClick={handleEdit}
						>
							<Edit className="h-4 w-4" />
							Edit
						</Button>
						<Button
							variant="ghost"
							size="sm"
							className="h-10 w-10 p-0"
							onClick={onClose}
						>
							<X className="h-4 w-4" />
						</Button>
					</div>
				</SheetHeader>

				{/* Content */}
				<div className="flex flex-1 flex-col gap-6 px-6">
					{/* Organization Info Card */}
					<div className="rounded-lg bg-white p-5 shadow-[0px_4px_20px_0px_rgba(173,186,195,0.16)]">
						<div className="flex gap-3">
							{/* Avatar */}
							<div className="flex-shrink-0">
								<div className="h-[85px] w-[85px] overflow-hidden rounded-full bg-gray-100">
									{orgData.logo ? (
										<img
											src={orgData.logo}
											alt={`${orgData.name} logo`}
											className="h-full w-full object-cover"
										/>
									) : (
										<div className="flex h-full w-full items-center justify-center bg-gray-200">
											<Hospital className="h-8 w-8 text-gray-400" />
										</div>
									)}
								</div>
							</div>

							{/* Organization Details */}
							<div className="flex flex-1 flex-col items-start justify-center space-y-1.5">
								<div className="space-y-2">
									<div className="flex items-center gap-2">
										<h3 className="text-foreground leading-5 font-bold">
											{orgData.name}
										</h3>
									</div>
									<div className="flex items-center gap-1 text-[15px] text-[#6d748d]">
										<MapPin className="h-4 w-4 flex-shrink-0" />
										<span>
											{[
												orgData.address,
												orgData.city,
												orgData.state,
												orgData.country
											]
												.filter(Boolean)
												.join(', ') || 'No address provided'}
										</span>
									</div>
									<StarRating rating={orgData.rating} />
								</div>
							</div>
						</div>

						{/* Description */}
						<p className="mt-5 line-clamp-2 text-[12px] leading-5 font-medium text-[#3b5566]">
							{orgData.description}
						</p>

						{/* Stats */}
						<div className="mt-5 flex gap-3">
							<div className="flex items-center gap-2 rounded-lg bg-white px-2 py-1">
								<Users className="h-3.5 w-3.5" />
								<span className="text-[13px] font-normal text-[#6d748d]">
									Providers
								</span>
								<span className="text-[13px] font-semibold text-[#3b5566]">
									{orgData.providersCount
										.toString()
										.padStart(2, "0")}
								</span>
							</div>
							<div className="flex items-center gap-2 rounded-lg bg-white px-2 py-1">
								<WandSparkles className="h-3.5 w-3.5" />
								<span className="text-[13px] font-normal text-[#6d748d]">
									Services
								</span>
								<span className="text-[13px] font-semibold text-[#3b5566]">
									{orgData.servicesCount
										?.toString()
										.padStart(3, "0") || "000"}
								</span>
							</div>
						</div>
					</div>

					{/* Tabs and QR Code Section */}
					<div className="rounded-lg bg-white p-5 shadow-[0px_4px_20px_0px_rgba(173,186,195,0.25)]">
						<div className="space-y-5">
							{/* Tabs Header with Settings */}
							<div className="flex items-center justify-between">
								<Tabs
									value={activeTab}
									onValueChange={setActiveTab}
								>
									<TabsList className="bg-zinc-100">
										<TabsTrigger
											value="schedule"
											className="cursor-pointer text-sm font-medium"
										>
											Schedule
										</TabsTrigger>
										<TabsTrigger
											value="waitlist"
											className="cursor-pointer text-sm font-medium"
										>
											Waitlist
										</TabsTrigger>
									</TabsList>
								</Tabs>
								<DropdownMenu>
									<DropdownMenuTrigger asChild>
										<Button
											variant="outline"
											size="sm"
											className="bg-foreground-muted hover:bg-foreground-muted/50 h-10 w-10 cursor-pointer border-none p-0"
										>
											<Settings className="h-4 w-4" />
										</Button>
									</DropdownMenuTrigger>
									<DropdownMenuContent
										align="end"
										className="z-[1004] w-80 rounded-lg border border-zinc-200 bg-white p-5 shadow-lg"
									>
										<DropdownMenuLabel className="mb-3 px-0 text-sm font-medium text-zinc-900">
											Location configuration
										</DropdownMenuLabel>
										<div className="space-y-4">
											{/* Active Toggle */}
											<div className="flex items-center justify-between py-2">
												<div className="flex items-center gap-2">
													<span className="text-sm text-zinc-900">
														Active
													</span>
													<div className="flex h-5 w-5 items-center justify-center rounded">
														<svg
															width="12"
															height="12"
															viewBox="0 0 12 12"
															fill="none"
															className="text-zinc-400"
														>
															<path
																d="M6 0C2.688 0 0 2.688 0 6s2.688 6 6 6 6-2.688 6-6-2.688-6-6-6zm.75 9h-1.5v-1.5h1.5V9zm0-3h-1.5V3h1.5v3z"
																fill="currentColor"
															/>
														</svg>
													</div>
												</div>
												<Switch
													checked={
														orgData.isActive ?? true
													}
													onCheckedChange={(
														checked
													) => {
														// Handle active state change
														console.log(
															"Active toggled:",
															checked
														);
													}}
												/>
											</div>

											{/* Location Visibility Toggle */}
											<div className="flex items-center justify-between py-2">
												<div className="flex items-center gap-2">
													<span className="text-sm text-zinc-900">
														Location Visibility
													</span>
													<div className="flex h-5 w-5 items-center justify-center rounded">
														<svg
															width="12"
															height="12"
															viewBox="0 0 12 12"
															fill="none"
															className="text-zinc-400"
														>
															<path
																d="M6 0C2.688 0 0 2.688 0 6s2.688 6 6 6 6-2.688 6-6-2.688-6-6-6zm.75 9h-1.5v-1.5h1.5V9zm0-3h-1.5V3h1.5v3z"
																fill="currentColor"
															/>
														</svg>
													</div>
												</div>
												<Switch
													checked={
														orgData.hasVisibility ??
														true
													}
													onCheckedChange={(
														checked
													) => {
														// Handle visibility state change
														console.log(
															"Visibility toggled:",
															checked
														);
													}}
												/>
											</div>

											{/* Join Via QR Code Toggle */}
											<div className="flex items-center justify-between py-2">
												<div className="flex items-center gap-2">
													<span className="text-sm text-zinc-900">
														Join Via QR Code
													</span>
													<div className="flex h-5 w-5 items-center justify-center rounded">
														<svg
															width="12"
															height="12"
															viewBox="0 0 12 12"
															fill="none"
															className="text-zinc-400"
														>
															<path
																d="M6 0C2.688 0 0 2.688 0 6s2.688 6 6 6 6-2.688 6-6-2.688-6-6-6zm.75 9h-1.5v-1.5h1.5V9zm0-3h-1.5V3h1.5v3z"
																fill="currentColor"
															/>
														</svg>
													</div>
												</div>
												<Switch
													checked={
														orgData.hasQRCode ??
														false
													}
													onCheckedChange={(
														checked
													) => {
														// Handle QR code state change
														console.log(
															"QR Code toggled:",
															checked
														);
													}}
												/>
											</div>

											{/* Join Via Web Link Toggle */}
											<div className="flex items-center justify-between py-2">
												<div className="flex items-center gap-2">
													<span className="text-sm text-zinc-900">
														Join Via Web Link
													</span>
													<div className="flex h-5 w-5 items-center justify-center rounded">
														<svg
															width="12"
															height="12"
															viewBox="0 0 12 12"
															fill="none"
															className="text-zinc-400"
														>
															<path
																d="M6 0C2.688 0 0 2.688 0 6s2.688 6 6 6 6-2.688 6-6-2.688-6-6-6zm.75 9h-1.5v-1.5h1.5V9zm0-3h-1.5V3h1.5v3z"
																fill="currentColor"
															/>
														</svg>
													</div>
												</div>
												<Switch
													checked={
														orgData.hasWebLink ??
														false
													}
													onCheckedChange={(
														checked
													) => {
														// Handle web link state change
														console.log(
															"Web Link toggled:",
															checked
														);
													}}
												/>
											</div>
										</div>

										<DropdownMenuSeparator className="my-4" />

										{/* Footer Buttons */}
										<div className="flex items-center justify-between">
											<span className="text-sm text-zinc-400 opacity-0">
												Reset
											</span>
											<div className="flex gap-3">
												<Button
													variant="outline"
													size="sm"
													className="h-10 w-20 text-sm font-medium"
												>
													Cancel
												</Button>
												<Button
													size="sm"
													className="h-10 w-20 bg-[#005893] text-sm font-medium hover:bg-[#005893]/90"
												>
													Apply
												</Button>
											</div>
										</div>
									</DropdownMenuContent>
								</DropdownMenu>
							</div>

							{/* Status Badges */}
							<div className="flex flex-wrap gap-1 px-4">
								<Badge
									variant="outline"
									className={`border-transparent ${orgData.isActive
										? "bg-[#c3efce] text-[#0a2914]"
										: "bg-gray-100 text-gray-500"
										}`}
								>
									<Check className="h-3.5 w-3.5" />
									Active
								</Badge>
								<Badge
									variant="outline"
									className={`border-transparent ${orgData.hasVisibility
										? "bg-[#c3efce] text-[#0a2914]"
										: "bg-foreground-muted text-zinc-800"
										}`}
								>
									{orgData.hasVisibility ? (
										<Check className="h-3.5 w-3.5" />
									) : (
										<Power className="h-3.5 w-3.5" />
									)}
									Visibility
								</Badge>
								<Badge
									variant="outline"
									className={`border-transparent ${orgData.hasQRCode
										? "bg-[#c3efce] text-[#0a2914]"
										: "bg-foreground-muted text-zinc-800"
										}`}
								>
									{orgData.hasQRCode ? (
										<Check className="h-3.5 w-3.5" />
									) : (
										<Power className="h-3.5 w-3.5" />
									)}
									QR Code
								</Badge>
								<Badge
									variant="outline"
									className={`border-transparent ${orgData.hasWebLink
										? "bg-[#c3efce] text-[#0a2914]"
										: "bg-foreground-muted text-zinc-800"
										}`}
								>
									{orgData.hasWebLink ? (
										<Check className="h-3.5 w-3.5" />
									) : (
										<Power className="h-3.5 w-3.5" />
									)}
									Web Link
								</Badge>
							</div>

							{/* QR Code */}
							<div className="flex justify-center">
								<QRCode value={orgData.websiteUrl || ""} />
							</div>

							{/* Instructions */}
							<p className="text-center text-[13px] font-medium text-[#a5a5ac]">
								Use Link or QR Code to Access Location Website
							</p>

							{/* URL Display */}
							<div className="rounded-lg bg-[#f7f7f7] p-3">
								<p className="text-[13px] break-all text-zinc-900">
									<span className="text-[#a3abb6]">
										https://app.migranium.com/schedule
									</span>
									/
									{orgData.name
										.toLowerCase()
										.replace(/\s+/g, "-")}
									-{orgData.id}
								</p>
							</div>

							{/* Action Buttons */}
							<div className="flex justify-end gap-2">
								<Button
									variant="outline"
									size="sm"
									className="bg-foreground-muted hover:bg-foreground-muted/50 h-10 w-10 cursor-pointer border-transparent p-0"
									onClick={handleEdit}
								>
									<Edit className="h-4 w-4" />
								</Button>
								<Button
									variant="outline"
									size="sm"
									className="bg-foreground-muted hover:bg-foreground-muted/50 h-10 w-10 cursor-pointer border-transparent p-0"
									onClick={() =>
										handleCopy(orgData.websiteUrl || "")
									}
								>
									<Copy className="h-4 w-4" />
								</Button>
								<Button
									variant="outline"
									size="sm"
									className="bg-foreground-muted hover:bg-foreground-muted/50 h-10 w-10 cursor-pointer border-transparent p-0"
									onClick={handleSend}
								>
									<Send className="h-4 w-4" />
								</Button>
								<Button
									variant="outline"
									size="sm"
									className="bg-foreground-muted hover:bg-foreground-muted/50 h-10 w-10 cursor-pointer border-transparent p-0"
									onClick={handleDownload}
								>
									<Download className="h-4 w-4" />
								</Button>
							</div>
						</div>
					</div>
				</div>

				{/* Footer */}
				<SheetFooter className="mt-6 flex-row justify-between gap-3">
					<Button
						variant="outline"
						size="sm"
						className="bg-foreground-muted h-10 w-10 cursor-pointer border-none p-0 text-base hover:bg-red-50 hover:text-red-600"
						onClick={() => { }}
					>
						<Trash2 className="h-4 w-4" />
					</Button>
					<div className="flex flex-row items-center gap-3">
						<Button
							className="bg-foreground-muted hover:bg-foreground-muted/50 text-secondary-foreground cursor-pointer"
							onClick={onClose}
						>
							Close
						</Button>
						<Button
							className="bg-primary hover:bg-primary/90 cursor-pointer"
							onClick={handleLocationSettings}
						>
							Location Details
						</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
};

export default LocationDetailsSheet;
