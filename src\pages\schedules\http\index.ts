import { apiClient } from "@/lib/api/clients";
import * as Types from "../types";

/***************************** Appointments *****************************/

/**
 * List appointments
 * @param params - Query parameters
 * @returns List of appointments
 */

export async function APIVersion3UseListAppointments(
    params: Types.AppointmentTypes.ListAppointmentsQueryParams
): Promise<Types.AppointmentTypes.ListAppointmentsResponse> {
    const response = await apiClient.get(`/api/appointments/`, { params });
    return response.data;
}

/**
 * Create appointment
 * @param data - Appointment data
 * @returns Appointment created response
 */

export async function APIVersion3UseCreateAppointment(
    data: Types.AppointmentTypes.ActionAppointmentParams
): Promise<Types.AppointmentTypes.CreateAppointmentResponse> {
    const response = await apiClient.post(`/api/appointments/`, { data });
    return response.data;
}

/**
 * Get appointment details
 * @param id - Appointment ID
 * @returns Appointment details
 */

export async function APIVersion3GetAppointmentDetails(
    id: string
): Promise<Types.AppointmentTypes.GetAppointmentDetailsResponse> {
    const response = await apiClient.get(`/api/appointments/${id}/`);
    return response.data;
}

/**
 * Update appointment
 * @param id - Appointment ID
 * @param data - Appointment data
 * @returns Appointment updated response
 */

export async function APIVersion3UpdateAppointment(
    id: string,
    data: Types.AppointmentTypes.ActionAppointmentParams
): Promise<Types.AppointmentTypes.UpdateAppointmentResponse> {
    const response = await apiClient.put(`/api/appointments/${id}/`, { data });
    return response.data;
}

/**
 * Delete appointment
 * @param id - Appointment ID
 * @returns Appointment deleted response
 */

export async function APIVersion3DeleteAppointment(
    id: string
): Promise<Types.AppointmentTypes.DeleteAppointmentResponse> {
    const response = await apiClient.delete(`/api/appointments/${id}/`);
    return response.data;
}

/**
 * Cancel appointment
 * @param id - Appointment ID
 * @param reason - Reason for cancellation
 * @returns Appointment cancelled response
 */

export async function APIVersion3CancelAppointment(
    id: string,
    reason: string
): Promise<Types.AppointmentTypes.CancelAppointmentResponse> {
    const response = await apiClient.post(`/api/appointments/${id}/cancel/`, { reason });
    return response.data;
}

/**
 * Check in appointment
 * @param id - Appointment ID
 * @returns Appointment checked in response
 */

export async function APIVersion3CheckInAppointment(
    id: string
): Promise<Types.AppointmentTypes.CheckInAppointmentResponse> {
    const response = await apiClient.post(`/api/appointments/${id}/check-in/`);
    return response.data;
}

/**
 * Complete appointment
 * @param id - Appointment ID
 * @param notes - Notes for the appointment
 * @returns Appointment completed response
 */

export async function APIVersion3CompleteAppointment(
    id: string,
    notes: string
): Promise<Types.AppointmentTypes.CompleteAppointmentResponse> {
    const response = await apiClient.post(`/api/appointments/${id}/complete/`, { notes });
    return response.data;
}

/**
 * Reschedule appointment
 * @param id - Appointment ID
 * @param data - Reschedule appointment data
 * @returns Appointment rescheduled response
 */

export async function APIVersion3RescheduleAppointment(
    id: string,
    data: Types.AppointmentTypes.RescheduleAppointmentParams
): Promise<Types.AppointmentTypes.RescheduleAppointmentResponse> {
    const response = await apiClient.post(`/api/appointments/${id}/reschedule/`, { data });
    return response.data;
}


/***************************** Check Availability *****************************/

/**
 * Check availability
 * @param params - Query parameters
 * @returns Availability response
 */

export async function APIVersion3CheckAvailability(
    params: Types.CheckAvailabilityTypes.CheckAvailabilityQueryParams
): Promise<
    Types.CheckAvailabilityTypes.CheckAvailabilityAvailableResponse
    |
    Types.CheckAvailabilityTypes.CheckAvailabilityUnavailableResponse
> {
    const response = await apiClient.get(`/api/availability/`, { params });
    return response.data;
}

/***************************** Get Available Slots *****************************/

/**
 * Get available slots
 * @param params - Query parameters
 * @returns Available slots
 */

export async function APIVersion3GetAvailableSlots(
    params: Types.AvailableSlotsTypes.AvailableSlotsQueryParams
): Promise<Types.AvailableSlotsTypes.AvailableSlotsResponse> {
    const response = await apiClient.get(`/api/available-slots/`, { params });
    return response.data;
}


/***************************** Get Availability Count *****************************/

/**
 * Get availability count
 * @param params - Query parameters
 * @returns Availability count
 */

export async function APIVersion3GetAvailabilityCount(
    params: Types.LocationTypes.AvailabilityCountQueryParams
): Promise<Types.LocationTypes.AvailabilityCountResponse> {
    const response = await apiClient.get(
        `/api/locations/${params.location_id}/stations/${params.station_id}/availability-count/`,
        { params }
    );
    return response.data;
}

/***************************** Get Time Ranges *****************************/

/**
 * Get time ranges
 * @param params - Query parameters
 * @returns Time ranges
 */

export async function APIVersion3GetTimeRanges(
    params: Types.LocationTypes.TimeRangesQueryParams
): Promise<Types.LocationTypes.TimeRangesResponse> {
    const response = await apiClient.get(
        `/api/locations/${params.location_id}/stations/${params.station_id}/time-ranges/`,
        { params }
    );
    return response.data;
}

/***************************** Get Time Slots *****************************/

/**
 * Get time slots
 * @param params - Query parameters
 * @returns Time slots
 */

export async function APIVersion3GetTimeSlots(
    params: Types.LocationTypes.TimeSlotsQueryParams
): Promise<Types.LocationTypes.TimeSlotsResponseData> {
    const response = await apiClient.get(
        `/api/locations/${params.location_id}/stations/${params.station_id}/time-slots/`,
        { params }
    );
    return response.data;
}

/***************************** Planners *****************************/

/**
 * List planner category rules
 * @param params - Query parameters
 * @returns Planner category rules
 */

export async function APIVersion3ListPlannerCategoryRules(
    params: Types.PlannerTypes.PlannerCategoriesListQueryParams
): Promise<Types.PlannerTypes.PlannerCategoriesListResponse> {
    const response = await apiClient.get(`/api/planner/category-rules/`, { params });
    return response.data;
}


/**
 * Get planner category rules
 * @param id - Planner ID
 * @returns Planner category rules
 */

export async function APIVersion3GetPlannerCategoryRules(
    id: string
): Promise<Types.PlannerTypes.PlannerCategoryRulesResponse> {
    const response = await apiClient.get(`/api/planner/category-rules/${id}/`);
    return response.data;
}

/**
 * Update planner category rules
 * @param id - Planner ID
 * @param data - Planner category rules data
 * @returns Planner category rules updated response
 */

export async function APIVersion3UpdatePlannerCategoryRules(
    id: string,
    data: Types.PlannerTypes.PlannerCategoryRulesUpdateParams
): Promise<Types.PlannerTypes.PlannerCategoryRulesUpdateResponse> {
    const response = await apiClient.put(`/api/planner/category-rules/${id}/`, { data });
    return response.data;
}

/**
 * Delete planner category rules
 * @param id - Planner ID
 * @returns Planner category rules deleted response
 */

export async function APIVersion1DeletePlannerCategoryRules(
    id: string
) {
    const response = await apiClient.delete(`/api/planner/category-rules/${id}/`);
    return response.data;
}

export async function APIVersion3CreatePlannerCategoryRules(
    data: Types.PlannerTypes.PlannerCategoryRulesCreateParams
): Promise<Types.PlannerTypes.PlannerCategoryRulesCreateResponse> {
    const response = await apiClient.post(`/api/planner/category-rules/`, { data });
    return response.data;
}

/***************************** Clients *****************************/

/**
 * Get patients
 */

export async function GetPatients(
    params: Types.ClientsTypes.GetPatientsParams,
    organizationId?: number
): Promise<Types.ClientsTypes.GetPatientsResponse> {
    const headers: Record<string, any> = {};
    if (organizationId) {
        headers["X-organizationId"] = organizationId;
    }

    const response = await apiClient.get(`/api/v1/clients`, { params, headers });
    return response.data;
}

/**
 * Create patient
 */

export async function CreatePatient(
    data: Types.ClientsTypes.CreatePatientPayload,
    organizationId?: number
): Promise<Types.ClientsTypes.CreatePatientResponse> {
    const headers: Record<string, any> = {};
    if (organizationId) {
        headers["X-organizationId"] = organizationId;
    }

    const response = await apiClient.post(`/api/v1/clients`, { data }, {
        headers
    });
    return response.data;
}
