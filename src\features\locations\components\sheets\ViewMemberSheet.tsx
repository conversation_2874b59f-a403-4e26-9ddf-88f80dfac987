import { X, Phone, Mail, Calendar, MapPin, Shield } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	Sheet<PERSON>itle,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";

interface TeamMember {
	id: number;
	name: string;
	email: string;
	phone_number: string;
	roles: Array<{
		role: string;
		stations?: Array<{ id: number; name: string }>;
		locations?: Array<{ id: number; name: string }>;
	}>;
	has_accepted: boolean;
	// Additional computed fields for UI
	displayRole?: string;
	status?: "Active" | "Unverified" | "Pending";
	dateOnboarded?: string;
	avatar?: string;
	// Additional details for view
	socialRoles?: string[];
	roleDetails?: {
		[key: string]: string[];
	};
	locationAccess?: string[];
	timeZone?: string;
	autoMessage?: string;
	serviceManager?: string;
}

interface ViewMemberSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	member: TeamMember | null;
	onEdit?: (member: TeamMember) => void;
}

export function ViewMemberSheet({
	open,
	onOpenChange,
	member,
	onEdit,
}: ViewMemberSheetProps) {
	if (!member) return null;

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case "Active":
				return "default";
			case "Unverified":
				return "secondary";
			case "Pending":
				return "outline";
			default:
				return "default";
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "Active":
				return "border-green-200 bg-green-100 text-green-800 hover:bg-green-100";
			case "Unverified":
				return "border-yellow-200 bg-yellow-100 text-yellow-800 hover:bg-yellow-100";
			case "Pending":
				return "bg-gray-100 text-gray-800 hover:bg-gray-100";
			default:
				return "bg-gray-100 text-gray-800 hover:bg-gray-100";
		}
	};

	// Extract basic member details for display
	const memberDetails = {
		...member,
		timeZone: member.timeZone || "EST (UTC-5)",
		autoMessage:
			member.autoMessage ||
			"Thank you for booking with us. I'll be with you shortly!",
		serviceManager: member.serviceManager || "Janet Samuel",
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] flex w-[500px] overflow-y-auto px-8 py-10 sm:max-w-[500px]">
				<SheetHeader className="p-0 pb-6">
					<div className="flex items-center justify-between">
						<SheetTitle className="text-2l p-0 font-semibold">
							Member Details
						</SheetTitle>
					</div>
				</SheetHeader>

				<div className="space-y-6">
					<div>
						<Avatar className="h-32 w-32 rounded-full">
							<AvatarImage
								src={member.avatar || ""}
								alt={member.name}
							/>
							<AvatarFallback className="bg-slate-200 text-xl">
								{member.name
									? member.name.charAt(0).toUpperCase()
									: "U"}
							</AvatarFallback>
						</Avatar>
					</div>
					{/* Profile Info */}
					<div className="space-y-4">
						<h3 className="text-lg font-medium">
							Profile Info <Separator />
						</h3>

						{/* Two-column grid layout for profile info */}
						<div className="grid grid-cols-2 gap-6">
							{/* Full Name */}
							<div>
								<p className="mb-1 text-sm font-medium text-gray-700">
									Full Name
								</p>
								<p className="text-sm font-medium text-gray-900">
									{member.name}
								</p>
							</div>

							{/* Email Address */}
							<div>
								<p className="mb-1 text-sm font-medium text-gray-700">
									Email Address
								</p>
								<p className="text-sm text-gray-600">
									{member.email}
								</p>
							</div>
							<Separator className="col-span-2" />

							{/* Phone Number */}
							<div>
								<p className="mb-1 text-sm font-medium text-gray-700">
									Phone Number
								</p>
								<p className="text-sm text-gray-600">
									{member.phone_number}
								</p>
							</div>

							{/* Date Onboarded */}
							<div>
								<p className="mb-1 text-sm font-medium text-gray-700">
									Date Onboarded
								</p>
								<p className="text-sm text-gray-600">
									{member.dateOnboarded}
								</p>
							</div>
						</div>
					</div>

					{/* Applied Role */}
					<div className="space-y-4">
						<h3 className="text-lg font-medium">Applied Role</h3>
						<Separator className="col-span-2" />
						{/* Dynamic roles based on member data */}
						<div className="space-y-4">
							{member.roles?.map((roleObj, roleIndex) => {
								const roleName = roleObj.role
									.toLowerCase()
									.replace(/_/g, " ")
									.replace(/\b\w/g, (l) => l.toUpperCase());

								return (
									<div key={roleIndex} className="rounded-md">
										<h4 className="mb-3 text-base font-medium text-gray-900">
											{roleName}
										</h4>

										{/* Show locations if available */}
										{roleObj.locations &&
											roleObj.locations.length > 0 && (
												<div className="mb-4 space-y-3">
													<div className="flex items-center gap-2">
														<MapPin className="h-4 w-4 text-gray-500" />
														<p className="text-sm font-medium text-gray-700">
															Assigned Locations
														</p>
													</div>
													<div className="grid grid-cols-2 gap-2">
														{roleObj.locations.map(
															(location) => (
																<div
																	key={
																		location.id
																	}
																	className="rounded bg-gray-100 px-2 py-1 text-center"
																>
																	<p className="text-xs text-gray-600">
																		{
																			location.name
																		}
																	</p>
																</div>
															)
														)}
													</div>
												</div>
											)}

										{/* Show stations if available */}
										{roleObj.stations &&
											roleObj.stations.length > 0 && (
												<div className="space-y-3">
													<div className="flex items-center gap-2">
														<MapPin className="h-4 w-4 text-gray-500" />
														<p className="text-sm font-medium text-gray-700">
															Assigned Stations
														</p>
													</div>
													<div className="grid grid-cols-2 gap-2">
														{roleObj.stations.map(
															(station) => (
																<div
																	key={
																		station.id
																	}
																	className="rounded bg-gray-100 px-2 py-1 text-center"
																>
																	<p className="text-xs text-gray-600">
																		{
																			station.name
																		}
																	</p>
																</div>
															)
														)}
													</div>
												</div>
											)}

										{/* If no locations or stations, show general access */}
										{(!roleObj.locations ||
											roleObj.locations.length === 0) &&
											(!roleObj.stations ||
												roleObj.stations.length ===
													0) && (
												<div className="space-y-3">
													<div className="flex items-center gap-2">
														<MapPin className="h-4 w-4 text-gray-500" />
														<p className="text-sm font-medium text-gray-700">
															General Access
														</p>
													</div>
													<div className="grid grid-cols-1 gap-2">
														<div className="rounded bg-gray-100 px-2 py-1 text-center">
															<p className="text-xs text-gray-600">
																All assigned
																locations
															</p>
														</div>
													</div>
												</div>
											)}
									</div>
								);
							})}
						</div>
					</div>
				</div>
				<SheetFooter>
					{/* Footer Actions */}
					<div className="align-self-end flex justify-end gap-3 justify-self-end pt-6">
						<Button
							variant="outline"
							onClick={() => onOpenChange(false)}
						>
							Close
						</Button>
						{onEdit && (
							<Button
								onClick={() => {
									onEdit(member);
									onOpenChange(false);
								}}
								className="migranium-button"
							>
								Edit Member
							</Button>
						)}
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
