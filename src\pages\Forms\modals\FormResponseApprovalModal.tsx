import { type FC } from "react";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { FormResponseTypes } from "../types";
import { cn } from "@/lib/utils";

interface FormResponseApprovalModalProps {
	open: boolean;
	isLoading: boolean;
	onOpenChange: (open: boolean) => void;
	response?: FormResponseTypes.FormResponseDataType;
	handleSubmit: () => void;
	handleCancel: () => void;
	formResponseNotes: string;
	handleNotesChange: (notes: string) => void;
	type: "approve" | "reject";
}

export const FormResponseApprovalModal: FC<FormResponseApprovalModalProps> = ({
	open,
	isLoading,
	onOpenChange,
	response,
	handleSubmit,
	handleCancel,
	formResponseNotes,
	handleNotesChange,
	type,
}) => {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="z-[9999] sm:max-w-[425px]">
				<DialogHeader className="gap-0">
					<DialogTitle className="text-lg font-semibold">
						{`${type === "approve" ? "Approve" : "Decline"} Form?`}
					</DialogTitle>
					<DialogDescription className="text-muted text-sm">
						{`Are you sure you want to ${type === "approve" ? "approve" : "decline"} ${response?.client?.name ? `${response?.client?.name}’s` : ""} ${response?.form?.name ? `submission of ${response?.form?.name}` : ""} ${response?.service?.name ? `for ${response?.service?.name}` : ""} ${response?.station?.name ? ` with ${response?.station?.name}?` : "?"}`}
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-4">
					{/* Add Notes */}
					<div className="space-y-2">
						<Label
							htmlFor="notes"
							className="text-right text-base leading-none font-medium"
						>
							Add Notes
						</Label>
						<Textarea
							id="notes"
							placeholder="Enter Notes Here..."
							value={formResponseNotes}
							onChange={(e) => handleNotesChange(e.target.value)}
							className="min-h-[144px] text-base"
						/>
					</div>
				</div>

				<DialogFooter>
					<div className="flex flex-col gap-3 sm:flex-row sm:justify-end sm:gap-2">
						<Button
							variant="outline"
							onClick={handleCancel}
							className="w-full min-w-[130px] cursor-pointer sm:w-auto"
						>
							Cancel
						</Button>
						<Button
							onClick={handleSubmit}
							disabled={isLoading}
							className={cn(
								"w-full min-w-[130px] cursor-pointer sm:w-auto",
								type === "approve" &&
									"bg-[#16A34A] hover:bg-[#16A34A]/90",
								type === "reject" &&
									"bg-[#DC2626] hover:bg-[#DC2626]/90"
							)}
						>
							{isLoading ? (
								<Loader2 className="h-4 w-4 animate-spin" />
							) : type === "approve" ? (
								"Yes, Approve"
							) : (
								"Yes, Decline"
							)}
						</Button>
					</div>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};
