import { type FC } from "react";
import {
	Trash2,
	Send,
	ChevronRight,
	Pencil,
	User,
	Info,
	MessageCircleMore,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import type { Location } from "@/features/locations/types";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import type { Patient } from "@/features/provider-details/types";

export interface PatientsCardProps {
	patient: Patient;
	onEdit?: (patient: Patient) => void;
	onView?: (patient: Patient) => void;
	isSelected?: boolean;
	onSelectionChange?: (selected: boolean) => void;
	onSendBookingLink?: () => void;
}

export const PatientsCard: React.FC<PatientsCardProps> = ({
	patient,
	onEdit,
	onView,
	isSelected = false,
	onSelectionChange,
	onSendBookingLink,
}) => {
	return (
		<div
			className="hover:bg-foreground-muted flex cursor-pointer flex-wrap items-center justify-start border-b border-zinc-200 bg-white last:border-b-0"
			onClick={() => onView?.(patient)}
		>
			{/* Checkbox Section */}
			<div
				className="flex h-16 items-center px-4"
				onClick={(e) => e.stopPropagation()}
			>
				<Checkbox
					checked={isSelected}
					onCheckedChange={onSelectionChange}
					className="cursor-pointer"
				/>
			</div>

			{/* Name Section */}
			<div className="flex flex-2 items-center gap-3 px-3">
				<div className="bg-foreground-disable flex h-9 w-9 items-center justify-center rounded-full">
					<User className="text-muted h-4 w-4" />
				</div>
				<div className="flex items-center gap-1">
					<h2 className="text-base leading-5 font-medium">
						{patient.name}
					</h2>
					<span className="h-1.5 w-1.5 rounded-full bg-red-500"></span>
				</div>
			</div>

			{/* Status Section */}
			<div className="flex flex-1 items-center px-3">
				<Badge
					variant="outline"
					className={cn(
						"border-transparent",
						patient.isActive
							? "bg-foreground-success text-[#0a2914]"
							: "bg-foreground-warning text-[#0a2914]"
					)}
				>
					{patient.isActive ? "Active" : "Inactive"}
				</Badge>
			</div>

			{/* Email Section */}
			<div className="flex flex-1 items-center px-3">
				<p className=" text-sm leading-5 font-normal text-[#71717A]">
					{patient.email}
				</p>
			</div>

			{/* Phone Section */}
			<div className="flex flex-1 items-center px-3">
				<p className=" text-sm leading-5 font-normal text-[#71717A]">
					{patient.phone}
				</p>
			</div>

			{/* Last Visit Section */}
			<div className="flex flex-1 items-center px-3">
				<p className=" text-sm leading-5 font-normal text-[#71717A]">
					{patient.lastVisit}
				</p>
			</div>

			{/* Actions Section */}
			<div className="flex flex-1 items-center justify-end px-3">
				<div className="flex items-center gap-2.5">
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
						}}
					>
						<Trash2 className="h-4 w-4 text-[#71717A]" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onEdit?.(patient);
						}}
					>
						<Pencil className="h-4 w-4 text-[#71717A]" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
						}}
					>
						<MessageCircleMore className="h-4 w-4 text-[#71717A]" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							onView?.(patient);
						}}
					>
						<Info className="h-4 w-4 text-[#71717A]" />
					</Button>
				</div>
			</div>
		</div>
	);
};
