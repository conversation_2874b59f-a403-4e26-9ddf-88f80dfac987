import React, { useState } from "react";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { GripVertical } from "lucide-react";

interface AutoSignContentProps {
	selectedLocationId?: string | null;
	selectedStationId?: string | null;
}

export const AutoSignContent: React.FC<AutoSignContentProps> = ({
	selectedLocationId,
	selectedStationId,
}) => {
	const [autoAssignEnabled, setAutoAssignEnabled] = useState(true);
	const [reassignPreviousPatients, setReassignPreviousPatients] =
		useState(true);
	const [assignBasedOnLoad, setAssignBasedOnLoad] = useState(true);
	const [allowConsecutiveAppointments, setAllowConsecutiveAppointments] =
		useState(false);

	const handleSave = () => {
		// TODO: Implement save functionality
		console.log("Auto Sign settings:", {
			autoAssignEnabled,
			reassignPreviousPatients,
			assignBasedOnLoad,
			allowConsecutiveAppointments,
			selectedLocationId,
			selectedStationId,
		});
	};

	const handleCancel = () => {
		// TODO: Reset to original values
		console.log("Cancel changes");
	};

	return (
		<div className="max-w-4xl p-6">
			<div className="space-y-8">
				{/* Auto Assign Section */}
				<div className="space-y-6">
					<div className="flex items-center justify-between">
						<div className="flex items-center space-x-3">
							<div>
								<Label
									htmlFor="auto-assign"
									className="text-base leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
								>
									Automatically Assign Providers Based on
									Specific Factors
								</Label>
								<p className="mt-1 text-sm text-gray-600">
									Set preferences for automatically assigning
									patients to providers based on available
									criteria.
								</p>
							</div>
						</div>
						<span className="item-center flex gap-3 text-sm font-medium text-blue-600">
							<Switch
								id="auto-assign"
								checked={autoAssignEnabled}
								onCheckedChange={setAutoAssignEnabled}
							/>
							Yes
						</span>
					</div>

					{/* Sub-options when Auto Assign is enabled */}
					{autoAssignEnabled && (
						<div className="ml-8 space-y-6 rounded-lg p-4">
							{/* Reassign previous patients */}
							<div className="flex items-center justify-between">
								<div className="flex items-start space-x-3">
									<div className="mt-1 flex items-center space-x-2">
										<GripVertical />
									</div>
									<div className="flex-1">
										<div className="flex items-center space-x-3">
											<Label
												htmlFor="reassign-patients"
												className="text-sm leading-none font-medium"
											>
												Reassign previous patients
											</Label>
										</div>
										<p className="mt-2 text-sm text-gray-600">
											Automatically assign returning
											patients to the same provider they
											saw previously, when available.
										</p>
									</div>
								</div>
								<span className="item-center flex gap-3 text-sm font-medium text-blue-600">
									<Switch
										id="reassign-patients"
										checked={reassignPreviousPatients}
										onCheckedChange={
											setReassignPreviousPatients
										}
									/>
									Yes
								</span>
							</div>

							{/* Assign based on load */}
							<div className="flex items-center justify-between">
								<div className="flex items-start space-x-3">
									<div className="mt-1 flex items-center space-x-2">
										<GripVertical />
									</div>
									<div className="flex-1">
										<div className="flex items-center space-x-3">
											<Label
												htmlFor="assign-load"
												className="text-sm leading-none font-medium"
											>
												Assign based on load
											</Label>
										</div>
										<p className="mt-2 text-sm text-gray-600">
											Automatically distribute
											appointments based on provider
											availability and current workload.
										</p>
									</div>
								</div>
								<span className="item-center flex gap-3 text-sm font-medium text-blue-600">
									<Switch
										id="assign-load"
										checked={assignBasedOnLoad}
										onCheckedChange={setAssignBasedOnLoad}
									/>
									Yes
								</span>
							</div>

							{/* Allow consecutive appointments */}
							<div className="flex items-center justify-between">
								<div className="flex items-start space-x-3">
									<div className="mt-1 flex items-center space-x-2">
										<GripVertical />
									</div>
									<div className="flex-1">
										<div className="flex items-center space-x-3">
											<Label
												htmlFor="consecutive-appointments"
												className="text-sm leading-none font-medium"
											>
												Allow the same patient to book
												consecutive appointments
											</Label>
										</div>
										<p className="mt-2 text-sm text-gray-600">
											Allow a patient to book multiple
											consecutive time slots with the same
											provider if needed.
										</p>
									</div>
								</div>
								<span className="item-center flex gap-3 text-sm font-medium text-blue-600">
									<Switch
										id="consecutive-appointments"
										checked={allowConsecutiveAppointments}
										onCheckedChange={
											setAllowConsecutiveAppointments
										}
									/>
									No
								</span>
							</div>
						</div>
					)}
				</div>

				{/* Action Buttons */}
				<div className="flex justify-end gap-3 pt-4">
					<Button
						type="button"
						variant="outline"
						onClick={handleCancel}
						className="px-6"
					>
						Cancel
					</Button>
					<Button type="button" onClick={handleSave} className="px-6">
						Save
					</Button>
				</div>
			</div>
		</div>
	);
};
