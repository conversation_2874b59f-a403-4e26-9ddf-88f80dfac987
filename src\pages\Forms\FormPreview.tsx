import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Edit, Pencil, Trash2 } from "lucide-react";
import { FormRenderer } from "./components/FormRenderer";
import { useFormDraftStore } from "./store/slices/formDraftSlice";
import type { FormTypes } from "./types";
import { useUIStore } from "@/stores/uiStore";

export const FormPreviewPage: React.FC = () => {
	const location = useLocation();
	const navigate = useNavigate();
	const { loadDraft, deleteDraft } = useFormDraftStore();
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setCurrentPageTitle = useUIStore(
		(state) => state.setCurrentPageTitle
	);
	const setShowHeaderDate = useUIStore((state) => state.setShowHeaderDate);

	const [formData, setFormData] = useState<FormTypes.FormDataType | null>(
		null
	);
	const [returnTo, setReturnTo] = useState("/dashboard/forms");

	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Dashboard",
				href: "/",
			},
			{
				label: "Forms",
				href: "/dashboard/forms",
			},
			{
				label: "Form Manager",
				href: "/dashboard/forms",
			},
			{
				label: "Preview Form",
			},
		]);

		setCurrentPageTitle("Form Manager");
		setShowHeaderDate(false);

		// Get form data from navigation state or draft store
		if (location.state?.formData) {
			setFormData(location.state.formData);
			setReturnTo(location.state.returnTo || "/dashboard/forms");
		} else if (location.state?.draftId) {
			const draft = loadDraft(location.state.draftId);
			if (draft) {
				setFormData(draft);
				setReturnTo(location.state.returnTo || "/dashboard/forms");
			}
		} else {
			// No form data available, redirect back
			navigate("/dashboard/forms");
		}

		return () => {
			setBreadcrumbs([]);
		};
	}, [location.state, loadDraft, navigate]);

	const handleBackToEdit = () => {
		navigate(returnTo, {
			state: {
				formData,
				fromPreview: true,
			},
		});
	};

	const handleDeleteDraft = () => {
		deleteDraft(location.state?.draftId);
		navigate("/dashboard/forms");
	};

	if (!formData) {
		return <div>Loading...</div>;
	}

	return (
		<div className="container mx-auto py-6">
			{/* Header */}
			<div className="mb-6 flex items-center justify-end">
				<div className="flex gap-2">
					<Button
						variant="outline"
						onClick={handleDeleteDraft}
						className="flex min-w-[130px] cursor-pointer items-center gap-2 text-xs text-[#DC2626]"
					>
						<Trash2 className="h-4 w-4" />
						Delete Form
					</Button>
					<Button
						onClick={handleBackToEdit}
						className="min-w-[130px] cursor-pointer text-xs"
					>
						<Pencil className="h-4 w-4" />
						Edit
					</Button>
				</div>
			</div>

			{/* Preview Content */}
			<div className="mx-auto max-w-[720px]">
				<FormRenderer
					formData={formData}
					mode="preview"
					responses={[]}
					onResponseChange={() => {}}
					onSubmit={() => {}}
				/>
			</div>
		</div>
	);
};
