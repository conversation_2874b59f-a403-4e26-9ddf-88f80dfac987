import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SketchPicker, type ColorResult } from "react-color";
import { Separator } from "@/components/ui/separator";
import { Eye, PaintBucket, Check } from "lucide-react";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useUpdateOrganizationTheme } from "@/features/organizations/hooks/useOrganizations";
import { toast } from "sonner";

const colorPalette = [
	"#222A31",
	"#5E98C9",
	"#0277D8",
	"#9A76C9",
	"#32BA3F",
	"#E36F6F",
	"#E9ED18",
	"#FF9500",
	"#C77676",
	"#FA38C4",
	"#54758F",
	"#A3762D",
];

const ThemeSettings: React.FC = () => {
	const { organization, organizationId, refetch } = useOrganizationContext();
	const updateThemeMutation = useUpdateOrganizationTheme();

	// Initialize with current organization theme or default
	const [primaryColor, setPrimaryColor] = useState(
		organization?.theme || "#2563eb"
	);
	const [previewMode, setPreviewMode] = useState(false);
	const [showColorPicker, setShowColorPicker] = useState(false);
	const colorPickerRef = useRef<HTMLDivElement>(null);

	// Update color when organization data loads
	useEffect(() => {
		if (organization?.theme) {
			if (organization?.theme == "default") {
				setPrimaryColor("#2563eb");
				return;
			}
			setPrimaryColor(organization.theme);
		}
	}, [organization?.theme]);

	const handleColorChange = (color: ColorResult) => {
		setPrimaryColor(color.hex);
	};

	const handleColorSelect = (color: string) => {
		setPrimaryColor(color);
		setShowColorPicker(false);
	};

	const toggleColorPicker = () => {
		setShowColorPicker(!showColorPicker);
	};

	// Click outside handler for color picker
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				colorPickerRef.current &&
				!colorPickerRef.current.contains(event.target as Node)
			) {
				setShowColorPicker(false);
			}
		};

		if (showColorPicker) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [showColorPicker]);

	const handlePreview = () => {
		setPreviewMode(!previewMode);
		if (!previewMode) {
			// Apply preview theme to CSS variables
			document.documentElement.style.setProperty(
				"--color-primary",
				primaryColor
			);
			toast.info("Theme preview applied. Click Preview again to revert.");
		} else {
			// Revert to original theme
			document.documentElement.style.setProperty(
				"--color-primary",
				organization?.theme === "default"
					? "#2563eb"
					: organization?.theme || "#2563eb"
			);
			toast.info("Preview reverted to original theme.");
		}
	};

	const handleUpdate = async () => {
		if (!organizationId) {
			toast.error("Organization ID not found");
			return;
		}

		try {
			await updateThemeMutation.mutateAsync({
				orgId: organizationId.toString(),
				theme: primaryColor,
			});

			// Refetch organization data to get the latest information
			await refetch();

			// Apply the new theme permanently
			document.documentElement.style.setProperty(
				"--color-primary",
				primaryColor
			);
			setPreviewMode(false);
		} catch (error) {
			console.error("Failed to update theme:", error);
		}
	};

	return (
		<div className="max-w-lg">
			<h2 className="mb-4 text-xl font-semibold">Theme</h2>
			<div className="mb-2 flex flex-col gap-2">
				<span className="text-sm font-medium">Assign Color</span>
				<Separator className="mb-4" />
				<div className="flex flex-col justify-center gap-8">
					<div className="relative flex items-center gap-2">
						<button
							type="button"
							onClick={toggleColorPicker}
							className="flex h-9 items-center gap-4 rounded-md bg-gray-100 p-2 transition-colors hover:bg-gray-200"
						>
							<PaintBucket className="h-5 w-5" />
							<div
								className="h-6 w-6 rounded-md"
								style={{
									backgroundColor: primaryColor,
								}}
							/>
						</button>
						<Input
							value={primaryColor}
							onChange={(e) => handleColorSelect(e.target.value)}
							className="h-9 w-24 text-xs"
							placeholder="#000000"
						/>

						{showColorPicker && (
							<div
								ref={colorPickerRef}
								className="absolute top-full left-0 z-50 mt-2 border border-gray-200 bg-white shadow-lg"
								style={{ borderRadius: "8px" }}
							>
								<style>{`
									.sketch-picker-custom .flexbox-fix:last-child {
										display: none !important;
									}
									.sketch-picker-custom .flexbox-fix:nth-last-child(2) {
										display: none !important;
									}
								`}</style>
								<div className="sketch-picker-custom">
									<SketchPicker
										color={primaryColor}
										onChange={handleColorChange}
										disableAlpha={true}
										presetColors={[]}
									/>
								</div>
							</div>
						)}
					</div>

					<div className="flex flex-col gap-1.5">
						<p className="text-[10px] text-gray-500">Recent</p>
						<div className="flex flex-wrap gap-1.5">
							{colorPalette.map((color, index) => (
								<button
									key={index}
									type="button"
									onClick={() => handleColorSelect(color)}
									className={`relative h-7 w-7 rounded-md transition-all ${primaryColor === color
										? "ring-2 ring-blue-500 ring-offset-1"
										: ""
										}`}
									style={{
										backgroundColor: color,
									}}
								>
									{primaryColor === color && (
										<div className="absolute inset-0 flex items-center justify-center">
											<Check className="h-3.5 w-3.5 text-white" />
										</div>
									)}
								</button>
							))}
						</div>
					</div>
				</div>
				<div className="mt-6 flex gap-3 self-end">
					<Button
						variant="outline"
						className="flex items-center gap-2"
						onClick={handlePreview}
						disabled={updateThemeMutation.isPending}
					>
						<Eye className="h-4 w-4" />
						{previewMode ? "Revert Preview" : "Preview"}
					</Button>
					<Button
						className="migranium-button"
						onClick={handleUpdate}
						disabled={updateThemeMutation.isPending}
					>
						{updateThemeMutation.isPending
							? "Updating..."
							: "Update"}
					</Button>
				</div>
			</div>
		</div>
	);
};

export default ThemeSettings;
