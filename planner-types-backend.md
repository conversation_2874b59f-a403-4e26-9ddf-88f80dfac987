/**
 * MIGRANIUM PLANNER SYSTEM - FRONTEND INTEGRATION GUIDE
 * Complete TypeScript interfaces and API contracts for all planner endpoints
 */

// ============================================================================
// ENUMS & CONSTANTS
// ============================================================================

export enum PreferenceType {
  AVAILABILITY = 'availability',
  RESTRICTION = 'restriction'
}

export enum FrequencyPeriod {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly'
}

export enum OccurrencePattern {
  ONCE = 'once',
  ROLLING = 'rolling',
  FIXED_DATES = 'fixed_dates'
}

export enum RuleStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  OVERRIDE = 'override',
  CONFLICTED = 'conflicted'
}

export enum RecurrenceFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly'
}

export enum TargetType {
  ORGANIZATION = 'organization',
  LOCATION = 'location',
  LOCATIONSTATION = 'locationstation'
}

// Weekdays: 0=Monday, 1=Tuesday, ..., 6=Sunday
export const WEEKDAYS = [
  { value: 0, label: 'Monday' },
  { value: 1, label: 'Tuesday' },
  { value: 2, label: 'Wednesday' },
  { value: 3, label: 'Thursday' },
  { value: 4, label: 'Friday' },
  { value: 5, label: 'Saturday' },
  { value: 6, label: 'Sunday' }
] as const;

// ============================================================================
// BASE INTERFACES
// ============================================================================

export interface BaseResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
  meta?: any;
}

export interface PaginatedResponse<T = any> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface ErrorResponse {
  success: false;
  message: string;
  error: string;
  errors?: Record<string, string[]>;
}

export interface PaginationParams {
  page?: number;
  per_page?: number;
}

export interface BaseEntity {
  id: string; // UUID
  created_at: string;
  updated_at: string;
}

// ============================================================================
// 1. SCHEDULE PLANNERS
// ============================================================================

export interface SchedulePlanner extends BaseEntity {
  content_type: string;
  object_id: string;
  target_type: TargetType;
  organization_id: string;
  organization_name: string;
  is_active: boolean;
}

export interface SchedulePlannerDetail extends SchedulePlanner {
  time_offs: PlannerTimeOff[];
  service_rules: ServiceRule[];
  category_rules: CategoryRule[];
}

export interface CreateSchedulePlannerRequest {
  content_type: string;
  object_id: string;
  is_active?: boolean;
}

export interface UpdateSchedulePlannerRequest {
  content_type?: string;
  object_id?: string;
  is_active?: boolean;
}

export interface SchedulePlannerListParams extends PaginationParams {
  target_type?: TargetType;
  organization_id?: string;
  is_active?: boolean;
  search?: string;
  ordering?: string;
  include?: string; // 'details,time_offs,service_rules,category_rules,stats'
  exclude?: string; // 'created_at,updated_at'
  content_type__model?: string;
}

export interface PlannerStats {
  total_planners: number;
  active_planners: number;
  total_time_offs: number;
  total_service_preferences: number;
  total_category_preferences: number;
  organization_breakdown: any[];
}

// ============================================================================
// 2. CATEGORY RULES
// ============================================================================

export interface CategoryRule extends BaseEntity {
  client_category: string; // UUID
  core_category_id: number;
  category_name: string;
  rule_name: string;
  priority: number;
  preference_type: PreferenceType;
  weekdays?: number[]; // [0,1,2,3,4,5,6]
  start_time?: string; // "HH:MM"
  end_time?: string; // "HH:MM"
  is_all_day: boolean;
  max_frequency?: number;
  frequency_period?: FrequencyPeriod;
  start_date?: string; // "YYYY-MM-DD"
  end_date?: string; // "YYYY-MM-DD"
  occurrence_pattern: OccurrencePattern;
  recurrence_interval: number;
  status: RuleStatus;
  is_active: boolean;
}

export interface CreateCategoryRuleRequest {
  client_category: string;
  rule_name: string;
  priority?: number;
  preference_type: PreferenceType;
  weekdays?: number[];
  start_time?: string;
  end_time?: string;
  is_all_day?: boolean;
  max_frequency?: number;
  frequency_period?: FrequencyPeriod;
  start_date?: string;
  end_date?: string;
  occurrence_pattern: OccurrencePattern;
  recurrence_interval?: number;
  status?: RuleStatus;
  is_active?: boolean;
}

export interface UpdateCategoryRuleRequest extends Partial<CreateCategoryRuleRequest> {}

export interface CategoryRuleListParams extends PaginationParams {
  category_id?: number;
  client_category?: string;
  planner?: string;
  planner_id?: string;
  preference_type?: PreferenceType;
  status?: RuleStatus;
  is_active?: boolean;
  search?: string;
  ordering?: string;
}

// ============================================================================
// 3. SERVICE RULES
// ============================================================================

export interface ServiceRule extends BaseEntity {
  service: string; // UUID
  core_service_id: number;
  service_name: string;
  rule_name: string;
  priority: number;
  preference_type: PreferenceType;
  weekdays?: number[];
  start_time?: string;
  end_time?: string;
  is_all_day: boolean;
  max_frequency?: number;
  frequency_period?: FrequencyPeriod;
  start_date?: string;
  end_date?: string;
  occurrence_pattern: OccurrencePattern;
  recurrence_interval: number;
  status: RuleStatus;
  is_active: boolean;
}

export interface CreateServiceRuleRequest {
  service: string;
  rule_name: string;
  priority?: number;
  preference_type: PreferenceType;
  weekdays?: number[];
  start_time?: string;
  end_time?: string;
  is_all_day?: boolean;
  max_frequency?: number;
  frequency_period?: FrequencyPeriod;
  start_date?: string;
  end_date?: string;
  occurrence_pattern: OccurrencePattern;
  recurrence_interval?: number;
  status?: RuleStatus;
  is_active?: boolean;
}

export interface UpdateServiceRuleRequest extends Partial<CreateServiceRuleRequest> {}

export interface ServiceRuleListParams extends PaginationParams {
  service?: string;
  service_id?: number;
  planner?: string;
  planner_id?: string;
  preference_type?: PreferenceType;
  status?: RuleStatus;
  is_active?: boolean;
  search?: string;
  ordering?: string;
}

// ============================================================================
// 4. TIME-OFFS
// ============================================================================

export interface PlannerTimeOff extends BaseEntity {
  title: string;
  start_date: string; // "YYYY-MM-DD"
  end_date?: string; // "YYYY-MM-DD"
  start_time?: string; // "HH:MM"
  end_time?: string; // "HH:MM"
  is_all_day: boolean;
  is_recurring: boolean;
  recurrence_frequency?: RecurrenceFrequency;
  recurrence_interval: number;
  recurrence_weekdays?: number[];
  recurrence_end_date?: string; // "YYYY-MM-DD"
  is_active: boolean;
}

export interface CreateTimeOffRequest {
  title: string;
  start_date: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  is_all_day?: boolean;
  is_recurring?: boolean;
  recurrence_frequency?: RecurrenceFrequency;
  recurrence_interval?: number;
  recurrence_weekdays?: number[];
  recurrence_end_date?: string;
  is_active?: boolean;
}

export interface UpdateTimeOffRequest extends Partial<CreateTimeOffRequest> {}

export interface TimeOffListParams extends PaginationParams {
  planner?: string;
  planner_id?: string;
  date_from?: string; // "YYYY-MM-DD"
  date_to?: string; // "YYYY-MM-DD"
  is_active?: boolean;
  is_recurring?: boolean;
  search?: string;
  ordering?: string;
}

// ============================================================================
// 5. PLANNER UTILITIES
// ============================================================================

export interface PlannerChoices {
  recurrence_frequencies: { value: string; label: string }[];
  preference_types: { value: string; label: string }[];
  weekday_choices: { value: number; label: string }[];
}

export interface InheritanceInfo {
  has_own_planner: boolean;
  time_offs: PlannerTimeOff[];
  service_preferences: ServiceRule[];
  category_preferences: CategoryRule[];
}

export interface OrganizationPlanner extends InheritanceInfo {
  organization_id: string;
}

export interface LocationPlanner extends InheritanceInfo {
  location_id: string;
  inherits_from_organization: boolean;
}

export interface LocationStationPlanner extends InheritanceInfo {
  location_station_id: string;
  inherits_from_organization: boolean;
  inherits_from_location: boolean;
}

// ============================================================================
// API CLIENT CLASS
// ============================================================================

export class PlannerApiClient {
  private baseUrl: string;
  private headers: Record<string, string>;

  constructor(baseUrl: string, token: string) {
    this.baseUrl = baseUrl;
    this.headers = {
      'Authorization': `Token ${token}`,
      'Content-Type': 'application/json',
    };
  }

  // Schedule Planners
  async listPlanners(params?: SchedulePlannerListParams): Promise<PaginatedResponse<SchedulePlanner>> {
    const url = new URL(`${this.baseUrl}/api/planner/planners/`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) url.searchParams.append(key, String(value));
      });
    }
    const response = await fetch(url.toString(), { headers: this.headers });
    return response.json();
  }

  async createPlanner(data: CreateSchedulePlannerRequest): Promise<BaseResponse<SchedulePlanner>> {
    const response = await fetch(`${this.baseUrl}/api/planner/planners/`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async getPlanner(id: string): Promise<BaseResponse<SchedulePlannerDetail>> {
    const response = await fetch(`${this.baseUrl}/api/planner/planners/${id}/`, {
      headers: this.headers,
    });
    return response.json();
  }

  async updatePlanner(id: string, data: UpdateSchedulePlannerRequest): Promise<BaseResponse<SchedulePlanner>> {
    const response = await fetch(`${this.baseUrl}/api/planner/planners/${id}/`, {
      method: 'PUT',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async patchPlanner(id: string, data: Partial<UpdateSchedulePlannerRequest>): Promise<BaseResponse<SchedulePlanner>> {
    const response = await fetch(`${this.baseUrl}/api/planner/planners/${id}/`, {
      method: 'PATCH',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async deletePlanner(id: string): Promise<BaseResponse<{ id: string; deleted_at: string }>> {
    const response = await fetch(`${this.baseUrl}/api/planner/planners/${id}/`, {
      method: 'DELETE',
      headers: this.headers,
    });
    return response.json();
  }

  async validatePlannerConfig(id: string, data: UpdateSchedulePlannerRequest): Promise<BaseResponse<any>> {
    const response = await fetch(`${this.baseUrl}/api/planner/planners/${id}/validate_config/`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async getPlannerStats(): Promise<BaseResponse<PlannerStats>> {
    const response = await fetch(`${this.baseUrl}/api/planner/planners/stats/`, {
      headers: this.headers,
    });
    return response.json();
  }

  // Category Rules
  async listCategoryRules(params?: CategoryRuleListParams): Promise<PaginatedResponse<CategoryRule>> {
    const url = new URL(`${this.baseUrl}/api/planner/category-rules/`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) url.searchParams.append(key, String(value));
      });
    }
    const response = await fetch(url.toString(), { headers: this.headers });
    return response.json();
  }

  async createCategoryRule(data: CreateCategoryRuleRequest): Promise<BaseResponse<CategoryRule>> {
    const response = await fetch(`${this.baseUrl}/api/planner/category-rules/`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async getCategoryRule(id: string): Promise<BaseResponse<CategoryRule>> {
    const response = await fetch(`${this.baseUrl}/api/planner/category-rules/${id}/`, {
      headers: this.headers,
    });
    return response.json();
  }

  async updateCategoryRule(id: string, data: UpdateCategoryRuleRequest): Promise<BaseResponse<CategoryRule>> {
    const response = await fetch(`${this.baseUrl}/api/planner/category-rules/${id}/`, {
      method: 'PUT',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async patchCategoryRule(id: string, data: Partial<UpdateCategoryRuleRequest>): Promise<BaseResponse<CategoryRule>> {
    const response = await fetch(`${this.baseUrl}/api/planner/category-rules/${id}/`, {
      method: 'PATCH',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async deleteCategoryRule(id: string): Promise<void> {
    await fetch(`${this.baseUrl}/api/planner/category-rules/${id}/`, {
      method: 'DELETE',
      headers: this.headers,
    });
  }

  // Service Rules
  async listServiceRules(params?: ServiceRuleListParams): Promise<PaginatedResponse<ServiceRule>> {
    const url = new URL(`${this.baseUrl}/api/planner/service-rules/`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) url.searchParams.append(key, String(value));
      });
    }
    const response = await fetch(url.toString(), { headers: this.headers });
    return response.json();
  }

  async createServiceRule(data: CreateServiceRuleRequest): Promise<BaseResponse<ServiceRule>> {
    const response = await fetch(`${this.baseUrl}/api/planner/service-rules/`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async getServiceRule(id: string): Promise<BaseResponse<ServiceRule>> {
    const response = await fetch(`${this.baseUrl}/api/planner/service-rules/${id}/`, {
      headers: this.headers,
    });
    return response.json();
  }

  async updateServiceRule(id: string, data: UpdateServiceRuleRequest): Promise<BaseResponse<ServiceRule>> {
    const response = await fetch(`${this.baseUrl}/api/planner/service-rules/${id}/`, {
      method: 'PUT',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async patchServiceRule(id: string, data: Partial<UpdateServiceRuleRequest>): Promise<BaseResponse<ServiceRule>> {
    const response = await fetch(`${this.baseUrl}/api/planner/service-rules/${id}/`, {
      method: 'PATCH',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async deleteServiceRule(id: string): Promise<void> {
    await fetch(`${this.baseUrl}/api/planner/service-rules/${id}/`, {
      method: 'DELETE',
      headers: this.headers,
    });
  }

  // Time-offs
  async listTimeOffs(params?: TimeOffListParams): Promise<PaginatedResponse<PlannerTimeOff>> {
    const url = new URL(`${this.baseUrl}/api/planner/time-offs/`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) url.searchParams.append(key, String(value));
      });
    }
    const response = await fetch(url.toString(), { headers: this.headers });
    return response.json();
  }

  async createTimeOff(data: CreateTimeOffRequest): Promise<BaseResponse<PlannerTimeOff>> {
    const response = await fetch(`${this.baseUrl}/api/planner/time-offs/`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async getTimeOff(id: string): Promise<BaseResponse<PlannerTimeOff>> {
    const response = await fetch(`${this.baseUrl}/api/planner/time-offs/${id}/`, {
      headers: this.headers,
    });
    return response.json();
  }

  async updateTimeOff(id: string, data: UpdateTimeOffRequest): Promise<BaseResponse<PlannerTimeOff>> {
    const response = await fetch(`${this.baseUrl}/api/planner/time-offs/${id}/`, {
      method: 'PUT',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async patchTimeOff(id: string, data: Partial<UpdateTimeOffRequest>): Promise<BaseResponse<PlannerTimeOff>> {
    const response = await fetch(`${this.baseUrl}/api/planner/time-offs/${id}/`, {
      method: 'PATCH',
      headers: this.headers,
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async deleteTimeOff(id: string): Promise<void> {
    await fetch(`${this.baseUrl}/api/planner/time-offs/${id}/`, {
      method: 'DELETE',
      headers: this.headers,
    });
  }

  // Utilities
  async getPlannerChoices(): Promise<BaseResponse<PlannerChoices>> {
    const response = await fetch(`${this.baseUrl}/api/planner/utilities/choices/`, {
      headers: this.headers,
    });
    return response.json();
  }

  async getOrganizationPlanner(organizationId: string): Promise<BaseResponse<OrganizationPlanner>> {
    const response = await fetch(`${this.baseUrl}/api/planner/utilities/organization/${organizationId}/`, {
      headers: this.headers,
    });
    return response.json();
  }

  async getLocationPlanner(locationId: string): Promise<BaseResponse<LocationPlanner>> {
    const response = await fetch(`${this.baseUrl}/api/planner/utilities/location/${locationId}/`, {
      headers: this.headers,
    });
    return response.json();
  }

  async getStationPlanner(stationId: string): Promise<BaseResponse<LocationStationPlanner>> {
    const response = await fetch(`${this.baseUrl}/api/planner/utilities/station/${stationId}/`, {
      headers: this.headers,
    });
    return response.json();
  }
}

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

export const ValidationHelpers = {
  isValidTime: (time: string): boolean => {
    return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
  },

  isValidDate: (date: string): boolean => {
    return /^\d{4}-\d{2}-\d{2}$/.test(date) && !isNaN(Date.parse(date));
  },

  isValidWeekday: (weekday: number): boolean => {
    return Number.isInteger(weekday) && weekday >= 0 && weekday <= 6;
  },

  isValidPriority: (priority: number): boolean => {
    return Number.isInteger(priority) && priority >= 1;
  },

  isValidFrequency: (frequency: number): boolean => {
    return Number.isInteger(frequency) && frequency >= 1 && frequency <= 1000;
  },

  isValidRecurrenceInterval: (interval: number): boolean => {
    return Number.isInteger(interval) && interval >= 1 && interval <= 52;
  },

  validateTimeRange: (startTime: string, endTime: string): boolean => {
    if (!ValidationHelpers.isValidTime(startTime) || !ValidationHelpers.isValidTime(endTime)) {
      return false;
    }
    return startTime < endTime;
  },

  validateDateRange: (startDate: string, endDate: string): boolean => {
    if (!ValidationHelpers.isValidDate(startDate) || !ValidationHelpers.isValidDate(endDate)) {
      return false;
    }
    return new Date(startDate) <= new Date(endDate);
  }
};

// ============================================================================
// USAGE EXAMPLES
// ============================================================================

/*
// Initialize the API client
const plannerApi = new PlannerApiClient('https://api.migranium.com', 'your-auth-token');

// List all planners
const planners = await plannerApi.listPlanners({
  is_active: true,
  target_type: TargetType.ORGANIZATION,
  include: 'time_offs,service_rules'
});

// Create a new category rule
const categoryRule = await plannerApi.createCategoryRule({
  client_category: 'uuid-here',
  rule_name: 'Weekend Restrictions',
  preference_type: PreferenceType.RESTRICTION,
  weekdays: [5, 6], // Saturday, Sunday
  is_all_day: true,
  occurrence_pattern: OccurrencePattern.ROLLING
});

// Get planner choices for dropdowns
const choices = await plannerApi.getPlannerChoices();

// Validate time input
const isValid = ValidationHelpers.isValidTime('09:30'); // true
const isValidRange = ValidationHelpers.validateTimeRange('09:00', '17:00'); // true
*/