import { apiClient } from "./clients";
import type {
	BusinessAttributesResponse,
	BusinessAttributesFilters,
	ConditionalAttributesResponse,
} from "@/types/businessAttributes";

export interface BulkUpdateAttributeData {
	title_on_migranium: string;
	type: string;
	is_validator: boolean;
	import_column_name: string;
}

export interface BulkUpdateAttributesResponse {
	success: boolean;
	message: string;
}

export interface AttributeTypeConfig {
	label: string;
	key: string;
	has_options: boolean;
}

export interface AttributeTypeConfigsResponse {
	data: AttributeTypeConfig[];
}

export interface ClientAttributeOption {
	id: number;
	label: string;
}

export interface ClientAttributeSetting {
	id: number;
	label: string;
	type: string;
	is_required: boolean;
	is_validator: boolean;
	is_system_field: boolean;
	is_emr_field: boolean;
	options: ClientAttributeOption[] | null;
}

export interface ClientAttributeSettingsResponse {
	data: ClientAttributeSetting[];
}

export interface UpdateClientAttributeSettingsRequest {
	data: {
		id: number | null;
		label: string;
		type: string;
		is_required: boolean;
		is_validator: boolean;
		options?: {
			id: number | null;
			label: string;
			order?: number;
		}[];
	}[];
}

export interface UpdateClientAttributeSettingsResponse {
	success: boolean;
	message: string;
	data: ClientAttributeSetting[];
}

const BUSINESS_ATTRIBUTES_ENDPOINTS = {
	base: "/api/v1/client-attributes",
	conditional: "/api/v1/client-attributes/conditional",
	bulkUpdate: "/api/v1/client-attributes/bulk-update",
	typeConfigs: "/api/v1/client-attributes/type-configs",
	settings: "/api/v1/client-attributes/settings",
	updateAll: "/api/v1/client-attributes/update-all",
} as const;

export const businessAttributesApi = {
	getBusinessAttributes: async (
		filters: BusinessAttributesFilters = {},
		organizationId?: number
	): Promise<BusinessAttributesResponse> => {
		const params = new URLSearchParams();
		if (filters.page !== undefined) {
			params.append("page", filters.page.toString());
		}
		if (filters.per_page !== undefined) {
			params.append("per_page", filters.per_page.toString());
		}
		if (filters.search) {
			params.append("search", filters.search);
		}
		if (filters.type) {
			params.append("type", filters.type);
		}
		if (filters.is_required !== undefined) {
			params.append("is_required", filters.is_required.toString());
		}
		if (filters.show_in_list !== undefined) {
			params.append("show_in_list", filters.show_in_list.toString());
		}
		const queryString = params.toString();
		const url = queryString
			? `${BUSINESS_ATTRIBUTES_ENDPOINTS.base}?${queryString}`
			: BUSINESS_ATTRIBUTES_ENDPOINTS.base;

		const headers: Record<string, any> = {};
		if (organizationId) {
			headers["X-organizationId"] = organizationId;
		}

		const response = await apiClient.get(url, { headers });
		return response.data;
	},

	getConditionalAttributes: async (
		organizationId: number
	): Promise<ConditionalAttributesResponse> => {
		const params = new URLSearchParams();
		params.append("X-organizationId", organizationId.toString());

		const response = await apiClient.get(
			`${BUSINESS_ATTRIBUTES_ENDPOINTS.conditional}?${params}`
		);
		return response.data;
	},

	getAttributeTypeConfigs: async (
		organizationId: number
	): Promise<AttributeTypeConfigsResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId.toString(),
		};

		const response = await apiClient.get(
			BUSINESS_ATTRIBUTES_ENDPOINTS.typeConfigs,
			{ headers }
		);
		return response.data;
	},

	bulkUpdateAttributes: async (
		data: BulkUpdateAttributeData[],
		organizationId: number
	): Promise<BulkUpdateAttributesResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId.toString(),
		};

		const response = await apiClient.post(
			BUSINESS_ATTRIBUTES_ENDPOINTS.bulkUpdate,
			data,
			{ headers }
		);
		return response.data;
	},

	getClientAttributeSettings: async (
		organizationId: number
	): Promise<ClientAttributeSettingsResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId.toString(),
		};

		const response = await apiClient.get(
			BUSINESS_ATTRIBUTES_ENDPOINTS.settings,
			{ headers }
		);
		return response.data;
	},

	updateClientAttributeSettings: async (
		data: UpdateClientAttributeSettingsRequest,
		organizationId: number
	): Promise<UpdateClientAttributeSettingsResponse> => {
		const headers: Record<string, any> = {
			"X-organizationId": organizationId.toString(),
		};

		const response = await apiClient.post(
			BUSINESS_ATTRIBUTES_ENDPOINTS.updateAll,
			data,
			{ headers }
		);
		return response.data;
	},
};
