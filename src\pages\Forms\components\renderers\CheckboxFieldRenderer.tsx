import React from "react";
import type { FormField } from "../../types/Form";

type CheckboxFieldRendererProps = {
	field: FormField;
	value: any;
	mode: "preview" | "view" | "edit";
	onChange?: (value: any) => void;
};

export const CheckboxFieldRenderer: React.FC<CheckboxFieldRendererProps> = ({
	field,
	value = [],
	mode,
	onChange,
}) => {
	if (mode === "view") {
		const checked = field.options
			?.filter((opt) => value?.includes(opt.id))
			.map((opt) => opt.value)
			.join(", ") || <span className="text-slate-400">No response</span>;
		return <div>{checked}</div>;
	}
	return (
		<div className="ml-3 flex flex-col gap-3">
			{field.options?.map((opt) => (
				<label key={opt.id} className="flex items-center gap-2">
					<input
						type="checkbox"
						checked={value?.includes(opt.id)}
						disabled={mode !== "edit"}
						onChange={
							mode === "edit" && onChange
								? (e) => {
										if (e.target.checked) {
											onChange([...value, opt.id]);
										} else {
											onChange(
												value.filter(
													(v: string) => v !== opt.id
												)
											);
										}
									}
								: undefined
						}
					/>
					<span className="text-xs font-medium text-[#27272A]">
						{opt.value}
					</span>
				</label>
			))}
		</div>
	);
};
