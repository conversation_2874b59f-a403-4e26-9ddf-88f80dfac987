import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Pencil } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Settings } from "lucide-react";
import { changePassword } from "@/lib/api/auth";
import { toast } from "sonner";
import { useProfile, useUpdateProfile } from "@/hooks/useProfile";

export function AccountSettingsContent() {
	const [showProfileUpdate, setShowProfileUpdate] = useState(false);
	const [showPasswordUpdate, setShowPasswordUpdate] = useState(false);

	const [emailAuthEnabled, setEmailAuthEnabled] = useState(true);
	const [thirdPartyAuthEnabled, setThirdPartyAuthEnabled] = useState(false);

	// Profile hooks
	const { data: profileData, isLoading: profileLoading } = useProfile();
	const updateProfileMutation = useUpdateProfile();

	// Profile form state
	const [profileForm, setProfileForm] = useState({
		firstName: "",
		lastName: "",
		email: "",
		phoneNumber: "",
	});
	const [isUpdatingProfile, setIsUpdatingProfile] = useState(false);

	// Password form state
	const [passwordForm, setPasswordForm] = useState({
		currentPassword: "",
		newPassword: "",
		confirmPassword: "",
	});
	const [isChangingPassword, setIsChangingPassword] = useState(false);

	// Load profile data when available
	useEffect(() => {
		if (profileData?.data) {
			setProfileForm({
				firstName: profileData.data.first_name || "",
				lastName: profileData.data.last_name || "",
				email: profileData.data.email || "",
				phoneNumber: profileData.data.phone_number || "",
			});
		}
	}, [profileData]);

	// Handle profile form input changes
	const handleProfileChange = (field: keyof typeof profileForm) => (
		e: React.ChangeEvent<HTMLInputElement>
	) => {
		setProfileForm(prev => ({
			...prev,
			[field]: e.target.value
		}));
	};

	// Handle profile update submission
	const handleUpdateProfile = async () => {
		// Basic validation
		if (!profileForm.firstName || !profileForm.lastName || !profileForm.phoneNumber) {
			toast.error("First name, last name, and phone number are required");
			return;
		}

		setIsUpdatingProfile(true);

		try {
			await updateProfileMutation.mutateAsync({
				first_name: profileForm.firstName,
				last_name: profileForm.lastName,
				phone_number: profileForm.phoneNumber,
				// profile_picture can be added later when file upload is implemented
			});

			// Hide the update section on success
			setShowProfileUpdate(false);

		} catch (error) {
			// Error handling is done in the mutation
		} finally {
			setIsUpdatingProfile(false);
		}
	};

	// Handle password form input changes
	const handlePasswordChange = (field: keyof typeof passwordForm) => (
		e: React.ChangeEvent<HTMLInputElement>
	) => {
		setPasswordForm(prev => ({
			...prev,
			[field]: e.target.value
		}));
	};

	// Handle password change submission
	const handleChangePassword = async () => {
		// Basic validation
		if (!passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword) {
			toast.error("All password fields are required");
			return;
		}

		if (passwordForm.newPassword !== passwordForm.confirmPassword) {
			toast.error("New passwords do not match");
			return;
		}

		if (passwordForm.newPassword.length < 8) {
			toast.error("Password must be at least 8 characters long");
			return;
		}

		setIsChangingPassword(true);

		try {
			const response = await changePassword({
				current_password: passwordForm.currentPassword,
				new_password: passwordForm.newPassword,
				new_password_confirmation: passwordForm.confirmPassword,
			});

			toast.success(response.message || "Password changed successfully");

			// Reset form and hide update section
			setPasswordForm({
				currentPassword: "",
				newPassword: "",
				confirmPassword: "",
			});
			setShowPasswordUpdate(false);

		} catch (error: any) {
			const errorMessage = error?.response?.data?.message || "Failed to change password";
			toast.error(errorMessage);
		} finally {
			setIsChangingPassword(false);
		}
	};

	return (
		<div className="flex w-full flex-col gap-8">
			{/* Heading & Description */}
			<div className="w-full border-b border-b-[#E4E4E7] pb-3">
				<h1 className="text-left text-2xl font-bold">
					Account Settings
				</h1>
				<p className="text-muted-foreground mt-1 text-sm">
					Change or update your account information.
				</p>
			</div>

			{/* Profile Information */}
			<section className="flex flex-col gap-4 border-b border-b-[#E4E4E7] pb-8">
				<div className="flex w-full items-center justify-between">
					<h2 className="text-xl font-semibold">
						Profile Information
					</h2>
					<Button
						variant="outline"
						size="sm"
						onClick={() => setShowProfileUpdate((prev) => !prev)}
						className="flex cursor-pointer items-center gap-2"
						disabled={profileLoading}
					>
						<Pencil className="h-4 w-4" />
						Edit
					</Button>
				</div>

				<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
					<div>
						<Label htmlFor="firstName" className="pb-2">
							First Name *
						</Label>
						<Input
							id="firstName"
							placeholder="John"
							className="h-10 border-[#E4E4E7]"
							value={profileForm.firstName}
							onChange={handleProfileChange('firstName')}
							disabled={profileLoading}
						/>
					</div>
					<div>
						<Label htmlFor="lastName" className="pb-2">
							Last Name *
						</Label>
						<Input
							id="lastName"
							placeholder="Doe"
							className="h-10 border-[#E4E4E7]"
							value={profileForm.lastName}
							onChange={handleProfileChange('lastName')}
							disabled={profileLoading}
						/>
					</div>
				</div>

				<div className="mt-6 grid grid-cols-1 gap-6">
					<div>
						<Label htmlFor="email" className="pb-2">
							Email *
						</Label>
						<Input
							id="email"
							type="email"
							placeholder="<EMAIL>"
							className="h-10 border-[#E4E4E7]"
							value={profileForm.email}
							disabled={true} // Email cannot be updated
						/>
						<p className="text-muted-foreground mt-1 text-xs">
							Email cannot be changed
						</p>
					</div>
					<div>
						<Label htmlFor="phone" className="pb-2">
							Phone *
						</Label>
						<Input
							id="phone"
							type="tel"
							placeholder="+234 ************"
							className="h-10 border-[#E4E4E7]"
							value={profileForm.phoneNumber}
							onChange={handleProfileChange('phoneNumber')}
							disabled={profileLoading}
						/>
					</div>
				</div>

				{showProfileUpdate && (
					<Button
						className="mt-4 self-start"
						onClick={handleUpdateProfile}
						disabled={isUpdatingProfile || profileLoading}
					>
						{isUpdatingProfile ? "Updating..." : "Update Profile"}
					</Button>
				)}
			</section>

			{/* Password Section */}
			<section className="flex flex-col gap-4 border-b border-b-[#E4E4E7] pb-8">
				<div className="flex w-full items-center justify-between">
					<h2 className="text-xl font-semibold">Password</h2>
					<Button
						variant="outline"
						size="sm"
						onClick={() => setShowPasswordUpdate((prev) => !prev)}
						className="flex cursor-pointer items-center gap-2"
					>
						<Pencil className="h-4 w-4" />
						Edit
					</Button>
				</div>

				<div className="grid grid-cols-1 gap-6">
					<div>
						<Label htmlFor="currentPassword" className="pb-2">
							Current Password *
						</Label>
						<Input
							id="currentPassword"
							type="password"
							className="h-10 border-[#E4E4E7]"
							value={passwordForm.currentPassword}
							onChange={handlePasswordChange('currentPassword')}
						/>
					</div>
					<div>
						<Label htmlFor="newPassword" className="pb-2">
							New Password *
						</Label>
						<Input
							id="newPassword"
							type="password"
							className="h-10 border-[#E4E4E7]"
							value={passwordForm.newPassword}
							onChange={handlePasswordChange('newPassword')}
						/>
						<p className="text-muted-foreground mt-2 text-xs">
							Password must be 8+ characters with an uppercase
							letter, lowercase letter, number, and special
							character.
						</p>
					</div>
					<div>
						<Label htmlFor="confirmPassword" className="pb-2">
							Confirm New Password *
						</Label>
						<Input
							id="confirmPassword"
							type="password"
							className="h-10 border-[#E4E4E7]"
							value={passwordForm.confirmPassword}
							onChange={handlePasswordChange('confirmPassword')}
						/>
					</div>
				</div>

				{showPasswordUpdate && (
					<Button
						className="mt-4 cursor-pointer self-start"
						variant="outline"
						onClick={handleChangePassword}
						disabled={isChangingPassword}
					>
						{isChangingPassword ? "Changing..." : "Change Password"}
					</Button>
				)}
			</section>

			{/* Authentication Section */}
			<section className="flex flex-col gap-6">
				{/* Section Heading */}
				<div className="flex w-full flex-col gap-1 border-b border-b-[#E4E4E7] pb-3">
					<h2 className="text-xl font-semibold">Authentication</h2>
					<p className="text-muted-foreground text-sm">
						Manage how your account is verified and accessed.
					</p>
				</div>

				{/* Email Authentication */}
				<div className="flex items-center justify-between">
					<div>
						<h3 className="text-sm font-medium">
							Email Authentication
						</h3>
						<p className="text-muted-foreground mt-1 text-xs">
							Securely verify your account using your email.
						</p>
					</div>
					<Switch
						checked={emailAuthEnabled}
						onCheckedChange={setEmailAuthEnabled}
						className="cursor-pointer"
					/>
				</div>

				{/* Third Party Auth */}
				<div className="flex items-center justify-between">
					<div>
						<h3 className="text-sm font-medium">
							Third-Party Authentication
						</h3>
						<p className="text-muted-foreground mt-1 text-xs">
							Authenticate via a one-time password from a trusted
							third-party service.
						</p>
					</div>
					<Switch
						checked={thirdPartyAuthEnabled}
						onCheckedChange={setThirdPartyAuthEnabled}
						className="cursor-pointer"
					/>
				</div>

				{/* Reconfigure OTP */}
				<div className="flex items-center justify-between">
					<div>
						<h3 className="text-sm font-medium">Reconfigure OTP</h3>
						<p className="text-muted-foreground mt-1 text-xs">
							Update or reset your one-time password
							configuration.
						</p>
					</div>
					<Button
						variant="outline"
						size="sm"
						className="flex items-center cursor-pointer gap-2"
					>
						<Settings className="h-4 w-4" />
						Reconfigure
					</Button>
				</div>

				{/* Close Account Warning */}

			</section>
		</div>
	);
}
