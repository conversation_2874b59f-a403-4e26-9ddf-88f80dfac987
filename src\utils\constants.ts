import type { AnalyticMetric } from "@/pages/Analytics/MetricCard";

export const analyticsInitialMetrics: AnalyticMetric[] = [
	{
		title: "Total Visits",
		value: "0",
		ratios: [],
		progress: 0,
		colors: {
			dark: "#6366F1",
			dim: "#E2E8F0",
		},
		legend: [
			{ label: "Returning", color: "#6366F1" },
			{ label: "New", color: "#E2E8F0" },
		],
	},
	{
		title: "Average Time Spent",
		value: "0",
		progress: 0,
		ratios: [],
		colors: {
			dark: "#0D9488",
			dim: "#E5F7F6",
		},
		legend: [
			{ label: "Serving", color: "#0D9488" },
			{ label: "Waiting", color: "#E5F7F6" },
		],
	},
	{
		title: "Cancellation Rate",
		value: "0%",
		progress: 0,
		ratios: [],
		colors: {
			dark: "#E11D48",
			dim: "#FEE2E2",
		},
		legend: [
			{ label: "By Admin", color: "#E11D48" },
			{ label: "By User", color: "#FEE2E2" },
		],
	},
	{
		title: "Average Utilization Rate",
		value: "0%",
		progress: 0,
		ratios: [],
		colors: {
			dark: "#22C55E",
			dim: "#DCFCE7",
		},
		legend: [
			{ label: "Utilized", color: "#22C55E" },
			{ label: "Available", color: "#DCFCE7" },
		],
	},
];