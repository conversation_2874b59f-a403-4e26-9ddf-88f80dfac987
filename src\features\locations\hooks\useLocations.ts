import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { locationsApi } from "../api/locationsApi";
import type { LocationsFilters, CreateLocationRequest, UpdateLocationRequest } from "../types";
import type {
	UpdateOperatingHoursRequest,
	LocationOperatingHoursResponse,
	UpdateScheduleSettingsRequest,
	LocationScheduleSettingsResponse,
	UpdateScheduleOptimizerRequest,
	LocationScheduleOptimizerResponse
} from "@/features/organizations/api/organizationsApi";
import { toast } from "sonner";

export function useLocations(filters: LocationsFilters = {}, organizationId?: number) {
	return useQuery({
		queryKey: ["locations", organizationId, filters],
		queryFn: () => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}
			return locationsApi.getLocations(filters, organizationId);
		},
		enabled: !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
}

export function useLocation(locationId: string, enabled = true) {
	// This function needs organization context, but we'll handle that in the component
	return useQuery({
		queryKey: ["location", locationId],
		queryFn: async () => {
			// We'll need to get organizationId from context in the component
			throw new Error("useLocation needs to be called with organizationId");
		},
		enabled: enabled && !!locationId,
		staleTime: 5 * 60 * 1000,
		refetchOnWindowFocus: false,
	});
}

export function useCreateLocation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ data, organizationId }: { data: CreateLocationRequest; organizationId: number }) =>
			locationsApi.createLocation(data, organizationId),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["locations"] });
			toast.success("Location created successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to create location");
		},
	});
}

export function useUpdateLocation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ data, organizationId }: { data: UpdateLocationRequest; organizationId: number }) =>
			locationsApi.updateLocation(data, organizationId),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["locations"] });
			toast.success("Location updated successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update location");
		},
	});
}

export function useDeleteLocation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ locationId, organizationId }: { locationId: string; organizationId: number }) =>
			locationsApi.deleteLocation(locationId, organizationId),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["locations"] });
			toast.success("Location deleted successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to delete location");
		},
	});
}

// Location Operating Hours hooks
export function useLocationOperatingHours(
	locationId: string | null,
	organizationId: number | null,
	enabled = true
) {
	return useQuery({
		queryKey: ["location-operating-hours", locationId, organizationId],
		queryFn: () => {
			if (!locationId || !organizationId) {
				throw new Error("Location ID and Organization ID are required");
			}
			return locationsApi.getLocationOperatingHours(locationId, organizationId);
		},
		enabled: enabled && !!locationId && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
}

export function useUpdateLocationOperatingHours() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			locationId,
			organizationId,
			data,
		}: {
			locationId: string;
			organizationId: number;
			data: UpdateOperatingHoursRequest;
		}) => locationsApi.updateLocationOperatingHours(locationId, organizationId, data),
		onSuccess: (_, variables) => {
			// Invalidate the specific location operating hours query
			queryClient.invalidateQueries({
				queryKey: ["location-operating-hours", variables.locationId, variables.organizationId],
			});
			toast.success("Location operating hours updated successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update location operating hours");
		},
	});
}

// Location Schedule Settings hooks
export function useLocationScheduleSettings(locationId: string | null, organizationId: number, enabled = true) {
	return useQuery({
		queryKey: ["locationScheduleSettings", locationId, organizationId],
		queryFn: () => {
			if (!locationId || !organizationId) {
				throw new Error("Location ID and Organization ID are required");
			}
			return locationsApi.getLocationScheduleSettings(locationId, organizationId);
		},
		enabled: enabled && !!locationId && !!organizationId,
		staleTime: 5 * 60 * 1000,
		refetchOnWindowFocus: false,
	});
}

export function useUpdateLocationScheduleSettings() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			locationId,
			organizationId,
			data,
		}: {
			locationId: string;
			organizationId: number;
			data: UpdateScheduleSettingsRequest;
		}) => locationsApi.updateLocationScheduleSettings(locationId, organizationId, data),
		onSuccess: (_, { locationId, organizationId }) => {
			// Invalidate and refetch location schedule settings
			queryClient.invalidateQueries({
				queryKey: ["locationScheduleSettings", locationId, organizationId],
			});
			toast.success("Location schedule settings updated successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update location schedule settings");
		},
	});
}

// Location Schedule Optimizer hooks
export function useLocationScheduleOptimizer(locationId: string | null, organizationId: number, enabled = true) {
	return useQuery({
		queryKey: ["locationScheduleOptimizer", locationId, organizationId],
		queryFn: () => {
			if (!locationId || !organizationId) {
				throw new Error("Location ID and Organization ID are required");
			}
			return locationsApi.getLocationScheduleOptimizer(locationId, organizationId);
		},
		enabled: enabled && !!locationId && !!organizationId,
		staleTime: 5 * 60 * 1000,
		refetchOnWindowFocus: false,
	});
}

export function useUpdateLocationScheduleOptimizer() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			locationId,
			organizationId,
			data,
		}: {
			locationId: string;
			organizationId: number;
			data: UpdateScheduleOptimizerRequest;
		}) => locationsApi.updateLocationScheduleOptimizer(locationId, organizationId, data),
		onSuccess: (_, { locationId, organizationId }) => {
			// Invalidate and refetch location schedule optimizer
			queryClient.invalidateQueries({
				queryKey: ["locationScheduleOptimizer", locationId, organizationId],
			});
			toast.success("Location schedule optimizer updated successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update location schedule optimizer");
		},
	});
}
