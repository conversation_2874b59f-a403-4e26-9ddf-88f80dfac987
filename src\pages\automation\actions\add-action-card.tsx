import { Button } from "@/components/ui/Button/Button";
import clsx from "clsx";
import { X } from "lucide-react";
import { onDragStart } from "../libs/editor-utils"
import { <PERSON><PERSON><PERSON>, LuMousePointerClick, LuSearchCheck } from "react-icons/lu";
import { PiGitForkBold } from "react-icons/pi";
import type { EditorCanvasTypes } from "../libs/type";
import { useState } from "react";
import { useEffect } from "react";

type AddActionCardProps = {
    open: boolean;
    onClose?: () => void;
};

const actionCards = [
    {
        title: "Condition",
        description: "Select the trigger that start the automation.",
        icon: <PiGitForkBold color="#B45309" size={20} />
    },
    {
        title: "IF Match",
        description: "Select the action that will perform if the condition is met.",
        icon: <LuMousePointerClick color="#289144" size={20} />
    },
    {
        title: "Else Match",
        description: "Select the action that will perform if the condition is not met.",
        icon: <LuMousePointerClick color="#289144" size={20} />
    },
    {
        title: "Check",
        description: "Select the action that will perform if the condition is not met.",
        icon: <LuSearchCheck color="#228579" size={20} />
    }
]

export default function AddActionCard({ open, onClose }: AddActionCardProps) {
    const [fullyOpen, setFullyOpen] = useState(false);

    useEffect(() => {
        if (open) {
            setTimeout(() => {
                setFullyOpen(open);
            }, 10);
        }
    }, [open]);

    return (
        <div className={clsx("z-50 fixed top-5 right-[4%] w-[30rem] bg-white border border-[#00589340] shadow-[0px_2px_4px_-1px_#0000000F,0px_0px_6px_-1px_#0000001A] transition-transform duration-300 ease-in-out will-change-transform rounded-xl pt-5 pb-1", fullyOpen ? "translate-x-0" : "translate-x-[115%]")}>
            <div>
                <div className="flex items-center justify-between">
                    <h1 className="text-[#27272A] text-xl font-semibold ml-6">Add Action Card</h1>
                    <Button
                        variant="ghost"
                        className="!px-0 w-11 h-10.5 rounded-lg cursor-pointer mr-3" onClick={onClose}>
                        <X className="text-base" color="#27272A" />
                    </Button>
                </div>
                <p className="text-[#71717A] text-sm font-light ml-6">
                    Select the action you want to add to the automation.
                </p>
            </div>

            <div className="min-h-[78vh] max-h-[78vh] h-full flex flex-col justify-between px-5 mt-9 overflow-y-auto">
                <div className="flex flex-col gap-y-6">
                    {actionCards.map((card, index) => (
                        <button
                            key={index}
                            className={clsx(
                                "bg-white rounded-xl min-w-[280px] max-w-[280px] pt-3 pb-1.5 px-1.5",
                                false && "border-2 border-[#00589340] shadow-[0px_1px_2px_0px_#0000000F,0px_1px_3px_0px_#0000001A]"
                            )}
                            draggable
                            style={{
                                boxShadow: "0px 1px 2px 0px #0000000F, 0px 1px 3px 0px #0000001A",
                            }}
                            onDragStart={(event) =>
                                onDragStart(event, card.title as EditorCanvasTypes)
                            }
                        >
                            <div className="flex items-center justify-between mb-4 px-2.5">
                                <div className="flex items-center gap-2">
                                    {card.icon}
                                    <span className="text-[#27272A] text-base font-normal">{card.title}</span>
                                </div>
                                <Button variant="outline" size="icon" className="h-9 w-9 rounded-lg cursor-pointer border-[#E4E4E7]">
                                    <LuCopy color="#A1A1AA" size={16} />
                                </Button>
                            </div>
                            <div className="bg-[#F4F4F5] py-2.5 px-3.5 rounded-lg">
                                <p className="text-[#71717A] text-sm font-light text-start">{card.description}</p>
                            </div>
                        </button>
                    ))}

                </div>
            </div>

        </div>
    )
}