import type { AutomationHistoryTableRowType, TableRowType } from "../schema/table";

export const dummyAutomationHistoryData: AutomationHistoryTableRowType[] = [
    {
        patient: "<PERSON>",
        email: "<EMAIL>",
        action: "Follow-up Consultation",
        runTime: "16 May 2020 9:30 am",
        status: "Successful"
    },
    {
        patient: "<PERSON>",
        email: "<EMAIL>",
        action: "Routine Checkup",
        runTime: "17 May 2020 11:00 am",
        status: "Successful"
    },
    {
        patient: "<PERSON>",
        email: "<EMAIL>",
        action: "Therapy Session",
        runTime: "18 May 2020 1:15 pm",
        status: "Successful"
    },
    {
        patient: "<PERSON>",
        email: "<EMAIL>",
        action: "Initial Consultation",
        runTime: "19 May 2020 3:45 pm",
        status: "Failed"
    },
    {
        patient: "<PERSON>",
        email: "<EMAIL>",
        action: "Dental Appointment",
        runTime: "20 May 2020 10:00 am",
        status: "Successful"
    },
    {
        patient: "<PERSON>",
        email: "micha<PERSON>.<EMAIL>",
        action: "Nutrition Advice",
        runTime: "21 May 2020 2:30 pm",
        status: "Failed"
    },
    {
        patient: "<PERSON>",
        email: "<EMAIL>",
        action: "Fitness Assessment",
        runTime: "22 May 2020 4:00 pm",
        status: "Successful"
    },
    {
        patient: "David Garcia",
        email: "<EMAIL>",
        action: "Physiotherapy Session",
        runTime: "23 May 2020 5:30 pm",
        status: "Successful"
    },
    {
        patient: "Sophia Martinez",
        email: "<EMAIL>",
        action: "Annual Checkup",
        runTime: "24 May 2020 7:00 am",
        status: "Failed"
    },
    {
        patient: "Liam Anderson",
        email: "<EMAIL>",
        action: "Eye Examination",
        runTime: "25 May 2020 12:00 pm",
        status: "Successful"
    }
];

export const conditionCategories = [
    "Fruit",
    "Immunization",
    "Gen Z",
    "Millennium",
    "Report",
    "Technology",
    "Travel",
    "Fitness",
    "Finance",
    "Education"
]

export const dummyTableData: TableRowType[] = [
    {
        id: "1",
        title: "Daily Backup",
        status: "Active",
        createDate: "2024-06-01",
        successfulRuns: 12,
        failedRuns: 0,
        owner: "Alice",
        enabled: true,
        profilePicture: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=987&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    },
    {
        id: "2",
        title: "Weekly Report",
        status: "Active",
        createDate: "2024-05-25",
        successfulRuns: 4,
        failedRuns: 1,
        owner: "Bob",
        enabled: true,
    },
    {
        id: "3",
        title: "Data Sync",
        status: "Inactive",
        createDate: "2024-06-03",
        successfulRuns: 0,
        failedRuns: 3,
        owner: "Charlie",
        enabled: false,
    },
    {
        id: "4",
        title: "User Import",
        status: "Active",
        createDate: "2024-06-02",
        successfulRuns: 0,
        failedRuns: 0,
        owner: "Diana",
        enabled: true,
        profilePicture: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    },
    {
        id: "5",
        title: "Nightly Cleanup",
        status: "Active",
        createDate: "2024-05-30",
        successfulRuns: 10,
        failedRuns: 2,
        owner: "Eve",
        enabled: false,
    },
    {
        id: "6",
        title: "Monthly Audit",
        status: "Inactive",
        createDate: "2024-05-01",
        successfulRuns: 1,
        failedRuns: 0,
        owner: "Frank",
        enabled: true,
    },
    {
        id: "7",
        title: "API Health Check",
        status: "Active",
        createDate: "2024-06-04",
        successfulRuns: 20,
        failedRuns: 0,
        owner: "Grace",
        enabled: true,
    },
    {
        id: "8",
        title: "Export Data",
        status: "Inactive",
        createDate: "2024-05-28",
        successfulRuns: 2,
        failedRuns: 1,
        owner: "Heidi",
        enabled: false,
    },
    {
        id: "9",
        title: "Security Scan",
        status: "Inactive",
        createDate: "2024-06-01",
        successfulRuns: 5,
        failedRuns: 0,
        owner: "Ivan",
        enabled: true,
        profilePicture: "https://images.unsplash.com/photo-1580489944761-15a19d654956?q=80&w=1061&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    },
    {
        id: "10",
        title: "Log Rotation",
        status: "Active",
        createDate: "2024-06-05",
        successfulRuns: 7,
        failedRuns: 0,
        owner: "Judy",
        enabled: true,
    },
];
