import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getProfile, updateProfile } from '@/lib/api/auth';
import type { ProfileData, UpdateProfileRequest } from '@/types/api/auth';
import { useAuthStore } from '@/stores/authStore';
import { toast } from 'sonner';

// Hook to fetch user profile
export const useProfile = () => {
  const { isAuthenticated, token } = useAuthStore();

  return useQuery({
    queryKey: ['profile'],
    queryFn: getProfile,
    enabled: isAuthenticated && !!token,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

// Hook to update user profile
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  const { updateUser } = useAuthStore();

  return useMutation({
    mutationFn: updateProfile,
    onSuccess: (response) => {
      // Update the cached profile data
      queryClient.setQueryData(['profile'], response);

      // Update the auth store with new profile data
      updateUser({
        name: response.data.name,
        email: response.data.email,
        // Add other fields that are compatible with AuthUserData
      });

      toast.success(response.message || 'Profile updated successfully');
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || 'Failed to update profile';
      toast.error(errorMessage);
    },
  });
};

// Hook to fetch profile on login success
export const useFetchProfileOnLogin = () => {
  const queryClient = useQueryClient();

  const fetchProfile = async () => {
    try {
      const response = await getProfile();
      queryClient.setQueryData(['profile'], response);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch profile on login:', error);
      return null;
    }
  };

  return { fetchProfile };
};