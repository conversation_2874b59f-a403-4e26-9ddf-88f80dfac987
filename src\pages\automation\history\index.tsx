import { Button } from "@/components/ui/Button/Button";
import { useUIStore } from "@/stores/uiStore";
import { useEffect } from "react";
import { useParams } from "react-router";
import { getAutomationHistoryColumns } from "./columns";
import AutomationHistoryTable from "./table";
import { dummyAutomationHistoryData } from "../dummy-data";

export default function AutomationHistory() {
    const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
    const setPhContent = useUIStore(
        (state) => state.setPageHeaderContent
    );
    const { automationId } = useParams();

    useEffect(() => {
        setBreadcrumbs([
            {
                label: "Automation",
                href: "/dashboard/automation",
                isCurrentPage: false,
            },
            {
                label: "Automation Run History",
                href: `/dashboard/automation/history/${automationId}`,
                isCurrentPage: true,
            },
        ]);
        return () => {
            setBreadcrumbs([]);
        };
    }, [setBreadcrumbs]);

    useEffect(() => {
        const headerContent = (
            <div className="flex flex-1 items-center justify-between">
                <h1 className="text-foreground text-2xl font-semibold">
                    [ Automation Title] History
                </h1>
                <Button variant="outline" className="cursor-pointer">
                    Automation Details
                </Button>
            </div>
        );

        setPhContent(headerContent);

        return () => {
            setPhContent(null);
        };
    }, []);

    const columns = getAutomationHistoryColumns();

    return (
        <div className="mt-10">
            <AutomationHistoryTable
                data={dummyAutomationHistoryData}
                columns={columns}
            />
        </div>
    );
}