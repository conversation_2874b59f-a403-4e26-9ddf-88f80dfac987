import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { stationsApi } from "../api/stationsApi";
import type {
	UpdateOperatingHoursRequest,
	LocationOperatingHoursResponse,
	UpdateScheduleSettingsRequest,
	LocationScheduleSettingsResponse,
	UpdateScheduleOptimizerRequest,
	LocationScheduleOptimizerResponse,
} from "@/features/organizations/api/organizationsApi";
import type { GetStationsResponse, StationData } from "../api/stationsApi";
import { toast } from "sonner";

interface UseStationsParams {
	locationId?: string;
	organizationId?: number;
	enabled?: boolean;
}

interface UseAllStationsParams {
	organizationId?: number;
	enabled?: boolean;
}

export function useStations({
	locationId,
	organizationId,
	enabled = true,
}: UseStationsParams) {
	return useQuery<GetStationsResponse, Error>({
		queryKey: ["stations", locationId, organizationId],
		queryFn: () => {
			if (!locationId || !organizationId) {
				throw new Error("LocationId and organizationId are required");
			}
			return stationsApi.getStations(locationId, organizationId);
		},
		enabled: enabled && !!locationId && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
}

interface UseStationParams {
	stationId?: string;
	organizationId?: number;
	enabled?: boolean;
}

export function useStation({
	stationId,
	organizationId,
	enabled = true,
}: UseStationParams) {
	return useQuery<
		{ success: boolean; message: string; data: StationData },
		Error
	>({
		queryKey: ["station", stationId, organizationId],
		queryFn: () => {
			if (!stationId || !organizationId) {
				throw new Error("StationId and organizationId are required");
			}
			return stationsApi.getStationById(stationId, organizationId);
		},
		enabled: enabled && !!stationId && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
}

export function useDeleteStation() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			stationId,
			organizationId,
		}: {
			stationId: string;
			organizationId: number;
		}) => stationsApi.deleteStation(stationId, organizationId),
		onSuccess: (_, { organizationId }) => {
			// Invalidate all stations queries for this organization
			queryClient.invalidateQueries({
				queryKey: ["stations"],
			});
			queryClient.invalidateQueries({
				queryKey: ["allStations", organizationId],
			});
			toast.success("Station deleted successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to delete station");
		},
	});
}

export function useAllStations({
	organizationId,
	enabled = true,
}: UseAllStationsParams) {
	return useQuery<GetStationsResponse, Error>({
		queryKey: ["allStations", organizationId],
		queryFn: () => {
			if (!organizationId) {
				throw new Error("OrganizationId is required");
			}
			return stationsApi.getAllStations(organizationId);
		},
		enabled: enabled && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
}

// Station Operating Hours hooks
export function useStationOperatingHours(
	locationId: string | null,
	stationId: string | null,
	organizationId: number | null,
	enabled = true
) {
	return useQuery({
		queryKey: [
			"station-operating-hours",
			locationId,
			stationId,
			organizationId,
		],
		queryFn: () => {
			if (!locationId || !stationId || !organizationId) {
				throw new Error(
					"Location ID, Station ID, and Organization ID are required"
				);
			}
			return stationsApi.getStationOperatingHours(
				locationId,
				stationId,
				organizationId
			);
		},
		enabled: enabled && !!locationId && !!stationId && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
}

export function useUpdateStationOperatingHours() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			locationId,
			stationId,
			organizationId,
			data,
		}: {
			locationId: string;
			stationId: string;
			organizationId: number;
			data: UpdateOperatingHoursRequest;
		}) =>
			stationsApi.updateStationOperatingHours(
				locationId,
				stationId,
				organizationId,
				data
			),
		onSuccess: (_, variables) => {
			// Invalidate the specific station operating hours query
			queryClient.invalidateQueries({
				queryKey: [
					"station-operating-hours",
					variables.locationId,
					variables.stationId,
					variables.organizationId,
				],
			});
			toast.success("Station operating hours updated successfully");
		},
		onError: (error: any) => {
			toast.error(
				error?.message || "Failed to update station operating hours"
			);
		},
	});
}

// Station Schedule Settings hooks
export function useStationScheduleSettings(
	locationId: string | null,
	stationId: string | null,
	organizationId: number,
	enabled = true
) {
	return useQuery({
		queryKey: [
			"stationScheduleSettings",
			locationId,
			stationId,
			organizationId,
		],
		queryFn: () => {
			if (!locationId || !stationId || !organizationId) {
				throw new Error(
					"Location ID, Station ID, and Organization ID are required"
				);
			}
			return stationsApi.getStationScheduleSettings(
				locationId,
				stationId,
				organizationId
			);
		},
		enabled: enabled && !!locationId && !!stationId && !!organizationId,
		staleTime: 5 * 60 * 1000,
		refetchOnWindowFocus: false,
	});
}

export function useUpdateStationScheduleSettings() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			locationId,
			stationId,
			organizationId,
			data,
		}: {
			locationId: string;
			stationId: string;
			organizationId: number;
			data: UpdateScheduleSettingsRequest;
		}) =>
			stationsApi.updateStationScheduleSettings(
				locationId,
				stationId,
				organizationId,
				data
			),
		onSuccess: (_, { locationId, stationId, organizationId }) => {
			// Invalidate and refetch station schedule settings
			queryClient.invalidateQueries({
				queryKey: [
					"stationScheduleSettings",
					locationId,
					stationId,
					organizationId,
				],
			});
			toast.success("Station schedule settings updated successfully");
		},
		onError: (error: any) => {
			toast.error(
				error?.message || "Failed to update station schedule settings"
			);
		},
	});
}

// Station Schedule Optimizer hooks
export function useStationScheduleOptimizer(
	locationId: string | null,
	stationId: string | null,
	organizationId: number,
	enabled = true
) {
	return useQuery({
		queryKey: [
			"stationScheduleOptimizer",
			locationId,
			stationId,
			organizationId,
		],
		queryFn: () => {
			if (!locationId || !stationId || !organizationId) {
				throw new Error(
					"Location ID, Station ID, and Organization ID are required"
				);
			}
			return stationsApi.getStationScheduleOptimizer(
				locationId,
				stationId,
				organizationId
			);
		},
		enabled: enabled && !!locationId && !!stationId && !!organizationId,
		staleTime: 5 * 60 * 1000,
		refetchOnWindowFocus: false,
	});
}

export function useUpdateStationScheduleOptimizer() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			locationId,
			stationId,
			organizationId,
			data,
		}: {
			locationId: string;
			stationId: string;
			organizationId: number;
			data: UpdateScheduleOptimizerRequest;
		}) =>
			stationsApi.updateStationScheduleOptimizer(
				locationId,
				stationId,
				organizationId,
				data
			),
		onSuccess: (_, { locationId, stationId, organizationId }) => {
			// Invalidate and refetch station schedule optimizer
			queryClient.invalidateQueries({
				queryKey: [
					"stationScheduleOptimizer",
					locationId,
					stationId,
					organizationId,
				],
			});
			toast.success("Station schedule optimizer updated successfully");
		},
		onError: (error: any) => {
			toast.error(
				error?.message || "Failed to update station schedule optimizer"
			);
		},
	});
}
