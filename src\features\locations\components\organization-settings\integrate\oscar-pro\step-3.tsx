import { Input } from "@/components/ui/input"
import addNewClientEmr from "../../images/add-new-client-emr.png"
import createClient from "../../images/create-client.png"

export default function IntegrateOscarProStep3() {
    return (
        <div>
            <h1 className="text-[#09090B] font-medium text-lg mb-3">Step 3</h1>
            <div className="bg-[#F4F4F5] py-5 px-6 rounded-xl">
                <h1 className="text-[#27272A] font-medium text-base">
                    Once at REST Clients, click ‘<span className="text-[#005893]">Add New</span>’ client to your EMR
                </h1>
                <p className="text-[#71717A] font-light mt-2">Click on
                    {" "}
                    '<span className="text-[#27272A] text-base font-normal">Add New</span>' under Manage Clients
                </p>
                <img
                    src={addNewClientEmr}
                    alt="Add New Client EMR"
                    className="w-full max-h-[26rem] mt-5"
                />
                <div className="mt-6">
                    <h1 className="text-[#27272A] font-medium text-base">
                        Fill in the information to complete adding a new client
                    </h1>
                    <p className="text-[#71717A] font-light mt-2">Click on
                        {" "}
                        '<span className="text-[#27272A] text-base font-normal">Add New</span>' under Manage Clients
                    </p>
                    <div className="grid grid-cols-2 gap-x-4">
                        <div className="flex flex-col gap-x-4 gap-y-5 mt-4">
                            <div className="flex flex-col gap-1">
                                <label htmlFor="name" className="text-[#27272A] font-normal text-sm">Name</label>
                                <Input
                                    type="text"
                                    placeholder="Migranium Inc"
                                    className="bg-white py-5"
                                />
                            </div>
                            <div className="flex flex-col gap-1">
                                <label htmlFor="url" className="text-[#27272A] font-normal text-sm">URL</label>
                                <Input
                                    type="url"
                                    placeholder="https://www.migranium.com"
                                    className="bg-white py-5"
                                />
                            </div>
                            <div className="flex flex-col gap-1">
                                <label htmlFor="name" className="text-[#27272A] font-normal text-sm">Lifetime Token</label>
                                <Input
                                    type="text"
                                    placeholder="-1"
                                    className="bg-white py-5"
                                />
                            </div>
                        </div>
                        <div className="">
                            <img
                                src={createClient}
                                alt="Create Client"
                                className="w-full max-h-[20rem] object-contain"
                            />
                        </div>
                    </div>
                </div>
            </div>
            {/*  */}
        </div>
    )
}