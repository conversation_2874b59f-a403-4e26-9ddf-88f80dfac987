import * as React from "react";
import { CalendarIcon } from "lucide-react";
import { addDays, format } from "date-fns";
import { type DateRange } from "react-day-picker";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { PopoverContent } from "@/components/ui/popover";
import { Popover, PopoverTrigger } from "@radix-ui/react-popover";
import { useCallback, useEffect, useState } from "react";
import { cn } from "@/lib/utils";

export function CalendarDateRangePicker({
	className,
	triggerClassName,
	onDateRangeChange,
	initialStartDate,
	initialEndDate,
}: {
	className?: string;
	triggerClassName?: string;
	onDateRangeChange: (dates: {
		start_date: string;
		end_date: string;
	}) => void;
	initialStartDate?: string;
	initialEndDate?: string;
}) {
	const [date, setDate] = useState<DateRange | undefined>(() => ({
		from: initialStartDate
			? new Date(initialStartDate)
			: addDays(new Date(2024, 0, 1), -1),
		to: initialEndDate ? new Date(initialEndDate) : new Date(2024, 0, 1),
	}));

	useEffect(() => {
		if (initialStartDate && initialEndDate) {
			setDate({
				from: new Date(initialStartDate),
				to: new Date(initialEndDate),
			});
		}
	}, [initialStartDate, initialEndDate]);

	const handleDateChange = useCallback((newDate: DateRange | undefined) => {
		if (!newDate?.from || !newDate?.to) return;

		setDate(newDate);
		onDateRangeChange({
			start_date: format(newDate.from, "yyyy-MM-dd"),
			end_date: format(newDate.to, "yyyy-MM-dd"),
		});
	}, []);

	return (
		<div className={cn("grid gap-2", className)}>
			<Popover>
				<PopoverTrigger asChild>
					<Button
						variant="outline"
						className={cn(
							"justify-start text-left font-normal",
							!date && "text-muted-foreground",
							triggerClassName
						)}
					>
						<CalendarIcon className="mr-2 h-4 w-4" />
						{date?.from ? (
							date.to ? (
								<>
									{format(date.from, "LLL dd, y")} -{" "}
									{format(date.to, "LLL dd, y")}
								</>
							) : (
								format(date.from, "LLL dd, y")
							)
						) : (
							<span>Pick a date</span>
						)}
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-auto p-0" align="start">
					<Calendar
						initialFocus
						mode="range"
						defaultMonth={date?.from}
						selected={date}
						onSelect={handleDateChange}
						numberOfMonths={2}
					/>
				</PopoverContent>
			</Popover>
		</div>
	);
}
