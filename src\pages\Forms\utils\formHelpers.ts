import { createOption } from "../schema/form";
import type { FormResponseTypes, FormTypes } from "../types";
import { cn } from "@/lib/utils";

type UUID = `${string}-${string}-${string}-${string}-${string}`;

const generateUUID = (): UUID => {
	return crypto.randomUUID() as UUID;
};

const defaultFieldValues = {
	text: {
		type: "text",
		required: false,
		options: [],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		order: 0,
	},

	numeric: {
		type: "numeric",
		required: false,
		options: [],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		order: 0,
	},

	date: {
		type: "date",
		required: false,
		options: [],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		dateValidation: {
			type: "static",
			minDate: null,
			maxDate: null,
		},
		order: 0,
	},

	date_range: {
		type: "date_range",
		required: false,
		options: [],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		dateValidation: {
			type: "static",
			minDate: null,
			maxDate: null,
		},
		order: 0,
	},

	long_text: {
		type: "long_text",
		required: false,
		options: [],
		description: null,
		image: null,
		info_text_value: null,
		approved_formats: [],
		order: 0,
	},

	checkbox: () => ({
		type: "checkbox",
		required: false,
		options: [createOption("checkbox")],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		order: 0,
	}),

	radio: () => ({
		type: "radio",
		required: false,
		options: [createOption("radio")],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		order: 0,
	}),

	dropdown: () => ({
		type: "dropdown",
		required: false,
		options: [createOption("dropdown")],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		// order: 0,
	}),

	attachment: {
		type: "attachment",
		required: false,
		options: [],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		// order: 0,
	},

	info_image: {
		type: "info_image",
		required: false,
		options: [],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		// order: 0,
	},

	info_text: {
		type: "info_text",
		required: false,
		options: [],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		// order: 0,
	},
	scale_1_10: {
		type: "scale_1_10",
		required: false,
		options: [],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		// order: 0,
	},
	satisfaction_scale: {
		type: "satisfaction_scale",
		required: false,
		options: [],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		// order: 0,
	},
	agree_disagree: {
		type: "agree_disagree",
		required: false,
		options: [],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		// order: 0,
	},
	yes_no: {
		type: "yes_no",
		required: false,
		options: [],
		description: "",
		image: null,
		info_text_value: null,
		approved_formats: [],
		// order: 0,
	},
};

const createField = (
	type: FormTypes.FieldType,
	order: number,
	preserveValues = {}
): FormTypes.FormField => {
	const defaults =
		defaultFieldValues[type as keyof typeof defaultFieldValues] ||
		defaultFieldValues.text;
	const baseValues = {
		id: crypto.randomUUID(),
		title: "",
		order: order,
		...(typeof defaults === "function" ? defaults() : defaults),
	};

	// Preserve any existing values while ensuring type-specific defaults
	return {
		...baseValues,
		...preserveValues,
		type, // Ensure type is always set correctly
	} as FormTypes.FormField;
};

const validateField = (field: FormTypes.FormField) => {
	// Import fieldSchemas dynamically to avoid circular dependency
	import("../schema/form").then(({ fieldSchemas }) => {
		const schema = fieldSchemas[field.type as keyof typeof fieldSchemas];
		if (!schema) return true;
		try {
			schema.parse(field);
			return true;
		} catch (error) {
			return false;
		}
	});
};

export { generateUUID, defaultFieldValues, createField, validateField };

export type { UUID };

/**
 * Get the variant color of the status badge
 * @param status - The status of the form
 * @returns The variant color of the status badge
 */

export const getVariantColor = (status: string, className: string): string => {
	const baseStyle = "px-[14px] py-[4px] text-xs capitalize font-medium";
	switch (status) {
		case "intake":
			return cn(
				baseStyle,
				"bg-opacity-[10%] bg-[#FF6A001A] text-[#C65200]",
				className
			);
		case "service":
			return cn(
				baseStyle,
				"bg-opacity-[10%] bg-[#0058931A] text-[#005893]",
				className
			);
		case "general":
			return cn(
				baseStyle,
				"bg-opacity-[10%] bg-[#FF48ED1A] text-[#C402B1]",
				className
			);
		case "feedback":
			return cn(
				baseStyle,
				"bg-opacity-[10%] bg-[#00B6C71A] text-[#015E8F]",
				className
			);
		case "referral":
			return cn(
				baseStyle,
				"bg-opacity-[10%] bg-[#6E00BD1A] text-[#5E00A2]",
				className
			);
		case "live":
			return cn(
				baseStyle,
				"bg-opacity-[15%] bg-[#C3EFCE] text-[#1C4B2A]",
				className
			);
		case "draft":
			return cn(
				baseStyle,
				"bg-opacity-[15%] bg-[#FEF3C7] text-[#92400E]",
				className
			);
		case "inactive":
			return cn(
				baseStyle,
				"bg-opacity-[15%] bg-[#E4E4E7] text-[#27272A]",
				className
			);
		case "pending":
			return cn(baseStyle, "bg-[#FAFAFA] text-[#27272A]", className);
		case "submitted":
			return cn(baseStyle, "bg-[#C3EFCE] text-[#1C4B2A]", className);
		case "error":
			return cn(baseStyle, "bg-[#FEE2E2] text-[#7F1D1D]", className);
		case "declined":
			return cn(baseStyle, "bg-[#FEE2E2] text-[#7F1D1D]", className);
		case "approved":
			return cn(baseStyle, "bg-[#C3EFCE] text-[#1C4B2A]", className);
		case "completed":
			return cn(baseStyle, "bg-[#C3EFCE] text-[#1C4B2A]", className);
		default:
			return cn(
				baseStyle,
				"bg-opacity-[15%] bg-[#0BC80126] text-[#005893]",
				className
			);
	}
};

/**
 * Get default values from response data
 * @param formData - The form data
 * @param responseData - The response data
 * @returns The default values
 */
export const getDefaultValues = (
	formData: FormTypes.FormDataType,
	responseData: FormResponseTypes.FormResponsesData
) => {
	const defaults: Record<string, any> = {};

	// Create a lookup map for responses
	const responseLookup: Record<string, any> = {};
	responseData?.forEach((section) => {
		section?.fields?.forEach((field) => {
			responseLookup[field.field_id] = field.raw_value;
		});
	});

	// Set default values for all fields
	formData?.sections?.forEach((section) => {
		section?.fields?.forEach((field) => {
			if (field.type === "info_text" || field.type === "info_image")
				return;

			const responseValue = responseLookup[field.id];

			switch (field.type) {
				case "checkbox":
					defaults[field.id] = responseValue || [];
					break;
				case "yes_no":
					defaults[field.id] =
						responseValue === "true" || responseValue === true;
					break;
				case "numeric":
				case "number":
				case "scale_1_10":
					defaults[field.id] = responseValue
						? Number(responseValue)
						: undefined;
					break;
				default:
					defaults[field.id] = responseValue || "";
			}
		});
	});

	return defaults;
};
