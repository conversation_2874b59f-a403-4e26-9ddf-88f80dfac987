import { FormTypes, FormResponseTypes } from "../types";
import { useForm, type FieldValues } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { getDefaultValues } from "../utils/formHelpers";
import { createDynamicSchema } from "../schema/formResponse";
import { EnhancedFieldRenderer } from "./EnhancedFieldRenderer";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getVariantColor } from "../utils/formHelpers";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { forwardRef, useImperativeHandle } from "react";
import { useUpdateFormResponse } from "../store/slices/formResponseSlice";
import useCustomToast from "@/components/CustomToast";

interface FormResponseEditorProps {
	formData: FormTypes.FormDataType;
	responseData: FormResponseTypes.FormResponsesData;
	onSave: (
		updatePayload: FormResponseTypes.UpdateFormResponsePayload
	) => void;
	mode: "edit" | "view" | "manage";
	onSaveSuccess?: () => void;
	formResponseData: FormResponseTypes.FormResponseDataType | null;
}

export interface FormResponseEditorRef {
	onSubmit: () => void;
	handleReset: () => void;
}

export const FormResponseEditor = forwardRef<
	FormResponseEditorRef,
	FormResponseEditorProps
>(
	(
		{
			formData,
			responseData,
			onSave,
			mode = "view",
			onSaveSuccess,
			formResponseData,
		},
		ref
	) => {
		// Create dynamic schema and default values
		const schema = createDynamicSchema(formData);
		const defaultValues = getDefaultValues(formData, responseData);
		const customToast = useCustomToast();
		const {
			mutate: updateFormResponse,
			isPending: isUpdatingFormResponse,
		} = useUpdateFormResponse();

		const {
			control,
			handleSubmit,
			formState: { errors, isSubmitting, isDirty },
			reset,
			watch,
		} = useForm({
			resolver: zodResolver(schema),
			defaultValues,
			mode: "onChange",
		});

		const watchedValues = watch();

		const onSubmit = (data: FieldValues) => {
			console.log("Form submitted:", data);

			// Transform data back to the expected format
			const transformedData = {
				responses: Object.entries(data).map(([fieldId, value]) => ({
					field_id: fieldId,
					value: value,
				})),
				...(formResponseData?.client?.id && {
					user_id: formResponseData?.client?.id,
				}),
			};

			updateFormResponse(
				{
					id: formResponseData?.id || "",
					data: transformedData,
				},
				{
					onSuccess: (data) => {
						customToast(
							data.message ||
								"Form response updated successfully 🎉",
							{
								id: "update-form-response",
								type: "success",
							}
						);
						onSaveSuccess?.();
					},
					onError: (error: any) => {
						customToast(
							error.response.data.message ||
								"Failed to update form response 🤕",
							{
								id: "update-form-response",
								type: "error",
							}
						);
					},
				}
			);
		};

		const handleReset = () => {
			reset(defaultValues);
		};

		// Expose functions to parent component
		useImperativeHandle(ref, () => ({
			onSubmit: () => handleSubmit(onSubmit)(),
			handleReset,
		}));

		const hasErrors = Object.keys(errors).length > 0;

		return (
			<Card className="border-t-primary border-t-4 py-0">
				<CardContent className="space-y-6 p-6">
					{/* Header */}
					<div className="flex items-center justify-between">
						<h2 className="text-3xl font-light text-slate-900">
							{formData.name}
						</h2>
						<div className="flex items-center gap-2">
							<Badge
								variant="outline"
								className={getVariantColor(
									formData?.type || "default",
									"border-none text-xs"
								)}
							>
								{formData?.type || ""}
							</Badge>
						</div>
					</div>

					{/* Form Content */}
					<div className="space-y-8">
						{formData.sections
							?.sort((a, b) => a.order - b.order)
							?.map((section) => (
								<div
									key={section.id}
									className="rounded-lg border border-gray-200 bg-white p-6"
								>
									<div className="mb-6">
										<h2 className="text-xl font-semibold text-gray-900">
											{section.title}
										</h2>
										{section.description && (
											<p className="mt-1 text-gray-600">
												{section.description}
											</p>
										)}
									</div>

									<div className="space-y-6">
										{section.fields
											?.sort((a, b) => a.order - b.order)
											?.map((field) => (
												<EnhancedFieldRenderer
													key={field.id}
													field={field}
													control={control}
													mode={mode}
													errors={errors}
												/>
											))}
									</div>
								</div>
							))}
					</div>

					{/* Form Actions */}
					{/* {mode === "edit" && (
					<div className="-mx-6 -mb-6 flex items-center justify-between rounded-b-lg border-t border-gray-200 bg-white px-6 py-4 pt-6">
						<Button
							type="button"
							variant="outline"
							onClick={handleReset}
							disabled={!isDirty}
							className="flex items-center gap-2"
						>
							<RotateCcw className="h-4 w-4" />
							Reset Changes
						</Button>

						<Button
							onClick={handleSubmit(onSubmit)}
							disabled={isSubmitting || hasErrors}
							className="flex items-center gap-2"
						>
							{isSubmitting ? (
								<>
									<div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
									Saving...
								</>
							) : (
								<>
									<Save className="h-4 w-4" />
									Save Changes
								</>
							)}
						</Button>
					</div>
				)} */}

					{/* Error Summary */}
					{hasErrors && (
						<Alert className="border-red-200 bg-red-50">
							<AlertCircle className="h-4 w-4 text-red-600" />
							<AlertDescription>
								<div className="mb-2 font-medium text-red-800">
									Please fix the following errors:
								</div>
								<ul className="list-inside list-disc space-y-1 text-sm text-red-700">
									{Object.entries(errors).map(
										([fieldId, error]) => {
											const field = formData.sections
												?.flatMap((s) => s.fields)
												?.find((f) => f.id === fieldId);
											return (
												<li key={fieldId}>
													<span className="font-medium">
														{field?.title}:
													</span>{" "}
													{(error as any)?.message}
												</li>
											);
										}
									)}
								</ul>
							</AlertDescription>
						</Alert>
					)}
				</CardContent>
			</Card>
		);
	}
);
