import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
	DialogPortal,
	DialogOverlay,
} from "@/components/ui/dialog";

interface DeleteConfirmationDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	title?: string;
	description?: string;
	onConfirm: () => void;
	onCancel?: () => void;
	confirmText?: string;
	cancelText?: string;
	isLoading?: boolean;
}

export function DeleteConfirmationDialog({
	open,
	onOpenChange,
	title = "Are you sure you want to delete this location?",
	description = "This action cannot be undone and it will permanently delete all location details and history.",
	onConfirm,
	onCancel,
	confirmText = "Continue",
	cancelText = "Cancel",
	isLoading = false,
}: DeleteConfirmationDialogProps) {
	const handleCancel = () => {
		if (onCancel) {
			onCancel();
		} else {
			onOpenChange(false);
		}
	};

	const handleConfirm = () => {
		onConfirm();
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogPortal>
				{/* Custom overlay with higher z-index */}
				<DialogOverlay className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-[1004] bg-black/50" />
				{/* Custom content with higher z-index */}
				<DialogContent className="data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-[1005] grid w-full max-w-[425px] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border bg-white p-6 shadow-lg duration-200">
					<DialogHeader>
						<DialogTitle className="text-lg font-semibold text-gray-900">
							{title}
						</DialogTitle>
						<DialogDescription className="mt-2 text-sm text-gray-500">
							{description}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter className="mt-6 flex justify-end gap-3">
						<Button
							variant="outline"
							onClick={handleCancel}
							disabled={isLoading}
							className="px-4 py-2"
						>
							{cancelText}
						</Button>
						<Button
							onClick={handleConfirm}
							disabled={isLoading}
							className="bg-red-600 px-4 py-2 text-white hover:bg-red-700"
						>
							{isLoading ? "Deleting..." : confirmText}
						</Button>
					</DialogFooter>
				</DialogContent>
			</DialogPortal>
		</Dialog>
	);
}
