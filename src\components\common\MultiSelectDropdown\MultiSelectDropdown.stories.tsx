import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";
import { useState } from "react";
import { MultiSelectDropdown } from "./MultiSelectDropdown";
import type { MultiSelectDropdownProps, MultiSelectOption } from "./types";
import { Star, User, MapPin, Calendar, Tag, AlertCircle } from "lucide-react";

const meta: Meta<typeof MultiSelectDropdown> = {
	title: "Components/MultiSelectDropdown",
	component: MultiSelectDropdown,
	parameters: {
		layout: "centered",
		docs: {
			description: {
				component:
					"A reusable multi-select dropdown component with visual chips for selected values. Supports both string arrays and object arrays, custom rendering, and extensive customization options.",
			},
		},
	},
	tags: ["autodocs"],
	argTypes: {
		value: {
			control: "object",
			description: "Current selected values",
		},
		onValueChange: {
			action: "value changed",
			description: "Callback when selection changes",
		},
		options: {
			control: "object",
			description: "Available options (string[] or MultiSelectOption[])",
		},
		placeholder: {
			control: "text",
			description: "Placeholder text when no options selected",
		},
		label: {
			control: "text",
			description: "Label for the select component",
		},
		disabled: {
			control: "boolean",
			description: "Whether the component is disabled",
		},
		error: {
			control: "boolean",
			description: "Whether to show error styling",
		},
		maxSelectedDisplay: {
			control: "number",
			description:
				"Maximum number of chips to display before showing overflow",
		},
		emptyText: {
			control: "text",
			description: "Text shown when no options are available",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic Examples
export const Default: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([]);
		const options = [
			"Option 1",
			"Option 2",
			"Option 3",
			"Option 4",
			"Option 5",
		];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedValues}
					onValueChange={setSelectedValues}
					options={options}
					placeholder="Select options..."
					label="Basic Multi-Select"
				/>
			</div>
		);
	},
};

export const WithObjectOptions: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([]);
		const options: MultiSelectOption[] = [
			{ value: "us", label: "United States" },
			{ value: "uk", label: "United Kingdom" },
			{ value: "ca", label: "Canada" },
			{ value: "fr", label: "France" },
			{ value: "de", label: "Germany" },
			{ value: "jp", label: "Japan", disabled: true },
		];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedValues}
					onValueChange={setSelectedValues}
					options={options}
					placeholder="Select countries..."
					label="Country Selection"
				/>
			</div>
		);
	},
};

export const PreSelected: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([
			"Option 1",
			"Option 3",
		]);
		const options = [
			"Option 1",
			"Option 2",
			"Option 3",
			"Option 4",
			"Option 5",
		];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedValues}
					onValueChange={setSelectedValues}
					options={options}
					placeholder="Select options..."
					label="Pre-selected Values"
				/>
			</div>
		);
	},
};

// State Examples
export const DisabledState: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([
			"Option 1",
			"Option 2",
		]);
		const options = ["Option 1", "Option 2", "Option 3", "Option 4"];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedValues}
					onValueChange={setSelectedValues}
					options={options}
					placeholder="Select options..."
					label="Disabled State"
					disabled
				/>
			</div>
		);
	},
};

export const ErrorState: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([]);
		const options = ["Option 1", "Option 2", "Option 3", "Option 4"];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedValues}
					onValueChange={setSelectedValues}
					options={options}
					placeholder="Select options..."
					label="Error State"
					error
				/>
			</div>
		);
	},
};

export const EmptyOptions: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([]);
		const options: string[] = [];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedValues}
					onValueChange={setSelectedValues}
					options={options}
					placeholder="No options available..."
					label="Empty Options"
					emptyText="No options found"
				/>
			</div>
		);
	},
};

// Overflow and Display
export const OverflowHandling: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([
			"Option 1",
			"Option 2",
			"Option 3",
			"Option 4",
			"Option 5",
		]);
		const options = [
			"Option 1",
			"Option 2",
			"Option 3",
			"Option 4",
			"Option 5",
			"Option 6",
			"Option 7",
			"Option 8",
		];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedValues}
					onValueChange={setSelectedValues}
					options={options}
					placeholder="Select options..."
					label="Overflow Handling"
					maxSelectedDisplay={3}
				/>
			</div>
		);
	},
};

export const CustomMaxDisplay: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([
			"Option 1",
			"Option 2",
		]);
		const options = [
			"Option 1",
			"Option 2",
			"Option 3",
			"Option 4",
			"Option 5",
		];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedValues}
					onValueChange={setSelectedValues}
					options={options}
					placeholder="Select options..."
					label="Max Display: 1"
					maxSelectedDisplay={1}
				/>
			</div>
		);
	},
};

// Styling Examples
export const CustomStyling: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([
			"High Priority",
		]);
		const options = [
			"High Priority",
			"Medium Priority",
			"Low Priority",
			"Critical",
		];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedValues}
					onValueChange={setSelectedValues}
					options={options}
					placeholder="Select priority..."
					label="Custom Styling"
					className="border-2 border-blue-200"
					selectTriggerProps={{
						className: "border-blue-500 focus:border-blue-600",
						style: { minHeight: "3rem" },
					}}
					chipProps={{
						className: "bg-blue-100 border-blue-300 text-blue-800",
						removeButtonClassName: "hover:bg-blue-200",
					}}
				/>
			</div>
		);
	},
};

// Custom Rendering
export const CustomChipRendering: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([
			"high",
			"medium",
		]);
		const options: MultiSelectOption[] = [
			{ value: "high", label: "High Priority" },
			{ value: "medium", label: "Medium Priority" },
			{ value: "low", label: "Low Priority" },
			{ value: "critical", label: "Critical Priority" },
		];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedValues}
					onValueChange={setSelectedValues}
					options={options}
					placeholder="Select priority..."
					label="Custom Chip Rendering"
					customRenderChip={(value, onRemove) => (
						<div
							className={`flex items-center gap-2 rounded-full px-3 py-1 text-sm text-white ${
								value === "high" || value === "critical"
									? "bg-red-500"
									: value === "medium"
										? "bg-yellow-500"
										: "bg-green-500"
							}`}
						>
							<span>{value}</span>
							<button
								onClick={onRemove}
								className="text-white hover:text-gray-200"
							>
								×
							</button>
						</div>
					)}
				/>
			</div>
		);
	},
};

export const CustomOptionRendering: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([
			"user",
			"admin",
		]);
		const options: MultiSelectOption[] = [
			{ value: "user", label: "User" },
			{ value: "admin", label: "Administrator" },
			{ value: "moderator", label: "Moderator" },
			{ value: "guest", label: "Guest" },
		];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedValues}
					onValueChange={setSelectedValues}
					options={options}
					placeholder="Select roles..."
					label="Custom Option Rendering"
					customRenderOption={(option) => {
						const opt =
							typeof option === "string"
								? { value: option, label: option }
								: option;
						return (
							<div className="flex items-center gap-2">
								<User className="h-4 w-4" />
								<span>{opt.label}</span>
								{opt.value === "admin" && (
									<Star className="h-3 w-3 text-yellow-500" />
								)}
							</div>
						);
					}}
				/>
			</div>
		);
	},
};

// Real-world Examples
export const TagSelection: Story = {
	render: () => {
		const [selectedTags, setSelectedTags] = useState<string[]>([
			"frontend",
			"react",
		]);
		const tags = [
			"frontend",
			"backend",
			"react",
			"typescript",
			"nodejs",
			"api",
			"database",
			"ui",
			"ux",
			"design",
		];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedTags}
					onValueChange={setSelectedTags}
					options={tags}
					placeholder="Select tags..."
					label="Project Tags"
					chipProps={{
						className:
							"bg-purple-100 border-purple-300 text-purple-800",
						removeButtonClassName: "hover:bg-purple-200",
					}}
				/>
			</div>
		);
	},
};

export const LocationSelection: Story = {
	render: () => {
		const [selectedLocations, setSelectedLocations] = useState<string[]>([
			"new-york",
		]);
		const locations: MultiSelectOption[] = [
			{ value: "new-york", label: "New York, NY" },
			{ value: "los-angeles", label: "Los Angeles, CA" },
			{ value: "chicago", label: "Chicago, IL" },
			{ value: "houston", label: "Houston, TX" },
			{ value: "phoenix", label: "Phoenix, AZ" },
			{ value: "philadelphia", label: "Philadelphia, PA" },
		];

		return (
			<div className="w-80">
				<MultiSelectDropdown
					value={selectedLocations}
					onValueChange={setSelectedLocations}
					options={locations}
					placeholder="Select locations..."
					label="Office Locations"
					customRenderOption={(option) => {
						const opt =
							typeof option === "string"
								? { value: option, label: option }
								: option;
						return (
							<div className="flex items-center gap-2">
								<MapPin className="h-4 w-4 text-gray-500" />
								<span>{opt.label}</span>
							</div>
						);
					}}
				/>
			</div>
		);
	},
};

// Interactive Playground
export const InteractivePlayground: Story = {
	render: () => {
		const [selectedValues, setSelectedValues] = useState<string[]>([
			"Option 1",
		]);
		const [isDisabled, setIsDisabled] = useState(false);
		const [hasError, setHasError] = useState(false);
		const [maxDisplay, setMaxDisplay] = useState(3);

		const options = [
			"Option 1",
			"Option 2",
			"Option 3",
			"Option 4",
			"Option 5",
			"Option 6",
			"Option 7",
		];

		return (
			<div className="space-y-6">
				<div className="w-80">
					<MultiSelectDropdown
						value={selectedValues}
						onValueChange={setSelectedValues}
						options={options}
						placeholder="Select options..."
						label="Interactive Playground"
						disabled={isDisabled}
						error={hasError}
						maxSelectedDisplay={maxDisplay}
					/>
				</div>

				<div className="space-y-4 rounded-lg bg-gray-50 p-4">
					<h3 className="font-semibold">Controls</h3>
					<div className="space-y-2">
						<label className="flex items-center gap-2">
							<input
								type="checkbox"
								checked={isDisabled}
								onChange={(e) =>
									setIsDisabled(e.target.checked)
								}
							/>
							Disabled
						</label>
						<label className="flex items-center gap-2">
							<input
								type="checkbox"
								checked={hasError}
								onChange={(e) => setHasError(e.target.checked)}
							/>
							Error State
						</label>
						<label className="flex items-center gap-2">
							Max Display Count:
							<input
								type="number"
								value={maxDisplay}
								onChange={(e) =>
									setMaxDisplay(parseInt(e.target.value) || 1)
								}
								min="1"
								max="10"
								className="w-16 rounded border px-2 py-1"
							/>
						</label>
					</div>
					<div className="text-sm text-gray-600">
						Selected: {selectedValues.join(", ") || "None"}
					</div>
				</div>
			</div>
		);
	},
};

// All Variants Showcase
export const AllVariants: Story = {
	render: () => {
		const [values1, setValues1] = useState<string[]>(["Option 1"]);
		const [values2, setValues2] = useState<string[]>(["us", "uk"]);
		const [values3, setValues3] = useState<string[]>([]);
		const [values4, setValues4] = useState<string[]>(["Disabled Option"]);
		const [values5, setValues5] = useState<string[]>([]);
		const [values6, setValues6] = useState<string[]>([
			"Option 1",
			"Option 2",
			"Option 3",
			"Option 4",
		]);

		const basicOptions = ["Option 1", "Option 2", "Option 3", "Option 4"];
		const countryOptions: MultiSelectOption[] = [
			{ value: "us", label: "United States" },
			{ value: "uk", label: "United Kingdom" },
			{ value: "ca", label: "Canada" },
			{ value: "fr", label: "France" },
		];

		return (
			<div className="grid max-w-4xl gap-8 p-6">
				<div className="space-y-4">
					<h3 className="text-lg font-semibold">
						Basic Multi-Select
					</h3>
					<div className="w-80">
						<MultiSelectDropdown
							value={values1}
							onValueChange={setValues1}
							options={basicOptions}
							placeholder="Select options..."
							label="Basic Example"
						/>
					</div>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-semibold">Object Options</h3>
					<div className="w-80">
						<MultiSelectDropdown
							value={values2}
							onValueChange={setValues2}
							options={countryOptions}
							placeholder="Select countries..."
							label="Country Selection"
						/>
					</div>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-semibold">Error State</h3>
					<div className="w-80">
						<MultiSelectDropdown
							value={values3}
							onValueChange={setValues3}
							options={basicOptions}
							placeholder="Select options..."
							label="Error Example"
							error
						/>
					</div>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-semibold">Disabled State</h3>
					<div className="w-80">
						<MultiSelectDropdown
							value={values4}
							onValueChange={setValues4}
							options={basicOptions}
							placeholder="Select options..."
							label="Disabled Example"
							disabled
						/>
					</div>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-semibold">Empty Options</h3>
					<div className="w-80">
						<MultiSelectDropdown
							value={values5}
							onValueChange={setValues5}
							options={[]}
							placeholder="No options available..."
							label="Empty Example"
							emptyText="No options found"
						/>
					</div>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-semibold">Overflow Handling</h3>
					<div className="w-80">
						<MultiSelectDropdown
							value={values6}
							onValueChange={setValues6}
							options={[
								"Option 1",
								"Option 2",
								"Option 3",
								"Option 4",
								"Option 5",
								"Option 6",
							]}
							placeholder="Select options..."
							label="Overflow Example"
							maxSelectedDisplay={2}
						/>
					</div>
				</div>
			</div>
		);
	},
};
