import { cn } from "@/lib/utils";

interface UserAvatarProps {
    profilePicture?: string | null;
    userName?: string;
    size?: "sm" | "md" | "lg";
    className?: string;
}

export const UserAvatar = ({
    profilePicture,
    userName = "",
    size = "md",
    className
}: UserAvatarProps) => {
    const sizeClasses = {
        sm: "h-8 w-8 text-xs",
        md: "h-10 w-10 text-sm",
        lg: "h-12 w-12 text-base"
    };

    const getInitials = (name: string): string => {
        if (!name) return "?";

        const nameParts = name.trim().split(" ");
        if (nameParts.length === 1) {
            return nameParts[0].charAt(0).toUpperCase();
        }

        return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
    };

    if (profilePicture) {
        return (
            <img
                className={cn(
                    "hover:ring-primary relative cursor-pointer rounded-full transition-all hover:ring-2 hover:ring-offset-2",
                    sizeClasses[size],
                    className
                )}
                src={profilePicture}
                alt="User avatar"
                onError={(e) => {
                    // If image fails to load, hide it and show initials instead
                    e.currentTarget.style.display = 'none';
                    const initialsDiv = e.currentTarget.nextElementSibling as HTMLElement;
                    if (initialsDiv) {
                        initialsDiv.style.display = 'flex';
                    }
                }}
            />
        );
    }

    return (
        <div
            className={cn(
                "hover:ring-primary relative cursor-pointer rounded-full bg-gradient-to-br from-blue-500 to-purple-600 transition-all hover:ring-2 hover:ring-offset-2 flex items-center justify-center font-medium text-white",
                sizeClasses[size],
                className
            )}
        >
            {getInitials(userName)}
        </div>
    );
};