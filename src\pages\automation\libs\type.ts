export type EditorCanvasTypes = "Trigger" | "Condition" | "IF Match" | "Else Match" | "Check"

export type IfElseMatchType = string[];

export type EditorCanvasCardMetaType =
    | {
        type: "Trigger",
        triggerType: string;
    }
    | {
        type: "Condition",
        condition: string[];
        dateTime?: {
            before?: Date;
            after?: Date;
        };
        category?: string[];
        registration?: "registered" | "not_registered" | "all";
        location?: string[];
        provider?: string[];
        service?: string[];
        appointmentStatus?: string[];
        patientStatus?: string;
    }
    | {
        type: "IF Match",
        action: string[];
    }
    | {
        type: "Else Match",
        action: string[];
    }
    | {
        type: "Check",
        checkItem: string;
        selectAction: string;
        formName: string;
        periodType: "dynamic" | "specific";
        numberOfChecks: number;
        ifMatch: IfElseMatchType;
        elseMatch: IfElseMatchType;
        intervalUnit: "hours" | "days" | "minutes";
        checkInterval: number;
    }

export type EditorCanvasCardType = {
    title: string
    completed: boolean
    description: string
    current: boolean
    metadata: EditorCanvasCardMetaType
    type: EditorCanvasTypes
}

export type EditorNodeType = {
    id: string
    type: EditorCanvasCardType['type']
    position: {
        x: number
        y: number
    }
    data: EditorCanvasCardType
}
export type EditorNode = EditorNodeType

export type EditorActions =
    | {
        type: 'LOAD_DATA'
        payload: {
            elements: EditorNode[]
            edges: {
                id: string
                source: string
                target: string
            }[]
        }
    }
    | {
        type: 'UPDATE_NODE'
        payload: {
            elements: EditorNode[]
        }
    }
    | { type: 'REDO' }
    | { type: 'UNDO' }
    | {
        type: 'SELECTED_ELEMENT'
        payload: {
            element: EditorNode
        }
    }
    | {
        type: 'UPDATE_NODE_METADATA',
        payload: {
            nodeId: string
            metadata: EditorCanvasCardMetaType
        },
    } | {
        type: "DELETE_NODE",
        payload: {
            nodeId: string;
        }
    }
