import React, { use<PERSON><PERSON>back, useState, useEffect } from "react";
import {
	Upload,
	FileText,
	Edit2,
	Trash2,
	Loader2,
	ImageIcon,
	CheckCircle,
	AlertCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import type { UploaderProps, FileItemProps, UploadedFile } from "./types";
import { uploadImage } from "@/lib/api/upload";

/**
 * File item component for displaying uploaded files
 */
const FileItem = React.forwardRef<HTMLDivElement, FileItemProps>(
	(
		{
			file,
			onRemove,
			onChange,
			accept = ".svg,.jpg,.jpeg,.png",
			className,
		},
		ref
	) => {
		const handleChange = useCallback(
			(e: React.MouseEvent) => {
				e.preventDefault();
				e.stopPropagation();

				console.log(
					"Change button clicked, creating dynamic file input"
				);

				// Create a dynamic file input element
				const input = document.createElement("input");
				input.type = "file";
				input.accept = accept;
				input.style.display = "none";

				input.onchange = (event) => {
					console.log("Dynamic file input change triggered");
					const target = event.target as HTMLInputElement;
					if (target.files && target.files.length > 0) {
						const newFile = target.files[0];
						console.log(
							"New file selected for change:",
							newFile.name
						);
						onChange?.(file.id, newFile);
					}
					// Clean up
					document.body.removeChild(input);
				};

				// Add to DOM and trigger click
				document.body.appendChild(input);
				input.click();
			},
			[file.id, file.name, accept, onChange]
		);

		const formatFileSize = (bytes: number) => {
			if (bytes === 0) return "0 Bytes";
			const k = 1024;
			const sizes = ["Bytes", "KB", "MB", "GB"];
			const i = Math.floor(Math.log(bytes) / Math.log(k));
			return (
				parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
			);
		};

		const getStatusIcon = () => {
			switch (file.uploadStatus) {
				case "uploading":
					return (
						<Loader2 className="h-4 w-4 animate-spin text-blue-500" />
					);
				case "success":
					return <CheckCircle className="h-4 w-4 text-green-500" />;
				case "error":
					return <AlertCircle className="h-4 w-4 text-red-500" />;
				default:
					return null;
			}
		};

		const getStatusText = () => {
			switch (file.uploadStatus) {
				case "uploading":
					return "Uploading...";
				case "success":
					return "Uploaded";
				case "error":
					return file.uploadError || "Upload failed";
				default:
					return formatFileSize(file.size);
			}
		};

		// Check if file is an image
		const isImage =
			file.type.startsWith("image/") ||
			/\.(jpg|jpeg|png|gif|svg|webp)$/i.test(file.name);

		return (
			<div
				ref={ref}
				className={cn(
					"bg-background flex items-center gap-4 rounded-lg p-6",
					"hover:bg-accent/50 min-h-[120px] border-2 border-dashed transition-colors",
					file.uploadStatus === "error" &&
						"border-red-200 bg-red-50/50",
					className
				)}
				onClick={(e) => {
					e.preventDefault();
					e.stopPropagation();
				}}
			>
				{/* Avatar/Preview */}
				<div className="flex-shrink-0">
					<div className="relative h-12 w-12">
						{(file.url || file.localPreview) && isImage ? (
							<img
								src={file.url || file.localPreview}
								alt={file.name}
								className="h-12 w-12 rounded-full object-cover"
							/>
						) : (
							<div className="flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
								<FileText className="h-6 w-6 text-gray-600" />
							</div>
						)}
						{/* Upload status overlay */}
						{file.uploadStatus === "uploading" && (
							<div className="absolute inset-0 flex items-center justify-center rounded-full bg-black/50">
								<Loader2 className="h-4 w-4 animate-spin text-white" />
							</div>
						)}
					</div>
				</div>

				{/* File Info */}
				<div className="min-w-0 flex-1">
					<div>
						<div className="mb-1 flex items-center gap-2">
							<p className="text-foreground truncate text-sm font-medium">
								{file.name}
							</p>
							{getStatusIcon()}
						</div>
						<p className="text-xs text-gray-500">
							{file.type || "Unknown type"}
						</p>
						<p className="mt-1 text-xs text-gray-500">
							File size: {formatFileSize(file.size)}
						</p>
						{(file.uploadStatus === "uploading" ||
							file.uploadStatus === "error") && (
							<p
								className={cn(
									"mt-1 text-xs",
									file.uploadStatus === "error"
										? "text-red-500"
										: "text-muted-foreground"
								)}
							>
								{getStatusText()}
							</p>
						)}
					</div>
				</div>

				{/* Action Buttons */}
				<div className="flex flex-2 items-center gap-2">
					<Button
						variant="destructive"
						size="sm"
						type="button"
						onClick={(e) => {
							e.preventDefault();
							e.stopPropagation();
							onRemove?.(file.id);
						}}
						disabled={file.uploadStatus === "uploading"}
						className="flex-1 cursor-pointer bg-red-600 px-4 text-white"
					>
						Remove
					</Button>
					<Button
						variant="outline"
						size="sm"
						type="button"
						onClick={handleChange}
						disabled={file.uploadStatus === "uploading"}
						className="flex-1 cursor-pointer px-4"
					>
						Change
					</Button>
				</div>
			</div>
		);
	}
);
FileItem.displayName = "FileItem";

/**
 * Uploader component for file uploads with drag and drop support
 *
 * @example
 * ```tsx
 * <Uploader
 *   onFilesChange={(files) => console.log('Files selected:', files)}
 *   accept=".svg,.jpg,.jpeg,.png"
 *   maxFileSize={10 * 1024 * 1024} // 10MB
 *   uploadText="Click or drag file here to upload"
 *   descriptionText="Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
 *   enableServerUpload={true}
 *   onUploadSuccess={(file, url) => console.log('Upload success:', file, url)}
 * />
 * ```
 */
const Uploader = React.forwardRef<HTMLDivElement, UploaderProps>(
	(
		{
			files = [],
			isLoading = false,
			multiple = false,
			accept = ".svg,.jpg,.jpeg,.png",
			maxFileSize = 10 * 1024 * 1024, // 10MB
			maxFiles = multiple ? 10 : 1,
			disabled = false,
			uploadText = "Click or drag file here to upload file",
			descriptionText = "Recommended file type: .svg, .png, .jpg (Max of 10 mb)",
			size = "default",
			variant = "default",
			enableServerUpload = false,
			className,
			uploadIcon,
			onFilesChange,
			onFileRemove,
			onError,
			onUploadStart,
			onUploadSuccess,
			onUploadError,
			...props
		},
		ref
	) => {
		const [isDragOver, setIsDragOver] = useState(false);
		const [isProcessing, setIsProcessing] = useState(false);
		const [internalFiles, setInternalFiles] = useState<UploadedFile[]>([]);
		const fileInputRef = React.useRef<HTMLInputElement>(null);

		// Ensure we're always working with arrays
		const displayFiles =
			Array.isArray(files) && files.length > 0 ? files : internalFiles;

		const hasFiles = Array.isArray(displayFiles) && displayFiles.length > 0;
		const hasUploadingFiles =
			Array.isArray(displayFiles) &&
			displayFiles?.some((file) => file.uploadStatus === "uploading");

		// Cleanup object URLs when component unmounts
		useEffect(() => {
			return () => {
				internalFiles.forEach((file) => {
					if (file.localPreview) {
						URL.revokeObjectURL(file.localPreview);
					}
				});
			};
		}, [internalFiles]);

		// Handle file removal with cleanup
		const handleFileRemove = useCallback(
			(fileId: string) => {
				if (files.length === 0) {
					// Find the file to cleanup its preview URL
					const fileToRemove = internalFiles.find(
						(f) => f.id === fileId
					);
					if (fileToRemove?.localPreview) {
						URL.revokeObjectURL(fileToRemove.localPreview);
					}

					// Remove from internal state
					setInternalFiles((prev) =>
						prev.filter((f) => f.id !== fileId)
					);
				}

				// Call the external handler
				onFileRemove?.(fileId);
			},
			[files.length, internalFiles, onFileRemove]
		);

		const sizeClasses = {
			sm: "p-6 min-h-[120px]",
			md: "p-8 min-h-[160px]",
			lg: "p-10 min-h-[200px]",
			default: "p-2 min-h-[68px]",
		};

		const variantClasses = {
			default: "border-dashed border-2",
			compact: "border border-dashed",
			bordered: "border-2 border-solid",
		};

		const validateFile = (file: File): string | null => {
			if (maxFileSize && file.size > maxFileSize) {
				return `File size exceeds ${Math.round(maxFileSize / 1024 / 1024)}MB limit`;
			}

			if (accept) {
				const acceptedTypes = accept
					.split(",")
					.map((type) => type.trim());
				const fileExtension =
					"." + file.name.split(".").pop()?.toLowerCase();
				const isAccepted = acceptedTypes.some(
					(type) =>
						type === fileExtension ||
						file.type.includes(type.replace(".", ""))
				);
				if (!isAccepted) {
					return `File type not accepted. Please use: ${accept}`;
				}
			}

			return null;
		};

		const uploadFileToServer = useCallback(
			async (file: File, fileId: string) => {
				try {
					// Update internal state to show uploading
					if (files.length === 0) {
						setInternalFiles((prev) =>
							prev.map((f) =>
								f.id === fileId
									? {
											...f,
											uploadStatus: "uploading" as const,
										}
									: f
							)
						);
					}
					onUploadStart?.(fileId);

					const uploadedUrl = await uploadImage(file);

					// Create updated file object with success status
					const updatedFile: UploadedFile = {
						id: fileId,
						name: file.name,
						size: file.size,
						type: file.type,
						url: uploadedUrl,
						uploadStatus: "success",
					};

					// Update internal state with success
					if (files.length === 0) {
						setInternalFiles((prev) =>
							prev.map((f) =>
								f.id === fileId
									? {
											...f,
											uploadStatus: "success" as const,
											url: uploadedUrl,
										}
									: f
							)
						);
					}

					onUploadSuccess?.(updatedFile, uploadedUrl);
				} catch (error) {
					const errorMessage =
						error instanceof Error
							? error.message
							: "Upload failed";

					// Update internal state with error
					if (files.length === 0) {
						setInternalFiles((prev) =>
							prev.map((f) =>
								f.id === fileId
									? {
											...f,
											uploadStatus: "error" as const,
											uploadError: errorMessage,
										}
									: f
							)
						);
					}

					onUploadError?.(fileId, errorMessage);
				}
			},
			[onUploadStart, onUploadSuccess, onUploadError, files.length]
		);

		const handleFiles = useCallback(
			(newFiles: FileList | File[]) => {
				const fileArray = Array.from(newFiles);
				const validFiles: File[] = [];

				for (const file of fileArray) {
					const error = validateFile(file);
					if (error) {
						onError?.(error);
						continue;
					}

					if (displayFiles.length + validFiles.length >= maxFiles) {
						onError?.(`Maximum ${maxFiles} files allowed`);
						break;
					}

					validFiles.push(file);
				}

				if (validFiles.length > 0) {
					// Create UploadedFile objects for each valid file with local previews
					const uploadedFiles: UploadedFile[] = validFiles.map(
						(file) => {
							const isImage =
								file.type.startsWith("image/") ||
								/\.(jpg|jpeg|png|gif|svg|webp)$/i.test(
									file.name
								);

							return {
								id: crypto.randomUUID(),
								name: file.name,
								size: file.size,
								type: file.type,
								localPreview: isImage
									? URL.createObjectURL(file)
									: undefined,
								uploadStatus: enableServerUpload
									? "pending"
									: undefined,
							};
						}
					);

					// If no external files management, handle internally
					if (files.length === 0) {
						setInternalFiles((prev) => [...prev, ...uploadedFiles]);
					}

					onFilesChange?.(validFiles);

					// If server upload is enabled, start uploading each file
					if (enableServerUpload) {
						uploadedFiles.forEach((uploadedFile, index) => {
							const file = validFiles[index];
							uploadFileToServer(file, uploadedFile.id);
						});
					}
				}
			},
			[
				displayFiles.length,
				files.length,
				maxFiles,
				onError,
				onFilesChange,
				validateFile,
				enableServerUpload,
				uploadFileToServer,
			]
		);

		// Handle file change (replacement)
		const handleFileChange = useCallback(
			(fileId: string, newFile: File) => {
				// First validate the new file
				const error = validateFile(newFile);
				if (error) {
					onError?.(error);
					return;
				}

				// Create the new UploadedFile object
				const isImage =
					newFile.type.startsWith("image/") ||
					/\.(jpg|jpeg|png|gif|svg|webp)$/i.test(newFile.name);

				const newUploadedFile: UploadedFile = {
					id: crypto.randomUUID(),
					name: newFile.name,
					size: newFile.size,
					type: newFile.type,
					localPreview: isImage
						? URL.createObjectURL(newFile)
						: undefined,
					uploadStatus: enableServerUpload ? "pending" : undefined,
				};

				// Handle internal files replacement
				if (files.length === 0) {
					// Find and cleanup the old file's preview URL
					const oldFile = internalFiles.find((f) => f.id === fileId);
					if (oldFile?.localPreview) {
						URL.revokeObjectURL(oldFile.localPreview);
					}

					// Replace the file in internal state
					setInternalFiles((prev) =>
						prev.map((f) => (f.id === fileId ? newUploadedFile : f))
					);

					// Only call external handlers if files are managed externally
				} else {
					// Call the external handler with the new file only if managing files externally
					onFilesChange?.([newFile]);
				}

				// If server upload is enabled, start uploading the new file
				if (enableServerUpload) {
					uploadFileToServer(newFile, newUploadedFile.id);
				}

				// Call the external remove handler for the old file only if managing files externally
				if (files.length > 0) {
					onFileRemove?.(fileId);
				}
			},
			[
				validateFile,
				onError,
				files.length,
				internalFiles,
				enableServerUpload,
				onFilesChange,
				uploadFileToServer,
				onFileRemove,
			]
		);

		const handleDragOver = useCallback(
			(e: React.DragEvent) => {
				e.preventDefault();
				e.stopPropagation();
				if (
					!disabled &&
					!isLoading &&
					!hasUploadingFiles &&
					!isProcessing
				) {
					setIsDragOver(true);
				}
			},
			[disabled, isLoading, hasUploadingFiles, isProcessing]
		);

		const handleDragLeave = useCallback((e: React.DragEvent) => {
			e.preventDefault();
			e.stopPropagation();
			setIsDragOver(false);
		}, []);

		const handleDrop = useCallback(
			(e: React.DragEvent) => {
				e.preventDefault();
				e.stopPropagation();
				setIsDragOver(false);

				if (disabled || isLoading || hasUploadingFiles || isProcessing)
					return;

				const droppedFiles = e.dataTransfer.files;
				if (droppedFiles.length > 0) {
					setIsProcessing(true);
					handleFiles(droppedFiles);
					setTimeout(() => setIsProcessing(false), 100);
				}
			},
			[disabled, isLoading, hasUploadingFiles, isProcessing, handleFiles]
		);

		const handleFileInput = useCallback(
			(e: React.ChangeEvent<HTMLInputElement>) => {
				e.stopPropagation();
				e.preventDefault();

				const selectedFiles = e.target.files;

				if (selectedFiles && selectedFiles.length > 0) {
					// Process the files first
					handleFiles(selectedFiles);

					// Reset input value AFTER processing to allow selecting the same file again
					if (fileInputRef.current) {
						fileInputRef.current.value = "";
					}
				} else {
					// Still reset the input even if no files
					if (fileInputRef.current) {
						fileInputRef.current.value = "";
					}
				}
			},
			[handleFiles, isProcessing, enableServerUpload]
		);

		const handleUploadClick = useCallback(
			(e: React.MouseEvent) => {
				e.preventDefault();
				e.stopPropagation();

				// Don't handle if disabled/loading
				if (disabled || isLoading) {
					return;
				}

				// Trigger file input click
				if (fileInputRef.current) {
					fileInputRef.current.click();
				}
			},
			[disabled, isLoading]
		);

		return (
			<div ref={ref} className={cn("space-y-4", className)} {...props}>
				{hasFiles ? (
					<>
						{/* Upload Area */}
						<div className="space-y-2">
							<div className="space-y-2">
								{displayFiles.map((file) => (
									<FileItem
										key={file.id}
										file={file}
										onRemove={handleFileRemove}
										onChange={handleFileChange}
										accept={accept}
									/>
								))}
							</div>
						</div>
					</>
				) : (
					<>
						{/* Upload Area */}
						<div
							className={cn(
								"relative transition-all duration-200",
								"border-border bg-background rounded-lg",
								"focus-within:ring-ring flex items-center justify-center focus-within:ring-2 focus-within:ring-offset-2",
								sizeClasses[size],
								variantClasses[variant],
								isDragOver && "border-primary bg-primary/5",
								disabled ||
									isLoading ||
									hasUploadingFiles ||
									isProcessing
									? "cursor-not-allowed opacity-50"
									: "hover:bg-accent/50 cursor-pointer",
								className
							)}
							onDragOver={handleDragOver}
							onDragLeave={handleDragLeave}
							onDrop={handleDrop}
						>
							<input
								ref={fileInputRef}
								type="file"
								accept={accept}
								multiple={multiple}
								onChange={handleFileInput}
								disabled={disabled || isLoading}
								style={{
									position: "absolute",
									inset: 0,
									width: "100%",
									height: "100%",
									opacity: 0,
									cursor:
										disabled || isLoading
											? "not-allowed"
											: "pointer",
									zIndex: 1,
								}}
							/>

							<div className="flex flex-1 items-center justify-between gap-6 text-center">
								<div className="bg-foreground-subtle flex h-10 w-10 items-center justify-center rounded-full">
									{isLoading || hasUploadingFiles ? (
										<Loader2 className="text-muted-foreground h-6 w-6 animate-spin" />
									) : (
										uploadIcon || (
											<ImageIcon className="text-muted h-6 w-6" />
										)
									)}
								</div>

								<div className="flex flex-2 flex-col items-start justify-center space-y-1">
									<p className="text-sm font-semibold">
										{hasUploadingFiles
											? "Uploading files..."
											: uploadText}
									</p>
									<p className="text-muted text-[10px]">
										{descriptionText}
									</p>
								</div>

								<div className="flex flex-1 items-center justify-end gap-4 text-center">
									<Button
										variant="outline"
										size="sm"
										type="button"
										disabled={
											disabled ||
											isLoading ||
											hasUploadingFiles ||
											isProcessing
										}
									>
										Browse Files
									</Button>
								</div>
							</div>
						</div>
					</>
				)}
			</div>
		);
	}
);
Uploader.displayName = "Uploader";

export { Uploader, FileItem };
export type { UploaderProps, FileItemProps };
