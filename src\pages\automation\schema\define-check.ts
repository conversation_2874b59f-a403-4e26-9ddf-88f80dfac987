import { z } from "zod"

export const DefineCheckSchema = z.object({
    checkItem: z.string().min(1, { message: "Check item is required" }),
    action: z.string().min(1, { message: "Action is required" }),
    formName: z.string().min(1, { message: "Form name is required" }),
    periodType: z.enum(["specific", "dynamic"]),
    numberOfChecks: z.number().min(1, { message: "Number of checks is required" }),
    ifMatch: z.array(z.string()).min(1, { message: "At least one action is required" }),
    elseMatch: z.array(z.string()).min(1, { message: "At least one action is required" }),
    checkInterval: z.number().min(1).optional(),
    intervalUnit: z.enum(["hours", "days", "minutes"]).optional(),
})

export type DefineCheckSchemaType = z.infer<typeof DefineCheckSchema>
