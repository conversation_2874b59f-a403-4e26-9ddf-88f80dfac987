export interface BannerMessage {
	id?: string;
	isEnabled: boolean;
	startDate?: Date;
	endDate?: Date;
	message: string;
	createdAt?: Date;
	updatedAt?: Date;
	locationId?: string;
	stationId?: string;
	organizationId?: string;
}

export interface CreateBannerData {
	isEnabled: boolean;
	startDate?: Date;
	endDate?: Date;
	message: string;
	locationId?: string;
	stationId?: string;
	organizationId?: string;
}

export interface UpdateBannerData extends Partial<CreateBannerData> {
	id: string;
}

export interface BannerResponse {
	data: BannerMessage;
	message: string;
	success: boolean;
}

export interface BannerListResponse {
	data: BannerMessage[];
	message: string;
	success: boolean;
	pagination?: {
		total: number;
		page: number;
		limit: number;
		totalPages: number;
	};
}
