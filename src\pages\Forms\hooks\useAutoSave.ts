import { useEffect, useRef } from "react";
import { useFormDraftStore } from "../store/slices/formDraftSlice";
import type { FormTypes } from "../types";

export const useAutoSave = (
	formData: FormTypes.FormDataType,
	interval: number = 30000 // 30 seconds
) => {
	const updateCurrentDraft = useFormDraftStore(
		(state) => state.updateCurrentDraft
	);
	const timeoutRef = useRef<NodeJS.Timeout | null>(null);
	const lastSavedRef = useRef<string>("");

	useEffect(() => {
		const currentDataString = JSON.stringify(formData);

		// Only auto-save if data has changed
		if (currentDataString !== lastSavedRef.current) {
			// Clear existing timeout
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}

			// Set new timeout for auto-save
			timeoutRef.current = setTimeout(() => {
				updateCurrentDraft(formData);
				lastSavedRef.current = currentDataString;
				console.log(
					"Form auto-saved at",
					new Date().toLocaleTimeString()
				);
			}, interval);
		}

		return () => {
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}
		};
	}, [formData, interval, updateCurrentDraft]);

	// Manual save function
	const saveNow = () => {
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
		}
		updateCurrentDraft(formData);
		lastSavedRef.current = JSON.stringify(formData);
	};

	return { saveNow };
};
