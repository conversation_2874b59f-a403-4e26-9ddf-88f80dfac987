import React from "react";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";
import {
	Select,
	SelectItem,
	SelectLabel,
	SelectContent,
	SelectGroup,
	SelectTrigger,
} from "@/components/ui/select";
import type {
	MultiSelectDropdownProps,
	MultiSelectChipProps,
	MultiSelectValueProps,
	MultiSelectOption,
} from "./types";

// Helper function to normalize options
const normalizeOptions = (
	options: MultiSelectOption[] | string[]
): MultiSelectOption[] => {
	return options.map((option) =>
		typeof option === "string" ? { value: option, label: option } : option
	);
};

// Helper function to get option label
const getOptionLabel = (
	value: string,
	options: MultiSelectOption[]
): string => {
	const option = options.find((opt) => opt.value === value);
	return option?.label || value;
};

// Chip component for selected values
const MultiSelectChip = React.memo<MultiSelectChipProps>(
	({ value, onRemove, className, removeButtonClassName, customRender }) => {
		const handleRemove = (event: React.MouseEvent) => {
			event.preventDefault();
			event.stopPropagation();
			event.nativeEvent.stopImmediatePropagation();

			setTimeout(() => {
				onRemove(value, event);
			}, 0);
		};

		if (customRender) {
			return (
				<>
					{customRender(value, () =>
						handleRemove({} as React.MouseEvent)
					)}
				</>
			);
		}

		return (
			<div
				className={cn(
					"flex items-center justify-between gap-x-1 rounded-xl border border-[#005893] bg-[#0058931A] px-2 py-1 text-[10px] font-medium text-[#27272A]",
					className
				)}
			>
				<span className="flex-1 truncate">{value}</span>
				<button
					type="button"
					onClick={handleRemove}
					onMouseDown={(e) => e.preventDefault()}
					onPointerDown={(e) => e.preventDefault()}
					className={cn(
						"cursor-pointer rounded-sm p-0.5 transition-colors hover:bg-black/10",
						removeButtonClassName
					)}
					aria-label={`Remove ${value}`}
				>
					<X className="h-3 w-3" />
				</button>
			</div>
		);
	}
);

MultiSelectChip.displayName = "MultiSelectChip";

// Custom select value component
const MultiSelectValue = React.memo<MultiSelectValueProps>(
	({
		value,
		onValueChange,
		placeholder,
		className,
		chipProps,
		maxSelectedDisplay = 3,
		customRenderChip,
		options,
	}) => {
		const selectedValues = value;

		if (selectedValues.length === 0) {
			return (
				<span className={cn("text-muted-foreground", className)}>
					{placeholder || "Select options..."}
				</span>
			);
		}

		const handleRemove = (
			selectedValue: string,
			event: React.MouseEvent
		) => {
			const updatedValues = selectedValues.filter(
				(v) => v !== selectedValue
			);
			onValueChange(updatedValues);
		};

		const displayValues = selectedValues.slice(0, maxSelectedDisplay);
		const remainingCount = selectedValues.length - maxSelectedDisplay;

		return (
			<div className={cn("flex flex-wrap gap-1", className)}>
				{displayValues.map((selectedValue: string) => {
					const label = options
						? getOptionLabel(selectedValue, options)
						: selectedValue;
					return (
						<MultiSelectChip
							key={selectedValue}
							value={label}
							onRemove={(_, event) =>
								handleRemove(selectedValue, event)
							}
							className={chipProps?.className}
							removeButtonClassName={
								chipProps?.removeButtonClassName
							}
							customRender={customRenderChip}
						/>
					);
				})}
				{remainingCount > 0 && (
					<span className="text-muted-foreground px-2 py-1 text-sm">
						+{remainingCount} more
					</span>
				)}
			</div>
		);
	}
);

MultiSelectValue.displayName = "MultiSelectValue";

// Main component
export const MultiSelectDropdown = React.forwardRef<
	HTMLButtonElement,
	MultiSelectDropdownProps
>(
	(
		{
			value,
			onValueChange,
			options,
			placeholder = "Select options...",
			label,
			id,
			className,
			disabled = false,
			error = false,
			selectTriggerProps,
			selectContentProps,
			selectGroupProps,
			selectItemProps,
			chipProps,
			emptyText = "No options available",
			maxSelectedDisplay = 3,
			customRenderChip,
			customRenderOption,
			...props
		},
		ref
	) => {
		const normalizedOptions = normalizeOptions(options);
		const selectedValues = value;

		const handleValueChange = (newValue: string | string[]) => {
			// Ensure we always pass an array
			const arrayValue = Array.isArray(newValue) ? newValue : [newValue];
			onValueChange(arrayValue);
		};

		return (
			<Select
				multiSelect
				value={value}
				onValueChange={handleValueChange}
				disabled={disabled}
				aria-label={label}
				{...props}
			>
				<SelectTrigger
					ref={ref}
					className={cn(
						"scrollbar-hide w-full items-start overflow-auto",
						error &&
							"border-destructive focus-visible:ring-destructive",
						disabled && "cursor-not-allowed opacity-50",
						selectTriggerProps?.className,
						className
					)}
					style={{
						height: "max-content",
						// maxHeight: "10rem",
						display: "flex",
						alignItems: "flex-start",
						position: "relative",
						...selectTriggerProps?.style,
					}}
					id={id}
					aria-label={label}
					disabled={disabled}
				>
					<MultiSelectValue
						value={value}
						onValueChange={onValueChange}
						placeholder={placeholder}
						chipProps={chipProps}
						maxSelectedDisplay={maxSelectedDisplay}
						customRenderChip={customRenderChip}
						options={normalizedOptions}
					/>
				</SelectTrigger>
				<SelectContent
					className={cn("z-[9999]", selectContentProps?.className)}
					aria-label="select-options"
				>
					<SelectGroup className={selectGroupProps?.className}>
						{label && (
							<SelectLabel className="px-2 py-1.5 text-sm font-medium">
								{label}
							</SelectLabel>
						)}

						{normalizedOptions.length === 0 ? (
							<div className="text-muted-foreground px-2 py-1.5 text-xs">
								{emptyText}
							</div>
						) : (
							normalizedOptions.map((option) => (
								<SelectItem
									key={option.value}
									value={option.value}
									disabled={option.disabled}
									className={cn(
										"cursor-pointer px-2 py-1.5 text-xs",
										option.disabled &&
											"cursor-not-allowed opacity-50",
										selectItemProps?.className
									)}
								>
									{customRenderOption
										? customRenderOption(option)
										: option.label}
								</SelectItem>
							))
						)}
					</SelectGroup>
				</SelectContent>
			</Select>
		);
	}
);

MultiSelectDropdown.displayName = "MultiSelectDropdown";
