import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { CiSearch } from "react-icons/ci";
import { GoTag } from "react-icons/go";
import { IoIosInformationCircleOutline } from "react-icons/io";
import { LuLayoutDashboard } from "react-icons/lu";
import { IoNotificationsOutline } from "react-icons/io5";
import { FiUser } from "react-icons/fi";
import { LuCalendar } from "react-icons/lu";
import { LuC<PERSON> } from "react-icons/lu";
import { MdOutlineNotificationsActive } from "react-icons/md";
import { FaRegTrashCan } from "react-icons/fa6";
import { Tooltip } from 'react-tooltip';

const defaultStyles = {
    backgroundColor: "white",
    opacity: 1,
    color: "#27272A",
    boxShadow: "2px 2px 10px 0px rgba(0, 0, 0, 0.1)",
    borderRadius: "8px",
}

export default function FollowUp() {
    return (
        <div className="w-full py-2 px-2 border border-solid border-[#E4E4E7] bg-[#F6F6F6DB] rounded-lg h-[calc(100vh-140px)] flex flex-col">

            {/* search input */}
            <div className="relative">
                <CiSearch className="absolute left-3 top-2 text-base text-[#27272A]" />
                <input
                    type="text"
                    placeholder="Search follow-up appointments"
                    className="w-full bg-white py-1.5 px-3 pl-8 rounded-md border border-solid border-[#E4E4E7] text-xs"
                />
            </div>

            {/* actions */}
            <div className="flex items-center justify-between mt-2">
                <div className="flex items-center gap-x-1.5">
                    <Checkbox />
                    <label htmlFor="select-all" className="text-[#898EAA] text-xs">
                        Select All
                    </label>
                </div>
                <div className="space-x-1.5">
                    <Button variant="outline" className="py-1.5 px-1.5 h-7">
                        <IoNotificationsOutline size={14} color="#898EAA" />
                    </Button>
                    <Button variant="outline" className="py-1.5 px-1.5 h-7">
                        <FaRegTrashCan size={14} color="#898EAA" />
                    </Button>
                </div>
            </div>

            {/* appointment list*/}
            <div className="flex flex-col gap-y-3 mt-3 flex-1 overflow-y-auto scrollbar-hide">
                {Array.from({ length: 6 }).map((_, index) => (
                    <div key={index} className="border border-[#E4E4E7] p-2 bg-white rounded-lg space-y-2 transition-all duration-300 hover:border-[#D09303] hover:shadow-md">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-x-2">
                                <Checkbox />
                                <div className="flex items-center gap-x-2">
                                    <h3 className="text-[#18181B] text-xs font-regular">Michael Thompson</h3>
                                    <div className="flex items-center gap-x-1">
                                        <div className="bg-[#FAFAFA] py-1 px-1 rounded-md">
                                            <IoIosInformationCircleOutline color="#71717A" className="text-base" />
                                        </div>
                                        <div className="bg-[#FAFAFA] py-1 px-1 rounded-md">
                                            <GoTag color="#71717A" className="text-sm" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-[#FEF3C7] text-[#D09303] px-2 py-1 rounded text-xs font-medium">
                                Follow Up
                            </div>
                        </div>
                        <p className="text-[#52525B] text-xs mt-1 flex items-center gap-x-1">
                            <LuLayoutDashboard
                                data-tooltip-id="base-tooltip"
                                data-tooltip-html="<div style='display: flex; align-items: center; gap: 4px;'>Book Via <p style='font-weight: 600; margin: 0;'>Phone</p></div>"
                                data-tooltip-place="top-start"
                                color="#52525B"
                                className="text-xs"
                            />
                            <span className="text-[#71717A]">
                                Dr. Michael Chen
                            </span>
                        </p>
                        <h1 className="flex items-center gap-x-1">
                            <FiUser
                                data-tooltip-id="base-tooltip"
                                data-tooltip-content="In Person"
                                color="#042C4D" />
                            <span className="text-[#71717A] text-xs font-medium">
                                Post-Surgery Follow Up
                            </span>
                        </h1>
                        <div className="flex items-center gap-x-3 text-[#27272A]">
                            <div className="flex items-center gap-x-1">
                                <LuCalendar className="opacity-50 text-base" />
                                <p className="text-xs">18 Apr 2025</p>
                            </div>
                            <div className="flex items-center gap-x-1">
                                <LuClock className="opacity-50 text-base" />
                                <p className="text-xs">
                                    2:00 pm - 3:00 pm
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-x-2 text-xs">
                            <span className="text-[#71717A]">Previous visit:</span>
                            <span className="text-[#27272A] font-medium">Surgery - 28 Mar 2025</span>
                        </div>
                        <div className="mt-3 pt-1.5 pb-1 flex items-center justify-between border-t border-solid border-[#E4E4E7]">
                            <div className="flex items-center gap-x-2">
                                <div
                                    data-tooltip-id="complete"
                                    data-tooltip-content="Mark Complete"
                                    className="bg-[#F0FDF4] py-1 px-1 rounded-md">
                                    <svg width="16" height="16" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10.2803 3.21967C10.5732 3.51256 10.5732 3.98744 10.2803 4.28033L5.28033 9.28033C4.98744 9.57322 4.51256 9.57322 4.21967 9.28033L1.71967 6.78033C1.42678 6.48744 1.42678 6.01256 1.71967 5.71967C2.01256 5.42678 2.48744 5.42678 2.78033 5.71967L4.75 7.68934L9.21967 3.21967C9.51256 2.92678 9.98744 2.92678 10.2803 3.21967Z" fill="#16A34A" />
                                    </svg>
                                </div>
                                <div
                                    data-tooltip-id="base-tooltip"
                                    data-tooltip-content="Message"
                                    className="bg-[#FAFAFA] py-1 px-1 rounded-md">
                                    <svg width="16" height="16" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10 2.5C10 2.36739 9.94728 2.24025 9.85352 2.14648C9.75975 2.05272 9.63261 2 9.5 2H2.5C2.36739 2 2.24025 2.05272 2.14648 2.14648C2.05272 2.24025 2 2.36739 2 2.5V9.29297L3.14648 8.14648L3.18311 8.11328C3.27208 8.04036 3.38395 8 3.5 8H9.5C9.63261 8 9.75975 7.94728 9.85352 7.85352C9.94728 7.75975 10 7.63261 10 7.5V2.5ZM8.5 5.5C8.77614 5.5 9 5.72386 9 6C9 6.27614 8.77614 6.5 8.5 6.5H3.5C3.22386 6.5 3 6.27614 3 6C3 5.72386 3.22386 5.5 3.5 5.5H8.5ZM6.5 3.5C6.77614 3.5 7 3.72386 7 4C7 4.27614 6.77614 4.5 6.5 4.5H3.5C3.22386 4.5 3 4.27614 3 4C3 3.72386 3.22386 3.5 3.5 3.5H6.5ZM11 7.5C11 7.89783 10.8419 8.27924 10.5605 8.56055C10.2792 8.84185 9.89783 9 9.5 9H3.70703L1.85352 10.8535C1.71052 10.9965 1.49543 11.0393 1.30859 10.9619C1.12179 10.8845 1 10.7022 1 10.5V2.5C1 2.10218 1.15815 1.72076 1.43945 1.43945C1.72076 1.15815 2.10218 1 2.5 1H9.5C9.89783 1 10.2792 1.15815 10.5605 1.43945C10.8419 1.72076 11 2.10218 11 2.5V7.5Z" fill="#71717A" />
                                    </svg>
                                </div>
                                <div
                                    data-tooltip-id="base-tooltip"
                                    data-tooltip-content="Reschedule"
                                    className="bg-[#FAFAFA] py-1 px-1 rounded-md">
                                    <svg width="16" height="16" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10.5 8C10.5 6.61929 9.38071 5.5 8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8ZM7.5 7C7.5 6.72386 7.72386 6.5 8 6.5C8.27614 6.5 8.5 6.72386 8.5 7V7.90967L9.0625 8.35938L9.10059 8.39355C9.28148 8.57105 9.30235 8.86034 9.14062 9.0625C8.97888 9.26469 8.69189 9.308 8.479 9.17041L8.4375 9.14062L7.6875 8.54053C7.56892 8.44564 7.5 8.30177 7.5 8.1499V7ZM10 3.75V3C10 2.86739 9.94728 2.74025 9.85352 2.64648C9.77144 2.56441 9.66382 2.51379 9.54932 2.50244L9.5 2.5H8.5V3C8.5 3.27614 8.27614 3.5 8 3.5C7.72386 3.5 7.5 3.27614 7.5 3V2.5H4.5V3C4.5 3.27614 4.27614 3.5 4 3.5C3.72386 3.5 3.5 3.27614 3.5 3V2.5H2.5C2.36739 2.5 2.24025 2.55272 2.14648 2.64648C2.05272 2.74025 2 2.86739 2 3V4.5H4C4.27614 4.5 4.5 4.72386 4.5 5C4.5 5.27614 4.27614 5.5 4 5.5H2V10C2 10.1326 2.05272 10.2597 2.14648 10.3535C2.24025 10.4473 2.36739 10.5 2.5 10.5H4.25C4.52614 10.5 4.75 10.7239 4.75 11C4.75 11.2761 4.52614 11.5 4.25 11.5H2.5C2.10218 11.5 1.72076 11.3419 1.43945 11.0605C1.15815 10.7792 1 10.3978 1 10V3C1 2.60218 1.15815 2.22076 1.43945 1.93945C1.72076 1.65815 2.10218 1.5 2.5 1.5H3.5V1C3.5 0.723858 3.72386 0.5 4 0.5C4.27614 0.5 4.5 0.723858 4.5 1V1.5H7.5V1C7.5 0.723858 7.72386 0.5 8 0.5C8.27614 0.5 8.5 0.723858 8.5 1V1.5H9.5L9.64844 1.50732C9.99176 1.54145 10.3144 1.69335 10.5605 1.93945C10.8419 2.22076 11 2.60217 11 3V3.75C11 4.02614 10.7761 4.25 10.5 4.25C10.2239 4.25 10 4.02614 10 3.75ZM11.5 8C11.5 9.933 9.933 11.5 8 11.5C6.067 11.5 4.5 9.933 4.5 8C4.5 6.067 6.067 4.5 8 4.5C9.933 4.5 11.5 6.067 11.5 8Z" fill="#71717A" />
                                    </svg>
                                </div>
                                <div
                                    data-tooltip-id="schedule-next"
                                    data-tooltip-content="Schedule Next Follow-up"
                                    className="bg-[#FEF3C7] py-1 px-1 rounded-md">
                                    <svg width="16" height="16" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M6 1C6.27614 1 6.5 1.22386 6.5 1.5V5.79289L8.14645 4.14645C8.34171 3.95118 8.65829 3.95118 8.85355 4.14645C9.04882 4.34171 9.04882 4.65829 8.85355 4.85355L6.35355 7.35355C6.15829 7.54882 5.84171 7.54882 5.64645 7.35355L3.14645 4.85355C2.95118 4.65829 2.95118 4.34171 3.14645 4.14645C3.34171 3.95118 3.65829 3.95118 3.85355 4.14645L5.5 5.79289V1.5C5.5 1.22386 5.72386 1 6 1ZM1.5 9C1.22386 9 1 9.22386 1 9.5C1 9.77614 1.22386 10 1.5 10H10.5C10.7761 10 11 9.77614 11 9.5C11 9.22386 10.7761 9 10.5 9H1.5Z" fill="#D09303" />
                                    </svg>
                                </div>
                            </div>
                            <div
                                data-tooltip-id="base-tooltip"
                                data-tooltip-content="Send Reminder"
                                className="bg-[#FAFAFA] py-1 px-1 rounded-md">
                                <MdOutlineNotificationsActive color="#D09303" className="text-base" />
                            </div>
                        </div>
                    </div>
                ))}
            </div>
            <Tooltip id="complete" style={{
                ...defaultStyles,
                color: "#16A34A",
            }} />
            <Tooltip id="schedule-next" style={{
                ...defaultStyles,
                color: "#D09303",
            }} />
            <Tooltip id="base-tooltip" style={defaultStyles} />
        </div>
    )
}