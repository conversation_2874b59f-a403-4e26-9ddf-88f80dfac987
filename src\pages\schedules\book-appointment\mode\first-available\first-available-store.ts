import { create } from 'zustand';

interface FirstAvailableState {
    // Date and time selection
    selectedDate: Date;

    // Service and provider selection
    selectedStations: string[];
    selectedProviders: string[];

    // Appointment method selection (only one can be selected)
    selectedAppointmentMethod: string;

    // UI state
    isLoading: boolean;

    // Actions
    setSelectedDate: (date: Date) => void;
    setSelectedStations: (stations: string[]) => void;
    setSelectedProviders: (providers: string[]) => void;
    setSelectedAppointmentMethod: (method: string) => void;
    setLoading: (loading: boolean) => void;
    reset: () => void;
}

const initialState = {
    selectedDate: new Date(),
    selectedStations: [],
    selectedProviders: [],
    selectedAppointmentMethod: "in-person",
    isLoading: false,
};

export const useFirstAvailableStore = create<FirstAvailableState>((set, get) => ({
    ...initialState,

    setSelectedDate: (date: Date) => set({ selectedDate: date }),

    setSelectedStations: (stations: string[]) => {
        set({ selectedStations: stations });
        // Reset providers when stations change
        set({ selectedProviders: [] });
    },

    setSelectedProviders: (providers: string[]) => set({ selectedProviders: providers }),

    setSelectedAppointmentMethod: (method: string) => set({ selectedAppointmentMethod: method }),

    setLoading: (loading: boolean) => set({ isLoading: loading }),

    reset: () => set(initialState),
})); 