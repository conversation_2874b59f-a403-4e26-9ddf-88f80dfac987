import React, { createContext, useContext, useCallback } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { stationsApi } from "../api/stationsApi";
import { useStations } from "../hooks/useStations";
import type { CreateProviderStationRequest } from "../types";

interface StationContextValue {
	// Data and loading states
	stations: any[];
	isLoading: boolean;
	error: Error | null;

	// Actions
	refetchStations: () => void;
	createStation: (
		data: CreateProviderStationRequest & { imageFile?: File }
	) => Promise<void>;

	// Configuration
	locationId?: string;
	organizationId?: number;
}

interface StationProviderProps {
	children: React.ReactNode;
	locationId?: string | number;
	organizationId?: number;
}

const StationContext = createContext<StationContextValue | undefined>(
	undefined
);

export const StationProvider: React.FC<StationProviderProps> = ({
	children,
	locationId,
	organizationId,
}) => {
	const queryClient = useQueryClient();

	// Convert locationId to string if it's a number
	const locationIdString =
		typeof locationId === "number" ? locationId.toString() : locationId;

	// Fetch stations data
	const {
		data: stationsResponse,
		isLoading,
		error,
		refetch: refetchStations,
	} = useStations({
		locationId: locationIdString,
		organizationId,
		enabled: !!locationIdString && !!organizationId,
	});

	// Create station mutation
	const createStationMutation = useMutation({
		mutationFn: async (
			data: CreateProviderStationRequest & { imageFile?: File }
		) => {
			if (!locationIdString || !organizationId) {
				throw new Error("LocationId and organizationId are required");
			}

			return await stationsApi.createStation(
				data,
				locationIdString,
				organizationId
			);
		},
		onSuccess: () => {
			// Invalidate and refetch stations data
			queryClient.invalidateQueries({
				queryKey: ["stations", locationIdString, organizationId],
			});

			// Also invalidate all stations if needed
			queryClient.invalidateQueries({
				queryKey: ["allStations", organizationId],
			});
		},
		onError: (error) => {
			console.error("Error creating station:", error);
		},
	});

	const createStation = useCallback(
		async (data: CreateProviderStationRequest & { imageFile?: File }) => {
			await createStationMutation.mutateAsync(data);
		},
		[createStationMutation]
	);

	const stations = stationsResponse?.data || [];

	const value: StationContextValue = {
		stations,
		isLoading,
		error,
		refetchStations,
		createStation,
		locationId: locationIdString,
		organizationId,
	};

	return (
		<StationContext.Provider value={value}>
			{children}
		</StationContext.Provider>
	);
};

export const useStationContext = (): StationContextValue => {
	const context = useContext(StationContext);
	if (context === undefined) {
		throw new Error(
			"useStationContext must be used within a StationProvider"
		);
	}
	return context;
};
