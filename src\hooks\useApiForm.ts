import { useCallback } from "react";
import type { FieldValues, UseFormSetError, Path } from "react-hook-form";
import { useUIStore } from "@/stores/uiStore";

interface ApiError {
	success: false;
	message: string;
	error: string;
	errors?: Record<string, string[]>;
}

interface UseApiFormOptions<T extends FieldValues> {
	setError: UseFormSetError<T>;
	showSuccessToast?: boolean;
	showErrorToast?: boolean;
	customErrorMessages?: Record<number, string>;
}

export const useApiForm = <T extends FieldValues>({
	setError,
	showSuccessToast = true,
	showErrorToast = true,
	customErrorMessages = {},
}: UseApiFormOptions<T>) => {
	const { addToast } = useUIStore();

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const handleApiError = useCallback(
		(error: any) => {
			const status = error.response?.status;
			const errorData = error.response?.data as ApiError;

			if (status === 422 && errorData?.errors) {
				// Handle validation errors by setting them on the form
				Object.entries(errorData.errors).forEach(
					([field, messages]) => {
						if (messages && messages.length > 0) {
							setError(field as Path<T>, {
								type: "server",
								message: messages[0],
							});
						}
					}
				);

				// Show a general validation error toast
				if (showErrorToast) {
					addToast({
						type: "error",
						title: "Validation Error",
						message:
							errorData.message ||
							"Please check your input and try again",
					});
				}
			} else if (showErrorToast && status !== 401 && status !== 403) {
				// For other errors, let the interceptor handle them unless we have custom messages
				const customMessage = customErrorMessages[status];
				if (customMessage) {
					addToast({
						type: "error",
						title: "Error",
						message: customMessage,
					});
				}
				// If no custom message, let the interceptor handle it
			}

			return error;
		},
		[
			setError,
			addToast,
			showSuccessToast,
			showErrorToast,
			customErrorMessages,
		]
	);

	const handleApiSuccess = useCallback(
		(message?: string) => {
			if (showSuccessToast) {
				addToast({
					type: "success",
					title: "Success",
					message: message || "Operation completed successfully",
				});
			}
		},
		[addToast, showSuccessToast]
	);

	return {
		handleApiError,
		handleApiSuccess,
	};
};
