import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { UploadCard } from "@/components/ui-components/Upload";
import type { UploadedFile } from "@/components/ui-components/Upload";
import { InputText } from "@/components/common/InputText";
import { InputPhone } from "@/components/common/InputPhone";
import { Separator } from "@/components/ui/seperator";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import {
	useOrganization,
	useUpdateOrganization,
	useBusinessCategories,
} from "@/features/organizations/hooks/useOrganizations";
import { toast } from "sonner";
import type { Organization } from "@/features/organizations/api/organizationsApi";
import { Country, State, City } from "country-state-city";
import type { ICountry, IState, ICity } from "country-state-city";
import { uploadImage } from "@/lib/api/upload";

const OrganizationInformation: React.FC = () => {
	const { organizationId } = useOrganizationContext();

	// Fetch organization data
	const {
		data: organizationResponse,
		isLoading,
		error,
		refetch,
	} = useOrganization(organizationId?.toString() || "", !!organizationId);

	// Fetch business categories
	const {
		data: businessCategories,
		isLoading: isCategoriesLoading,
		error: categoriesError,
	} = useBusinessCategories();

	// Update organization mutation
	const updateOrganizationMutation = useUpdateOrganization();

	// Location data
	const [countries, setCountries] = useState<ICountry[]>([]);
	const [states, setStates] = useState<IState[]>([]);
	const [cities, setCities] = useState<ICity[]>([]);

	// Form states
	const [orgName, setOrgName] = useState("");
	const [businessCategoryId, setBusinessCategoryId] = useState(1);
	const [phone, setPhone] = useState("");
	const [address, setAddress] = useState("");
	const [countryCode, setCountryCode] = useState("");
	const [stateCode, setStateCode] = useState("");
	const [cityName, setCityName] = useState("");
	const [zipCode, setZipCode] = useState("");
	const [logoFiles, setLogoFiles] = useState<UploadedFile[]>([]);
	const [logoUrl, setLogoUrl] = useState("");

	// Upload card specific state
	const [isUploaded, setIsUploaded] = useState(false);
	const [uploadedFileName, setUploadedFileName] = useState("");
	const [uploadedFileSize, setUploadedFileSize] = useState("");
	const [uploadedFilePreview, setUploadedFilePreview] = useState("");
	const [selectedFile, setSelectedFile] = useState<File | null>(null);

	// Load countries on component mount
	useEffect(() => {
		const allCountries = Country.getAllCountries();
		setCountries(allCountries);
	}, []);

	// Load states when country changes
	useEffect(() => {
		if (countryCode) {
			const countryStates = State.getStatesOfCountry(countryCode);
			setStates(countryStates);
			// Reset state and city when country changes
			setStateCode("");
			setCityName("");
			setCities([]);
		}
	}, [countryCode]);

	// Load cities when state changes
	useEffect(() => {
		if (countryCode && stateCode) {
			const stateCities = City.getCitiesOfState(countryCode, stateCode);
			setCities(stateCities);
			// Reset city when state changes
			setCityName("");
		}
	}, [countryCode, stateCode]);

	// Populate form when data is loaded
	useEffect(() => {
		if (organizationResponse?.data) {
			const org = organizationResponse.data;
			setOrgName(org.name || "");
			setBusinessCategoryId(org.business_category_id || 1);
			setPhone(org.phone_number || "");
			setAddress(org.address || "");

			// Find country by name and set country code
			const foundCountry = countries.find((c) => c.name === org.country);
			if (foundCountry) {
				setCountryCode(foundCountry.isoCode);
			}

			// Set state and city names
			setStateCode(org.state || "");
			setCityName(org.city || "");
			setZipCode(org.zip_code || "");
			setLogoUrl(org.logo_url || "");

			// Set upload state based on existing logo
			if (org.logo_url) {
				setIsUploaded(true);
				setUploadedFilePreview(org.logo_url);
				// Extract filename from URL if possible, otherwise use default
				const urlParts = org.logo_url.split("/");
				const filename = urlParts[urlParts.length - 1] || "logo.png";
				setUploadedFileName(filename);
				setUploadedFileSize("Unknown size");
			} else {
				setIsUploaded(false);
				setUploadedFileName("");
				setUploadedFileSize("");
				setUploadedFilePreview("");
				setSelectedFile(null);
			}
		}
	}, [organizationResponse, countries]);

	// Upload card handlers
	const handleBrowseClick = () => {
		const input = document.createElement("input");
		input.type = "file";
		input.accept = ".jpg,.jpeg,.png";
		input.onchange = (e) => {
			const file = (e.target as HTMLInputElement).files?.[0];
			if (file) {
				handleFileUpload(file);
			}
		};
		input.click();
	};

	const handleFileUpload = (file: File) => {
		if (file.size > 2 * 1024 * 1024) {
			toast.error("File size must be less than 2MB");
			return;
		}

		const preview = URL.createObjectURL(file);
		setIsUploaded(true);
		setUploadedFileName(file.name);
		setUploadedFileSize(`${(file.size / 1024 / 1024).toFixed(2)} MB`);
		setUploadedFilePreview(preview);
		setSelectedFile(file); // Store the actual file for upload
		// Don't set logoUrl yet - we'll set it after successful upload
	};

	const handleRemoveFile = () => {
		setIsUploaded(false);
		setUploadedFileName("");
		setUploadedFileSize("");
		setUploadedFilePreview("");
		setSelectedFile(null);
		setLogoUrl("");
	};

	const handleChangeFile = () => {
		handleBrowseClick();
	};

	const handleDragOver = (e: React.DragEvent) => {
		e.preventDefault();
	};

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();
		const file = e.dataTransfer.files[0];
		if (file) {
			handleFileUpload(file);
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!organizationId) {
			toast.error("Organization ID not found");
			return;
		}

		try {
			let finalLogoUrl = logoUrl;

			// Upload image if a new file is selected
			if (selectedFile) {
				try {
					finalLogoUrl = await uploadImage(selectedFile);
					setLogoUrl(finalLogoUrl); // Update the logoUrl state with the uploaded URL
				} catch (uploadError) {
					console.error("Failed to upload image:", uploadError);
					toast.error("Failed to upload image. Please try again.");
					return; // Don't proceed with form submission if image upload fails
				}
			}

			// Get country name from country code
			const selectedCountry = countries.find(
				(c) => c.isoCode === countryCode
			);
			const countryName = selectedCountry?.name || "";

			// Get state name from state code
			const selectedState = states.find((s) => s.isoCode === stateCode);
			const stateName = selectedState?.name || "";

			const updateData: Partial<Organization> = {
				name: orgName,
				address,
				country: countryName,
				state: stateName,
				city: cityName,
				zip_code: zipCode,
				phone_number: phone,
				business_category_id: businessCategoryId,
				logo_url: finalLogoUrl,
			};

			await updateOrganizationMutation.mutateAsync({
				id: organizationId.toString(),
				data: updateData,
			});

			toast.success("Organization updated successfully");
			// Clear the selected file since it's now uploaded
			setSelectedFile(null);
			// Refetch organization data to get updated values
			refetch();
		} catch (error) {
			console.error("Failed to update organization:", error);
			toast.error("Failed to update organization");
		}
	};

	const handleCancel = () => {
		// Reset form to original values
		if (organizationResponse?.data) {
			const org = organizationResponse.data;
			setOrgName(org.name || "");
			setBusinessCategoryId(org.business_category_id || 1);
			setPhone(org.phone_number || "");
			setAddress(org.address || "");

			// Find country by name and set country code
			const foundCountry = countries.find((c) => c.name === org.country);
			if (foundCountry) {
				setCountryCode(foundCountry.isoCode);
			}

			setStateCode(org.state || "");
			setCityName(org.city || "");
			setZipCode(org.zip_code || "");
			setLogoUrl(org.logo_url || "");
			setLogoFiles([]);

			// Reset upload state
			setIsUploaded(!!org.logo_url);
			setUploadedFileName("");
			setUploadedFileSize("");
			setUploadedFilePreview(org.logo_url || "");
			setSelectedFile(null);
		}
	};

	// Show loading state
	if (isLoading || isCategoriesLoading) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-[#005893]"></div>
					<p className="text-gray-600">Loading organization...</p>
				</div>
			</div>
		);
	}

	// Show error state
	if (error || categoriesError) {
		return (
			<div className="flex min-h-[400px] items-center justify-center">
				<div className="text-center">
					<p className="mb-4 text-red-600">
						Failed to load organization data
					</p>
					<Button onClick={() => refetch()} variant="outline">
						Try Again
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className="max-w-2xl">
			<form onSubmit={handleSubmit} className="">
				<div className="flex flex-col gap-3">
					<h2 className="mb-4 text-xl font-semibold">
						Organization Information
					</h2>
					<Separator />
					<div className="grid grid-cols-2 gap-6">
						<div className="col-span-2">
							<InputText
								variant="with-label"
								label="Organization Name *"
								id="org-name"
								type="text"
								value={orgName}
								onChange={(e) => setOrgName(e.target.value)}
								required
							/>
						</div>
						<div className="col-span-2">
							<Label htmlFor="org-type">Type</Label>
							<Select
								value={businessCategoryId.toString()}
								onValueChange={(value) =>
									setBusinessCategoryId(
										parseInt(value as string)
									)
								}
							>
								<SelectTrigger
									className="col-span-2 mt-2 w-full"
									id="org-type"
								>
									<SelectValue placeholder="Select type" />
								</SelectTrigger>
								<SelectContent>
									{businessCategories?.map((category) => (
										<SelectItem
											key={category.id}
											value={category.id.toString()}
										>
											{category.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
						<div className="col-span-2 flex flex-col gap-2">
							<Label htmlFor="org-phone">Phone *</Label>
							<InputPhone
								variant="with-country-dropdown"
								value={phone}
								onChange={(value) => setPhone(value)}
								defaultCountry="US"
								placeholder="Enter phone number"
								className="w-full"
								showFlag={true}
								format="international"
								searchable={true}
								showValidation={true}
							/>
						</div>
						<div className="col-span-2">
							<Label htmlFor="org-address">Address *</Label>
							<Input
								id="org-address"
								type="text"
								placeholder="456 Elm Avenue"
								value={address}
								className="mt-2"
								onChange={(e) => setAddress(e.target.value)}
								required
							/>
						</div>
						<div className="col-span-2">
							<Label htmlFor="org-country">Country</Label>
							<Select
								value={countryCode}
								onValueChange={(value) =>
									setCountryCode(value as string)
								}
							>
								<SelectTrigger
									className="mt-2 w-full"
									id="org-country"
								>
									<SelectValue placeholder="Select country" />
								</SelectTrigger>
								<SelectContent>
									{countries.map((country) => (
										<SelectItem
											key={country.isoCode}
											value={country.isoCode}
										>
											{country.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
						<div>
							<Label htmlFor="org-state">State/Province</Label>
							<Select
								value={stateCode}
								onValueChange={(value) =>
									setStateCode(value as string)
								}
								disabled={!countryCode}
							>
								<SelectTrigger
									className="mt-2 w-full"
									id="org-state"
								>
									<SelectValue
										placeholder={
											countryCode
												? "Select state/province"
												: "Select country first"
										}
									/>
								</SelectTrigger>
								<SelectContent>
									{states.map((state) => (
										<SelectItem
											key={state.isoCode}
											value={state.isoCode}
										>
											{state.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
						<div>
							<Label htmlFor="org-city">City</Label>
							<Select
								value={cityName}
								onValueChange={(value) =>
									setCityName(value as string)
								}
								disabled={!stateCode}
							>
								<SelectTrigger
									className="mt-2 w-full"
									id="org-city"
								>
									<SelectValue
										placeholder={
											stateCode
												? "Select city"
												: "Select state first"
										}
									/>
								</SelectTrigger>
								<SelectContent>
									{cities.map((city) => (
										<SelectItem
											key={city.name}
											value={city.name}
										>
											{city.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
						<div className="col-span-2">
							<Label htmlFor="org-zip">ZIP Code</Label>
							<Input
								id="org-zip"
								type="text"
								className="mt-2 w-full"
								placeholder="94102"
								value={zipCode}
								onChange={(e) => setZipCode(e.target.value)}
								required
							/>
						</div>
					</div>
					<div className="flex justify-end gap-2">
						<Button
							type="button"
							variant="outline"
							onClick={handleCancel}
							disabled={updateOrganizationMutation.isPending}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							disabled={updateOrganizationMutation.isPending}
							className="bg-[#005893] hover:bg-[#004a7a]"
						>
							{updateOrganizationMutation.isPending
								? "Saving..."
								: "Save Changes"}
						</Button>
					</div>
				</div>
				<div className="col-span-2 gap-6">
					<UploadCard
						title="Click or drag file here to upload logo"
						description="JPG or PNG, min 800x800px, max 2MB"
						buttonText="Browse Files"
						variant="centered"
						width="w-[400px]"
						onBrowseClick={handleBrowseClick}
						onDragOver={handleDragOver}
						onDrop={handleDrop}
						accept=".jpg,.jpeg,.png"
						maxSize="2MB"
						isUploaded={isUploaded}
						fileName={uploadedFileName}
						fileSize={uploadedFileSize}
						filePreview={uploadedFilePreview}
						onRemove={handleRemoveFile}
						onChange={handleChangeFile}
					/>
				</div>
			</form>
		</div>
	);
};

export default OrganizationInformation;
