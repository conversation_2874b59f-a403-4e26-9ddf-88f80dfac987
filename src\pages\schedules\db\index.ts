export const locations = [
    "New York Medical Center",
    "Las Vegas Medical Center",
    "Los Angeles Medical Center",
    "Chicago Medical Center",
    "Houston Medical Center",
    "Miami Medical Center",
    "San Francisco Medical Center",
    "Seattle Medical Center",
]

export const providers = [
    "Dr. <PERSON>",
    "Dr. <PERSON>",
    "Dr. <PERSON>",
    "<PERSON><PERSON> <PERSON><PERSON>",
]

export const services = [
    "General Consultation",
    "Physical Exam",
    "Lab Test",
    "Imaging",
    "Immunizations",
    "Medication Management",
    "Mental Health",
    "Nutrition",
    "Pediatrics",
    "Primary Care",
    "Specialty Care",
]

export const resources = [
    { resourceId: 1, resourceTitle: 'Dr. <PERSON>', color: "#851AAC" },
    { resourceId: 2, resourceTitle: 'Dr. <PERSON>', color: "#005893" },
    { resourceId: 3, resourceTitle: 'Dr. <PERSON>', color: "#56A8DE" },
    { resourceId: 4, resourceTitle: 'Dr. <PERSON>', color: "#FFA500" },
    { resourceId: 5, resourceTitle: 'Dr. <PERSON>', color: "#FF5733" },
    { resourceId: 6, resourceTitle: 'Dr. <PERSON>', color: "#33C1FF" },
    { resourceId: 7, resourceTitle: 'Dr. <PERSON>', color: "#33FF57" },
    { resourceId: 8, resourceTit<PERSON>: '<PERSON>. <PERSON> Green', color: "#FF33A8" },
];

export const event<PERSON>it<PERSON> = [
    '<PERSON> U<PERSON>', '<PERSON> <PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON> <PERSON>', '<PERSON>', '<PERSON> <PERSON>', '<PERSON> <PERSON>',
    '<PERSON> <PERSON>', '<PERSON>', '<PERSON>', '<PERSON> <PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON> <PERSON>', '<PERSON> <PERSON>',
    'Charlotte King', 'Logan Wright', 'Amelia Green', 'Ethan Adams',
    'Harper Nelson', 'Benjamin Carter', 'Evelyn Mitchell', 'Jack Perez',
    'Abigail Roberts', 'Henry Campbell', 'Emily Stewart', 'Sebastian Morris',
    'Grace Rogers', 'Alexander Reed', 'Chloe Cook', 'Jacob Morgan',
    'Scarlett Bell', 'William Murphy', 'Victoria Bailey', 'James Rivera',
    'Penelope Cooper', 'Elijah Richardson', 'Layla Cox', 'Matthew Howard',
    'Zoe Ward', 'David Torres', 'Lily Peterson', 'Carter Gray', 'Hannah Ramirez'
];

// Helper: get staggered hours for each doctor
const STAGGERED_HOURS = [
    [0, 3, 6, 9, 11],    // Dr. 1
    [1, 4, 7, 10, 12],   // Dr. 2
    [2, 5, 8, 11, 0],    // Dr. 3
    [3, 6, 9, 12, 1],    // Dr. 4
    [4, 7, 10, 0, 2],    // Dr. 5
    [5, 8, 11, 1, 3],    // Dr. 6
    [6, 9, 12, 2, 4],    // Dr. 7
    [7, 10, 0, 3, 5],    // Dr. 8 (if needed)
];

// Days: April 7th to April 15th, 2025
const DAYS = Array.from({ length: 9 }, (_, i) => 7 + i); // [7,8,9,10,11,12,13,14,15]

// Type for events
export type DummyEvent = {
    resourceId: number;
    start: Date;
    end: Date;
    appointments: { title: string }[];
};

// Build rich dummy events for all doctors, all days
export const DUMMY_DAY_EVENTS: DummyEvent[] = [];
for (let d = 0; d < resources.length; d++) {
    const doctor = resources[d];
    for (let dayIdx = 0; dayIdx < DAYS.length; dayIdx++) {
        const day = DAYS[dayIdx];
        const hours = STAGGERED_HOURS[d % STAGGERED_HOURS.length];
        for (let e = 0; e < hours.length; e++) {
            // Vary number of appointments: 1-3
            const numAppointments = 1 + ((d + e + dayIdx) % 3);
            const appointments = Array.from({ length: numAppointments }, (_, a) => ({
                title: eventTitles[(d * 10 + dayIdx * 5 + e * 3 + a) % eventTitles.length],
            }));
            DUMMY_DAY_EVENTS.push({
                resourceId: doctor.resourceId,
                start: new Date(2025, 3, day, hours[e], 0),
                end: new Date(2025, 3, day, hours[e] + 1, 0),
                appointments,
            });
        }
    }
}

export function generateAppointments({
    view = 'day',
    activeDoctor = 0,
    date = new Date(),
} = {}) {
    if (view === 'week') {
        return getDummyWeekEventsForDoctor(activeDoctor);
    }
    if (view === 'month') {
        // Return all events for the selected month and doctor
        const doctor = resources[activeDoctor];
        return DUMMY_DAY_EVENTS.filter(ev =>
            ev.start.getFullYear() === date.getFullYear() &&
            ev.start.getMonth() === date.getMonth() &&
            ev.resourceId === doctor.resourceId
        );
    }
    // Day view: return all doctors' events for the selected day
    return DUMMY_DAY_EVENTS.filter(ev =>
        ev.start.getFullYear() === date.getFullYear() &&
        ev.start.getMonth() === date.getMonth() &&
        ev.start.getDate() === date.getDate()
    );
}

// Dummy events for a single doctor for a week (Monday-Friday)
export function getDummyWeekEventsForDoctor(doctorIdx: number): DummyEvent[] {
    const doctor = resources[doctorIdx];
    if (!doctor) return [];
    const baseYear = 2025, baseMonth = 3; // April (0-indexed)
    const weekDays = [7, 8, 9, 10, 11]; // Mon-Fri (April 7-11, 2025)
    const hours = STAGGERED_HOURS[doctorIdx % STAGGERED_HOURS.length];
    const events: DummyEvent[] = [];
    for (let dayIdx = 0; dayIdx < weekDays.length; dayIdx++) {
        const day = weekDays[dayIdx];
        for (let e = 0; e < hours.length; e++) {
            // Vary number of appointments: 1-3
            const numAppointments = 1 + ((doctorIdx + e + dayIdx) % 3);
            const appointments = Array.from({ length: numAppointments }, (_, a) => ({
                title: eventTitles[(doctorIdx * 10 + dayIdx * 5 + e * 3 + a) % eventTitles.length],
            }));
            events.push({
                resourceId: doctor.resourceId,
                start: new Date(baseYear, baseMonth, day, hours[e], 0),
                end: new Date(baseYear, baseMonth, day, hours[e] + 1, 0),
                appointments,
            });
        }
    }
    return events;
}