import { type FC } from "react";
import { Checkbox } from "@/components/common/Checkbox";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TableCell } from "@/components/ui/table";
import {
	Calendar,
	Clock,
	User,
	MoreHorizontal,
	MessageSquare,
	CalendarSync,
	Trash2,
	Bell,
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { Appointment, AppointmentStatus } from "./AppointmentsTab";

interface AppointmentTableCardProps {
	appointment: Appointment;
	isSelected: boolean;
	onSelectionChange: (selected: boolean) => void;
}

// Status color mapping
const getStatusConfig = (status: AppointmentStatus) => {
	switch (status) {
		case "scheduled":
			return {
				label: "Scheduled",
				className: "bg-blue-100 text-blue-800 hover:bg-blue-200",
			};
		case "in_progress":
			return {
				label: "In Progress",
				className: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
			};
		case "upcoming":
			return {
				label: "Upcoming",
				className: "bg-green-100 text-green-800 hover:bg-green-200",
			};
		case "completed":
			return {
				label: "Completed",
				className: "bg-gray-100 text-gray-800 hover:bg-gray-200",
			};
		case "no_show":
			return {
				label: "No Show",
				className: "bg-red-100 text-red-800 hover:bg-red-200",
			};
		case "cancelled_patient":
			return {
				label: "Cancelled (Patient)",
				className: "bg-orange-100 text-orange-800 hover:bg-orange-200",
			};
		case "cancelled_admin":
			return {
				label: "Cancelled (Admin)",
				className: "bg-purple-100 text-purple-800 hover:bg-purple-200",
			};
		default:
			return {
				label: "Unknown",
				className: "bg-gray-100 text-gray-800 hover:bg-gray-200",
			};
	}
};

export const AppointmentTableCard: FC<AppointmentTableCardProps> = ({
	appointment,
	isSelected,
	onSelectionChange,
}) => {
	const statusConfig = getStatusConfig(appointment.status);

	return (
		<>
			{/* Patient */}
			<TableCell className="min-w-[200px]">
				<div className="flex items-center gap-2">
					<User className="h-4 w-4 text-gray-400" />
					<div>
						<p className="font-medium text-gray-900">
							{appointment.patientName}
						</p>
					</div>
				</div>
			</TableCell>

			{/* Appointment */}
			<TableCell className="min-w-[200px]">
				<p className="font-medium text-gray-900">
					{appointment.appointmentType}
				</p>
			</TableCell>

			{/* Date & Time */}
			<TableCell className="min-w-[180px]">
				<div className="space-y-1">
					<div className="flex items-center gap-1 text-sm text-gray-600">
						<Calendar className="h-3 w-3" />
						<span>{appointment.date}</span>
					</div>
					<div className="flex items-center gap-1 text-sm text-gray-600">
						<Clock className="h-3 w-3" />
						<span>{appointment.time}</span>
					</div>
				</div>
			</TableCell>

			{/* Doctor */}
			<TableCell className="min-w-[150px]">
				<p className="text-sm text-gray-600">{appointment.doctor}</p>
			</TableCell>

			{/* Status */}
			<TableCell className="min-w-[120px]">
				<Badge
					variant="secondary"
					className={cn("text-xs", statusConfig.className)}
				>
					{statusConfig.label}
				</Badge>
			</TableCell>

			{/* Actions */}
			<TableCell className="w-[140px]">
				<div className="flex items-center justify-end gap-1">
					<Button variant="ghost" size="sm" className="h-8 w-8 p-0">
						<MessageSquare className="h-4 w-4 text-gray-400" />
					</Button>
					<Button variant="ghost" size="sm" className="h-8 w-8 p-0">
						<CalendarSync className="h-4 w-4 text-gray-400" />
					</Button>
					<Button variant="ghost" size="sm" className="h-8 w-8 p-0">
						<Trash2 className="h-4 w-4 text-gray-400" />
					</Button>
					<Button variant="ghost" size="sm" className="h-8 w-8 p-0">
						<Bell className="h-4 w-4 text-blue-600" />
					</Button>
				</div>
			</TableCell>
		</>
	);
};
