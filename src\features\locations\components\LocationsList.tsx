import { useState, type FC, useRef, useEffect } from "react";
import { MapPin } from "lucide-react";
import { useLocations } from "../hooks";
import type { LocationsFilters } from "../types";
import { cn } from "@/lib/utils";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import { LocationTab } from "./LocationTab";
import { ServicesTab } from "./ServicesTab";
import { ScheduleSettingsTab } from "./ScheduleSettingsTab";
import { WalkInSettingsTab } from "./WalkInSettingsTab";
import { TeamMembersTab } from "./TeamMemberTabs";
import OrganizationSettingsTab from "./OrganizationSettingsTab";
import { useOrganizationContext } from "@/features/organizations/context";

export interface LocationsListProps {
	className?: string;
	onRefetchReady?: (refetchFn: (() => void) | null) => void;
}

export const LocationsList: FC<LocationsListProps> = ({
	className,
	onRefetchReady,
}) => {
	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});
	const [activeTab, setActiveTab] = useState("locations");

	// Refs to store refetch functions for different tabs
	const locationsRefetchRef = useRef<(() => void) | null>(null);
	const servicesRefetchRef = useRef<(() => void) | null>(null);
	const teamMembersRefetchRef = useRef<(() => void) | null>(null);

	// Update parent with the appropriate refetch function based on active tab
	useEffect(() => {
		if (onRefetchReady) {
			const refreshableTab =
				activeTab === "locations" ||
				activeTab === "services" ||
				activeTab === "team-members";

			if (!refreshableTab) {
				onRefetchReady(null); // No refresh for non-refreshable tabs
				return;
			}

			const currentRefetch =
				activeTab === "locations"
					? locationsRefetchRef.current
					: activeTab === "services"
						? servicesRefetchRef.current
						: activeTab === "team-members"
							? teamMembersRefetchRef.current
							: null;

			onRefetchReady(currentRefetch);
		}
	}, [activeTab, onRefetchReady]);

	const items = [
		{ value: "locations", label: "Locations" },
		{ value: "services", label: "Services" },
		{ value: "team-members", label: "Team Members" },
		{ value: "schedule-settings", label: "Schedule Settings" },
		// { value: "walk-in-settings", label: "Walk-in Settings" },
		{ value: "organization-settings", label: "Organization Settings" },
	];

	return (
		<div className={cn("flex flex-col gap-0.5 p-2", className)}>
			{/* Tabs */}
			<Tabs
				items={items}
				defaultValue="locations"
				useRouting={true}
				searchParamKey="location-tab"
				onValueChange={(value) => setActiveTab(value)}
			>
				<TabsContent value="locations">
					<LocationTab
						onRefetchReady={(refetchFn) => {
							locationsRefetchRef.current = refetchFn;
							if (activeTab === "locations" && onRefetchReady) {
								onRefetchReady(refetchFn);
							}
						}}
					/>
				</TabsContent>
				<TabsContent value="services">
					<ServicesTab
						onRefetchReady={(refetchFn) => {
							servicesRefetchRef.current = refetchFn;
							if (activeTab === "services" && onRefetchReady) {
								onRefetchReady(refetchFn);
							}
						}}
					/>
				</TabsContent>
				<TabsContent value="team-members">
					<TeamMembersTab
						onRefetchReady={(refetchFn) => {
							teamMembersRefetchRef.current = refetchFn;
							if (
								activeTab === "team-members" &&
								onRefetchReady
							) {
								onRefetchReady(refetchFn);
							}
						}}
					/>
				</TabsContent>
				<TabsContent value="schedule-settings">
					<ScheduleSettingsTab />
				</TabsContent>
				<TabsContent value="walk-in-settings">
					<WalkInSettingsTab />
				</TabsContent>
				<TabsContent value="organization-settings">
					<OrganizationSettingsTab />
				</TabsContent>
			</Tabs>
		</div>
	);
};
