import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { CiSearch } from "react-icons/ci";
import { GoTag } from "react-icons/go";
import { IoIosInformationCircleOutline } from "react-icons/io";
import { LuLayoutDashboard } from "react-icons/lu";
import { IoNotificationsOutline } from "react-icons/io5";
import { FiUser } from "react-icons/fi";
import { LuCalendar } from "react-icons/lu";
import { LuClock } from "react-icons/lu";
import { MdOutlineNotificationsActive } from "react-icons/md";
import { FaRegTrashCan } from "react-icons/fa6";
import { Tooltip } from 'react-tooltip';

const defaultStyles = {
    backgroundColor: "white",
    opacity: 1,
    color: "#27272A",
    boxShadow: "2px 2px 10px 0px rgba(0, 0, 0, 0.1)",
    borderRadius: "8px",
}

export default function Confirmed() {
    return (
        <div className="w-full py-2 px-2 border border-solid border-[#E4E4E7] bg-[#F6F6F6DB] rounded-lg h-[calc(100vh-140px)] flex flex-col">

            {/* search input */}

            <div className="relative">
                <CiSearch className="absolute left-3 top-2 text-base text-[#27272A]" />
                <input
                    type="text"
                    placeholder="Search"
                    className="w-full bg-white py-1.5 px-3 pl-8 rounded-md border border-solid border-[#E4E4E7] text-xs"
                />
            </div>

            {/* actions */}

            <div className="flex items-center justify-between mt-2">
                <div className="flex items-center gap-x-1.5">
                    <Checkbox />
                    <label htmlFor="select-all" className="text-[#898EAA] text-xs">
                        Select All
                    </label>
                </div>
                <div className="space-x-1.5">
                    <Button variant="outline" className="py-1.5 px-1.5 h-7">
                        <IoNotificationsOutline size={14} color="#898EAA" />
                    </Button>
                    <Button variant="outline" className="py-1.5 px-1.5 h-7">
                        <FaRegTrashCan size={14} color="#898EAA" />
                    </Button>
                </div>

            </div>

            {/* appointment list*/}

            <div className="flex flex-col gap-y-3 mt-3 flex-1 overflow-y-auto scrollbar-hide">
                {Array.from({ length: 10 }).map((_, index) => (
                    <div key={index} className="border border-[#E4E4E7] p-2 bg-white rounded-lg space-y-2 transition-all duration-300 hover:border-[#128576] hover:shadow-md">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-x-2">
                                <Checkbox />
                                <div className="flex items-center gap-x-2">
                                    <h3 className="text-[#18181B] text-xs font-regular">John Heatherway</h3>
                                    <div className="flex items-center gap-x-1">
                                        <div className="bg-[#FAFAFA] py-1 px-1 rounded-md">
                                            <IoIosInformationCircleOutline color="#71717A" className="text-base" />
                                        </div>
                                        <div className="bg-[#FAFAFA] py-1 px-1 rounded-md">
                                            <GoTag color="#71717A" className="text-sm" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p className="text-[#52525B] text-xs mt-1 flex items-center gap-x-1">
                            <LuLayoutDashboard
                                data-tooltip-id="base-tooltip"
                                data-tooltip-html="<div style='display: flex; align-items: center; gap: 4px;'>Book Via <p style='font-weight: 600; margin: 0;'>Qr Code</p></div>"
                                data-tooltip-place="top-start"
                                color="#52525B"
                                className="text-xs"
                            />
                            <span className="text-[#71717A]">
                                Dr. Abraham Johnson
                            </span>
                        </p>
                        <h1 className="flex items-center gap-x-1">
                            <FiUser
                                data-tooltip-id="base-tooltip"
                                data-tooltip-content="In Person"
                                color="#042C4D" />
                            <span className="text-[#71717A] text-xs font-medium">
                                First CT Scan Appointment
                            </span>
                        </h1>
                        <div className="flex items-center gap-x-3 text-[#27272A]">
                            <div className="flex items-center gap-x-1">
                                <LuCalendar className="opacity-50 text-base" />
                                <p className="text-xs">12 Apr 2025</p>
                            </div>
                            <div className="flex items-center gap-x-1">
                                <LuClock className="opacity-50 text-base" />
                                <p className="text-xs">
                                    12:30 pm - 4:00 pm
                                </p>
                            </div>
                        </div>
                        <div className="mt-3 pt-1.5 pb-1 flex items-center justify-between border-t border-solid border-[#E4E4E7]">
                            <div className="flex items-center gap-x-2">
                                <div
                                    data-tooltip-id="cancel"
                                    data-tooltip-content="Cancel"
                                    className="bg-[#FAFAFA] py-1 px-1 rounded-md">
                                    <svg width="16" height="16" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 3.5H3V10C3 10.0874 3.04869 10.2167 3.16602 10.334C3.28334 10.4513 3.41262 10.5 3.5 10.5H8.5C8.58738 10.5 8.71666 10.4513 8.83398 10.334C8.95131 10.2167 9 10.0874 9 10V3.5ZM4.5 8.5V5.5C4.5 5.22386 4.72386 5 5 5C5.27614 5 5.5 5.22386 5.5 5.5V8.5C5.5 8.77614 5.27614 9 5 9C4.72386 9 4.5 8.77614 4.5 8.5ZM6.5 8.5V5.5C6.5 5.22386 6.72386 5 7 5C7.27614 5 7.5 5.22386 7.5 5.5V8.5C7.5 8.77614 7.27614 9 7 9C6.72386 9 6.5 8.77614 6.5 8.5ZM7.5 2C7.5 1.91262 7.45131 1.78334 7.33398 1.66602C7.21666 1.54869 7.08738 1.5 7 1.5H5C4.91262 1.5 4.78334 1.54869 4.66602 1.66602C4.54869 1.78334 4.5 1.91262 4.5 2V2.5H7.5V2ZM8.5 2.5H10.5C10.7761 2.5 11 2.72386 11 3C11 3.27614 10.7761 3.5 10.5 3.5H10V10C10 10.4126 9.79869 10.7833 9.54102 11.041C9.28334 11.2987 8.91262 11.5 8.5 11.5H3.5C3.08738 11.5 2.71666 11.2987 2.45898 11.041C2.20131 10.7833 2 10.4126 2 10V3.5H1.5C1.22386 3.5 1 3.27614 1 3C1 2.72386 1.22386 2.5 1.5 2.5H3.5V2C3.5 1.58738 3.70131 1.21666 3.95898 0.958984C4.21666 0.701311 4.58738 0.5 5 0.5H7C7.41262 0.5 7.78334 0.701311 8.04102 0.958984C8.29869 1.21666 8.5 1.58738 8.5 2V2.5Z" fill="#71717A" />
                                    </svg>
                                </div>
                                <div
                                    data-tooltip-id="base-tooltip"
                                    data-tooltip-content="Message"
                                    className="bg-[#FAFAFA] py-1 px-1 rounded-md">
                                    <svg width="16" height="16" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10 2.5C10 2.36739 9.94728 2.24025 9.85352 2.14648C9.75975 2.05272 9.63261 2 9.5 2H2.5C2.36739 2 2.24025 2.05272 2.14648 2.14648C2.05272 2.24025 2 2.36739 2 2.5V9.29297L3.14648 8.14648L3.18311 8.11328C3.27208 8.04036 3.38395 8 3.5 8H9.5C9.63261 8 9.75975 7.94728 9.85352 7.85352C9.94728 7.75975 10 7.63261 10 7.5V2.5ZM8.5 5.5C8.77614 5.5 9 5.72386 9 6C9 6.27614 8.77614 6.5 8.5 6.5H3.5C3.22386 6.5 3 6.27614 3 6C3 5.72386 3.22386 5.5 3.5 5.5H8.5ZM6.5 3.5C6.77614 3.5 7 3.72386 7 4C7 4.27614 6.77614 4.5 6.5 4.5H3.5C3.22386 4.5 3 4.27614 3 4C3 3.72386 3.22386 3.5 3.5 3.5H6.5ZM11 7.5C11 7.89783 10.8419 8.27924 10.5605 8.56055C10.2792 8.84185 9.89783 9 9.5 9H3.70703L1.85352 10.8535C1.71052 10.9965 1.49543 11.0393 1.30859 10.9619C1.12179 10.8845 1 10.7022 1 10.5V2.5C1 2.10218 1.15815 1.72076 1.43945 1.43945C1.72076 1.15815 2.10218 1 2.5 1H9.5C9.89783 1 10.2792 1.15815 10.5605 1.43945C10.8419 1.72076 11 2.10218 11 2.5V7.5Z" fill="#71717A" />
                                    </svg>
                                </div>
                                <div
                                    data-tooltip-id="base-tooltip"
                                    data-tooltip-content="Reschedule"
                                    className="bg-[#FAFAFA] py-1 px-1 rounded-md">
                                    <svg width="16" height="16" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10.5 8C10.5 6.61929 9.38071 5.5 8 5.5C6.61929 5.5 5.5 6.61929 5.5 8C5.5 9.38071 6.61929 10.5 8 10.5C9.38071 10.5 10.5 9.38071 10.5 8ZM7.5 7C7.5 6.72386 7.72386 6.5 8 6.5C8.27614 6.5 8.5 6.72386 8.5 7V7.90967L9.0625 8.35938L9.10059 8.39355C9.28148 8.57105 9.30235 8.86034 9.14062 9.0625C8.97888 9.26469 8.69189 9.308 8.479 9.17041L8.4375 9.14062L7.6875 8.54053C7.56892 8.44564 7.5 8.30177 7.5 8.1499V7ZM10 3.75V3C10 2.86739 9.94728 2.74025 9.85352 2.64648C9.77144 2.56441 9.66382 2.51379 9.54932 2.50244L9.5 2.5H8.5V3C8.5 3.27614 8.27614 3.5 8 3.5C7.72386 3.5 7.5 3.27614 7.5 3V2.5H4.5V3C4.5 3.27614 4.27614 3.5 4 3.5C3.72386 3.5 3.5 3.27614 3.5 3V2.5H2.5C2.36739 2.5 2.24025 2.55272 2.14648 2.64648C2.05272 2.74025 2 2.86739 2 3V4.5H4C4.27614 4.5 4.5 4.72386 4.5 5C4.5 5.27614 4.27614 5.5 4 5.5H2V10C2 10.1326 2.05272 10.2597 2.14648 10.3535C2.24025 10.4473 2.36739 10.5 2.5 10.5H4.25C4.52614 10.5 4.75 10.7239 4.75 11C4.75 11.2761 4.52614 11.5 4.25 11.5H2.5C2.10218 11.5 1.72076 11.3419 1.43945 11.0605C1.15815 10.7792 1 10.3978 1 10V3C1 2.60218 1.15815 2.22076 1.43945 1.93945C1.72076 1.65815 2.10218 1.5 2.5 1.5H3.5V1C3.5 0.723858 3.72386 0.5 4 0.5C4.27614 0.5 4.5 0.723858 4.5 1V1.5H7.5V1C7.5 0.723858 7.72386 0.5 8 0.5C8.27614 0.5 8.5 0.723858 8.5 1V1.5H9.5L9.64844 1.50732C9.99176 1.54145 10.3144 1.69335 10.5605 1.93945C10.8419 2.22076 11 2.60217 11 3V3.75C11 4.02614 10.7761 4.25 10.5 4.25C10.2239 4.25 10 4.02614 10 3.75ZM11.5 8C11.5 9.933 9.933 11.5 8 11.5C6.067 11.5 4.5 9.933 4.5 8C4.5 6.067 6.067 4.5 8 4.5C9.933 4.5 11.5 6.067 11.5 8Z" fill="#71717A" />
                                    </svg>
                                </div>
                            </div>
                            <div
                                data-tooltip-id="base-tooltip"
                                data-tooltip-content="Alert Patient"
                                className="bg-[#FAFAFA] py-1 px-1 rounded-md">
                                <MdOutlineNotificationsActive color="#005893" className="text-base" />
                            </div>
                        </div>
                    </div>
                ))}
            </div>
            <Tooltip id="cancel" style={{
                ...defaultStyles,
                color: "#ff0000",
            }} />
            <Tooltip id="message" style={defaultStyles} />
            <Tooltip id="base-tooltip" style={defaultStyles} />
        </div>
    )
}