import { useMemo } from "react";
import type { ColumnDef } from "@tanstack/react-table";
import type { AutomationHistoryTableRowType } from "../schema/table";
import { Checkbox } from "@/components/common/Checkbox/Checkbox";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/Button/Button";
import { LiaAngleRightSolid } from "react-icons/lia";
import FailedAutomationDetailsSheet from "./failed-automation-details-sheet";

export function getAutomationHistoryColumns() {
    return useMemo(() => {
        const columns: ColumnDef<AutomationHistoryTableRowType>[] = [
            {
                id: "select",
                header: ({ table }) => (
                    <Checkbox
                        checked={table.getIsAllPageRowsSelected() || false}
                        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
                        aria-label="Select all"
                        className="size-5"
                    />
                ),
                cell: ({ row }) => (
                    <Checkbox
                        checked={row.getIsSelected()}
                        onCheckedChange={value => row.toggleSelected(!!value)}
                        aria-label="Select row"
                        className="size-5"
                    />
                ),
                enableSorting: false,
                enableHiding: false,
            },
            {
                id: "patient",
                filterFn: "includesString",
                header: () => (
                    <p className="text-[#71717A] !font-light text-sm">Patient</p>
                ),
                cell: ({ row }) => (
                    <p className="text-sm text-[#27272A]">{row.original.patient}</p>
                ),
            },
            {
                id: "email",
                filterFn: "includesString",
                header: () => (
                    <p className="text-[#71717A] !font-light text-sm">Email</p>
                ),
                cell: ({ row }) => (
                    <p className="text-sm font-light text-[#71717A]">{row.original.email}</p>
                ),
            },
            {
                id: "action",
                filterFn: "includesString",
                header: () => (
                    <p className="text-[#71717A] !font-light text-sm">Action</p>
                ),
                cell: ({ row }) => (
                    <p className="text-sm font-light text-[#71717A]">{row.original.action}</p>
                ),
            },
            {
                id: "runTime",
                filterFn: "includesString",
                header: () => (
                    <p className="text-[#71717A] !font-light text-sm">Run Time</p>
                ),
                cell: ({ row }) => (
                    <p className="text-sm font-light text-[#71717A]">{row.original.runTime}</p>
                ),
            },
            {
                id: "status",
                filterFn: "includesString",
                header: () => (
                    <p className="text-[#71717A] !font-light text-sm">Status</p>
                ),
                cell: ({ row }) => (
                    <p className={cn("text-sm font-normal text-center rounded-md px-2 py-2.5", row.original.status === "Successful" ? "text-[#27272A] bg-[#C3EFCE]" : "text-[#DC2626] bg-[#FEE2E2]")}>
                        {row.original.status}
                    </p>
                ),
            },
            {
                id: "action",
                enableSorting: false,
                enableHiding: false,
                header: () => (
                    <p className="text-[#71717A] !font-light text-sm">Action</p>
                ),
                cell: ({ row }) => (
                    row.original.status.toLowerCase() === "failed" && (
                        <div className="max-w-[3rem]">
                            <FailedAutomationDetailsSheet>
                                <Button variant="outline" size="icon" className="h-9 w-9 rounded-lg cursor-pointer">
                                    <LiaAngleRightSolid />
                                </Button>
                            </FailedAutomationDetailsSheet>
                        </div>
                    )
                ),
            },
        ];

        return columns;
    }, []);
}