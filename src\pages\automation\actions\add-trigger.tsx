import { But<PERSON> } from "@/components/ui/Button/Button";
import { X } from "lucide-react";
import clsx from "clsx";
import type { EditorCanvasCardMetaType, EditorNode } from "../libs/type";
import { type UseFormReturn } from "react-hook-form";
import type { AddTriggerSchemaType } from "../schema/add-trigger";
import { useState, useEffect } from "react";
import type { TriggerTypesData } from "@/features/automation/api/trigger";
import RequestIsLoading from "@/components/RequestIsLoading";

type AddTriggerProps = {
    open: boolean;
    onClose?: () => void;
    triggerId?: string | null;
    onNext: () => void;
    onUpdateMetadata?: (metadata: EditorCanvasCardMetaType) => void;
    triggerForm: UseFormReturn<AddTriggerSchemaType>;
    isLoading: boolean,
    triggers: TriggerTypesData[]
};

export default function AddTrigger({ open, onClose, onNext, onUpdateMetadata, triggerForm, isLoading, triggers }: AddTriggerProps) {
    const selectedTriggerType = triggerForm.watch('triggerType');
    const [fullyOpen, setFullyOpen] = useState(false);

    useEffect(() => {
        if (open) {
            setTimeout(() => {
                setFullyOpen(open);
            }, 10);
        }
    }, [open]);

    const onSubmit = (data: { triggerType: string }) => {
        // Update the node metadata with selected trigger
        const updatedMetadata = {
            type: 'Trigger' as const,
            triggerType: data.triggerType,
        };

        if (onUpdateMetadata) {
            onUpdateMetadata(updatedMetadata);
        }

        // Call onNext to proceed
        onNext();
    };

    return (
        <div className={clsx("z-50 fixed top-5 right-[4%] w-[30rem] bg-white border border-[#00589340] shadow-[0px_2px_4px_-1px_#0000000F,0px_0px_6px_-1px_#0000001A] transition-transform duration-300 ease-in-out will-change-transform rounded-xl py-5", fullyOpen ? "translate-x-0" : "translate-x-[115%]")}>
            <div className="flex items-center justify-between">
                <h1 className="text-[#27272A] text-xl font-semibold ml-6">Add Trigger</h1>
                <Button
                    variant="ghost"
                    className="!px-0 w-11 h-10.5 rounded-lg cursor-pointer mr-3" onClick={onClose}>
                    <X className="text-base" color="#27272A" />
                </Button>
            </div>

            <form onSubmit={triggerForm.handleSubmit(onSubmit)} className="min-h-[85vh] h-full flex flex-col justify-between">
                {isLoading ? (
                    <div role="status" className="min-h-[50vh] flex items-center justify-center">
                        <svg aria-hidden="true" className="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor" />
                            <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill" />
                        </svg>
                        <span className="sr-only">Loading...</span>
                    </div>
                ) : (
                    <div className="flex flex-col gap-y-6 mt-8 mx-5">
                        {triggers.map((trigger) => (
                            <label htmlFor={trigger.trigger_type} key={trigger.trigger_type} className="flex items-center gap-x-2">
                                <input
                                    type="radio"
                                    id={trigger.trigger_type}
                                    value={trigger.trigger_type}
                                    className="size-3.5"
                                    {...triggerForm.register('triggerType', {
                                        required: 'Please select a trigger type'
                                    })}
                                />
                                <p className="text-[#27272A] font-medium text-sm">{trigger.name}</p>
                            </label>
                        ))}
                        {triggerForm.formState.errors.triggerType && (
                            <p className="text-red-500 text-sm">{triggerForm.formState.errors.triggerType.message}</p>
                        )}
                    </div>
                )}
                <div className="flex justify-end px-4">
                    <Button
                        type="submit"
                        className="w-[8rem] cursor-pointer"
                        disabled={!selectedTriggerType}
                    >
                        Add Trigger
                    </Button>
                </div>
            </form>
        </div>
    );
}