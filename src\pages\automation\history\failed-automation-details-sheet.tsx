import { But<PERSON> } from "@/components/ui/Button/Button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet";
import { X } from "lucide-react";
import { CgDanger } from "react-icons/cg";

export default function FailedAutomationDetailsSheet({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <Sheet>
            <SheetTrigger asChild>
                {children}
            </SheetTrigger>
            <SheetContent className="sm:max-w-[550px]">
                <SheetHeader>
                    <SheetTitle>Automation Failure Details </SheetTitle>
                </SheetHeader>
                <div className="px-6">
                    <h1 className="text-[#DC2626] flex items-center gap-1.5 mb-6">
                        <CgDanger className="size-5" />
                        <span className="text-sm">Automation test run failed</span>
                    </h1>
                    <div className="border-y border-[#E4E4E7] pt-2.5 pb-3 mt-6">
                        <div className="flex justify-between">
                            <div>
                                <h1 className="text-[#27272A] font-medium text-sm mb-1.5">Trigger</h1>
                                <p className="text-[#71717A] text-sm font-light">Trigger is Appointment Scheduled</p>
                            </div>
                            <p className="text-[#289144] font-medium text-sm">Success</p>
                        </div>
                    </div>
                    <div className="border-b border-[#E4E4E7] pt-2.5 pb-3">
                        <div className="flex justify-between">
                            <div>
                                <h1 className="text-[#27272A] font-medium text-sm mb-1.5">Condition</h1>
                                <p className="text-[#71717A] text-sm font-light leading-[1.6]">
                                    Category check is Blueberry, Gen Z, Millennium, Assessments, Report Review, Immunizations, Follow Up Consultation, Cancer Screening, Mental Health Assessment, Referral Appointment, Assessments
                                </p>
                            </div>
                            <p className="text-[#289144] font-medium text-sm">Success</p>
                        </div>
                    </div>
                    <div className="border-b border-[#E4E4E7] pt-2.5 pb-3">
                        <div className="flex justify-between">
                            <div>
                                <h1 className="text-[#27272A] font-medium text-sm mb-1.5">
                                    Action (If Match)
                                </h1>
                                <p className="text-[#71717A] text-sm font-light leading-[1.6]">
                                    Then Redirect To Registration Form and Create an account
                                </p>
                            </div>
                            <p className="text-[#DC2626] font-medium text-sm">Failed</p>
                        </div>
                        <h1 className="text-[#DC2626] flex items-center gap-1.5 mt-4">
                            <CgDanger className="size-5" />
                            <span className="text-sm">Automation test run failed</span>
                        </h1>
                    </div>
                    <div className="border-b border-[#E4E4E7] pt-2.5 pb-3">
                        <div className="flex justify-between">
                            <div>
                                <h1 className="text-[#27272A] font-medium text-sm mb-1.5">
                                    Action (If Not a Match)
                                </h1>
                                <p className="text-[#71717A] text-sm font-light leading-[1.6]">
                                    Then sent Form: http://examplelink.com
                                </p>
                            </div>
                            <p>-</p>
                        </div>
                    </div>
                    <div className="border-b border-[#E4E4E7] pt-2.5 pb-3">
                        <div className="flex justify-between">
                            <div>
                                <h1 className="text-[#27272A] font-medium text-sm mb-1.5">
                                    Check
                                </h1>
                                <p className="text-[#71717A] text-sm font-light leading-[1.6]">
                                    Will check for Form submission in every 48 hours
                                </p>
                            </div>
                            <p>-</p>
                        </div>
                    </div>
                </div>
                <SheetFooter>
                    <div className="flex items-center justify-end gap-3 mb-3">
                        <Button variant="outline" className="cursor-pointer py-5">
                            Go to Client’s Profile
                        </Button>
                        <Button className="cursor-pointer py-5">
                            Re-Run Automation
                        </Button>
                    </div>
                </SheetFooter>
            </SheetContent>
        </Sheet>
    );
}