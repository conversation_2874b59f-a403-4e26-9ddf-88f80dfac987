export type GetStationsParams = {
    search?: string;
    location?: string;
    roles?: string;
    sort_by?: string;
    from?: string;
    to?: string;
    basic?: boolean;
    include?: string;
    location_id?: string;
}

export type GetStationsResponse = {
    success: true,
    message: string,
    data:
    {
        id: number,
        name: string,
        image: string | null,
        description: string | null,
        locations: {
            id: number,
            name: string
        }[],
        service_providers: {
            id: number,
            first_name: string,
            last_name: string,
            email: string,
            phone_number: string,
            role: string
        }[]
    }[],
}