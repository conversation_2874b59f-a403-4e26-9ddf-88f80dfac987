import React, { useState, memo, useRef, useEffect } from "react";
import { Info, Maximize2 } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LabelList, Tooltip } from "recharts";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import StarRating from "./StarRating";
import TooltipButton from "./TooltipButton";
import PopUpModal from "./PopUpModal";
import InfoTooltip from "./InfoTooltip";
import { ResponsiveContainer } from "recharts";

const CustomYAxisTick = memo(({ x, y, payload, starSize = 10 }: any) => {
	const getSequentialNumber = (rating: any) => {
		// Extract just the number from the rating (e.g., "5 ★" -> "5")
		// Add null checking to prevent split error
		if (!rating || typeof rating !== 'string') return '';
		return rating.split(" ")[0];
	};

	return (
		<g
			transform={`translate(${x},${y})`}
			className="flex items-center"
		>
			<text
				x={-25}
				y={5}
				fontSize="12"
				fill="#596574"
			>
				{getSequentialNumber(payload.value)}
			</text>
			<svg
				x={-15}
				y={-5}
				width={starSize}
				height={starSize + 1}
				viewBox="0 0 10 11"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
			>
				<g clipPath="url(#clip0_3409_9703)">
					<path
						d="M5.00065 1.33301L6.28815 3.94134L9.16732 4.36217L7.08398 6.39134L7.57565 9.25801L5.00065 7.90384L2.42565 9.25801L2.91732 6.39134L0.833984 4.36217L3.71315 3.94134L5.00065 1.33301Z"
						stroke="#596574"
						strokeLinecap="round"
						strokeLinejoin="round"
					/>
				</g>
			</svg>
		</g>
	);
});

CustomYAxisTick.displayName = "CustomYAxisTick";

const ChartContent = memo(({ chartData, width, height = 250 }: any) => {
	return (
		<ResponsiveContainer width={width} height={height}>
			<BarChart
				data={chartData}
				layout="vertical"
				margin={{
					top: 20,
					right: 50,
					left: 20,
					bottom: 5,
				}}
			>
				<XAxis type="number" hide />
				<YAxis
					dataKey="rating"
					type="category"
					tickLine={false}
					axisLine={false}
					tick={<CustomYAxisTick starSize={12} />}
					// width={150}
				/>
				<Tooltip
					cursor={{ fill: "rgba(233, 185, 73, 0.1)" }}
					contentStyle={{
						backgroundColor: "#fff",
						border: "1px solid #E9B949",
						borderRadius: "6px",
						padding: "8px 12px",
					}}
					labelFormatter={(label) => `${label.split(" ")[0]} stars`}
					formatter={(value) => [`${value.toLocaleString()} reviews`]}
				/>
				<Bar dataKey="count" fill="#E9B949" radius={4} barSize={30}>
					<LabelList
						dataKey="count"
						position="right"
						offset={8}
						className="fill-foreground"
						fontSize={12}
						// formatter={(value) => value === 0 ? null : value.toLocaleString()}
					/>
				</Bar>
			</BarChart>
		</ResponsiveContainer>
	);
});

ChartContent.displayName = "ChartContent";

const RatingsChart = ({
	title = "",
	averageRating = 4.4,
	participation = 61,
	totalReviews = 4200,
	ratingCounts = [
		{ rating: "5 ★", count: 999 },
		{ rating: "4 ★", count: 999 },
		{ rating: "3 ★", count: 999 },
		{ rating: "2 ★", count: 999 },
		{ rating: "1 ★", count: 999 },
	],
}) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const containerRef = useRef<any>(null);
	const [chartWidth, setChartWidth] = useState(800);

	useEffect(() => {
		const updateWidth = () => {
			if (containerRef.current) {
				const newWidth = containerRef.current.offsetWidth;
				setChartWidth(newWidth);
			}
		};

		updateWidth();

		const resizeObserver = new ResizeObserver(updateWidth);
		if (containerRef.current) {
			resizeObserver.observe(containerRef.current);
		}

		return () => {
			if (containerRef.current) {
				resizeObserver.unobserve(containerRef.current);
			}
			resizeObserver.disconnect();
		};
	}, []);

	const chartData = ratingCounts.map((item) => ({
		...item,
		fill: "#E9B949",
	}));

	const tooltipContent =
		"Percentage of clients who left a review. Ex. 10% means 10 out of a total 100 clients gave a rating";

	return (
		<>
			<Card className="flex-1 border-0 bg-[#fafafa]">
				<CardHeader>
					<div className="flex items-center justify-between">
						<CardTitle className="text-base">{title}</CardTitle>
						<button
							onClick={() => setIsModalOpen(true)}
							className="text-gray-500 transition-colors hover:text-gray-700"
						>
							<Maximize2 className="h-4 w-4" />
						</button>
					</div>
					<div className="flex flex-wrap items-center gap-6 text-gray-500">
						<div className="relative flex">
							<span className="mr-1 text-xs">
								Participation:{" "}
							</span>
							<InfoTooltip
								trigger={
									<Info className="h-[16px] w-[24px] rounded bg-[#f5f5f5] p-[2px] text-[#323539]" />
								}
							>
								{tooltipContent}
							</InfoTooltip>
						</div>
						<div className="relative ml-1 flex items-center gap-2">
							<span className="text-xs font-semibold text-black">
								{participation === 0
									? participation
									: participation.toFixed(2)}
								%
							</span>
						</div>
						<div className="flex items-center gap-2">
							<span className="text-xs">Total Reviews: </span>
							<span className="text-xs font-semibold text-black">
								{totalReviews.toLocaleString()}
							</span>
						</div>
						<div className="flex items-center gap-2">
							<span className="text-xs">Average: </span>
							<span className="text-xs font-semibold text-black">
								{averageRating}
							</span>
							<div className="flex">
								<StarRating rating={averageRating} />
							</div>
						</div>
					</div>
				</CardHeader>
				<CardContent className="px-0">
					<div ref={containerRef} className="h-64 pl-4">
						<ChartContent
							chartData={chartData}
							width={chartWidth}
						/>
					</div>
				</CardContent>
			</Card>

			<PopUpModal
				title={title}
				open={isModalOpen}
				onOpenChange={setIsModalOpen}
				modalOpenHandler={(isModalOpen) => {
					setIsModalOpen(isModalOpen);
				}}
				participation={participation}
				totalReviews={totalReviews}
				averageRating={averageRating}
				tooltipContent={tooltipContent}
			>
				<div className="pl-4">
					<ChartContent chartData={chartData} width={500} height={350} />
				</div>
			</PopUpModal>
		</>
	);
};

export default memo(RatingsChart);
