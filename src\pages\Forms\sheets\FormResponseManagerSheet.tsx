import { useState, useRef, useEffect } from "react";
import { Calendar, Clock, X, Flag } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON>eader,
	Sheet<PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { FormResponseApprovalModal, MarkTypeFormModal } from "../modals";
import {
	useDeleteFormResponseMark,
	useGetFormResponsesMarkType,
} from "../store/slices/formResponseSlice";
import * as Types from "../types";
import { Card, CardContent } from "@/components/ui/card";
import useCustomToast from "@/components/CustomToast";
import { useGetForm } from "../store/slices/formSlice";

import {
	useApproveFormResponse,
	useRejectFormResponse,
	useGetFormResponse,
} from "../store/slices/formResponseSlice";
import { format } from "date-fns";
import { FormResponseEditor } from "../components/FormResponseEditor";
import type { FormResponseEditorRef } from "../components/FormResponseEditor";
import { useUpdateFormResponse } from "../store/slices/formResponseSlice";
import { cn } from "@/lib/utils";
import { useModal } from "@/lib/hooks/useModal";

interface FilterData {
	location_ids: string[];
	station_ids: string[];
	service_ids: string[];
	form_types: string[];
	form_statuses: string[];
	flags: string[];
	client_ids: string[];
	date_from: string;
	date_to: string;
}

interface FormResponseManagerSheetProps {
	sheetControl: {
		open: boolean;
		formMode: "view" | "edit" | "manage" | null;
	};
	onSheetControlChange: (sheetControl: {
		open: boolean;
		formMode: "view" | "edit" | "manage" | null;
	}) => void;
	onApplyFilters?: (filters: FilterData) => void;
	isLoading: boolean;
	clearFilters?: () => void;
	formResponse: Types.FormResponseTypes.FormResponseDataType | null;
	refetchForms?: () => void;
}

export const FormResponseManagerSheet = ({
	sheetControl,
	onSheetControlChange,
	formResponse,
	refetchForms,
}: FormResponseManagerSheetProps) => {
	const customToast = useCustomToast();
	const formResponseEditorRef = useRef<FormResponseEditorRef>(null);
	const [openFormResponseApprovalModal, setOpenFormResponseApprovalModal] =
		useState(false);
	const [formResponseNotes, setFormResponseNotes] = useState("");
	const [type, setType] = useState<"approve" | "reject">("approve");
	const [showMarkTypeFormModal, setShowMarkTypeFormModal] = useState(false);
	const { openModal, closeModal, activeModal, updateModalData } = useModal();
	const {
		mutate: approveFormResponse,
		isPending: isApproveFormResponsePending,
	} = useApproveFormResponse();

	const {
		mutate: rejectFormResponse,
		isPending: isRejectFormResponsePending,
	} = useRejectFormResponse();

	const {
		mutate: deleteFormResponseMark,
		isPending: isDeletingFormResponseMark,
	} = useDeleteFormResponseMark();

	const { data: markTypeData, isLoading: isLoadingMarkType } =
		useGetFormResponsesMarkType();

	const {
		data: formResponseData,
		refetch: refetchFormResponse,
		isLoading: isLoadingFormResponse,
	} = useGetFormResponse(formResponse?.id || "");

	const { data: formData, isLoading: isLoadingForm } = useGetForm(
		formResponseData?.data?.form?.id || ""
	);

	const { mutate: updateFormResponse, isPending: isUpdatingFormResponse } =
		useUpdateFormResponse();

	const handleRejectFormResponse = (
		formResponse: Types.FormResponseTypes.FormResponseDataType
	) => {
		rejectFormResponse(
			{
				response_uuid: formResponse?.id || "",
				data: {
					notes: formResponseNotes,
				},
			},
			{
				onSuccess: (data) => {
					setFormResponseNotes("");
					setOpenFormResponseApprovalModal(false);
					customToast(
						data.message ||
							"Form response rejected successfully 🎉",
						{
							id: "reject-form-response",
							type: "success",
						}
					);
					refetchFormResponse();
					refetchForms?.();
					handleOpenChange(true);
				},
				onError: (error: any) => {
					customToast(
						error.response.data.message ||
							"Failed to reject form response 🤕",
						{
							id: "reject-form-response",
							type: "error",
						}
					);
				},
			}
		);
	};

	const handleApproveFormResponse = (
		formResponse: Types.FormResponseTypes.FormResponseDataType
	) => {
		approveFormResponse(
			{
				response_uuid: formResponse?.id || "",
				data: {
					notes: formResponseNotes,
				},
			},
			{
				onSuccess: (data) => {
					setFormResponseNotes("");
					setOpenFormResponseApprovalModal(false);
					customToast(
						data.message ||
							"Form response approved successfully 🎉",
						{
							id: "approve-form-response",
							type: "success",
						}
					);
					refetchFormResponse();
					refetchForms?.();
					handleOpenChange(true);
				},
				onError: (error: any) => {
					customToast(
						error.response.data.message ||
							"Failed to approve form response 🤕",
						{
							id: "approve-form-response",
							type: "error",
						}
					);
				},
			}
		);
	};

	const handleOpenChange = (open: boolean) => {
		onSheetControlChange({
			open,
			formMode: sheetControl.formMode,
		});
	};

	const handleViewEditResponse = () => {
		onSheetControlChange({
			open: true,
			formMode: "edit",
		});
	};

	const handleMarkFormResponse = () => {
		setShowMarkTypeFormModal(true);
		handleOpenChange(false);
	};

	const handleApproval = (type: "approve" | "reject") => {
		setOpenFormResponseApprovalModal(true);
		setType(type);
		handleOpenChange(false);
	};

	const handleNotesChange = (notes: string) => {
		setFormResponseNotes(notes);
	};

	const handleSubmit = () => {
		if (type === "approve") {
			handleApproveFormResponse(
				formResponseData?.data as Types.FormResponseTypes.FormResponseDataType
			);
		} else {
			handleRejectFormResponse(
				formResponseData?.data as Types.FormResponseTypes.FormResponseDataType
			);
		}
	};

	const handleCancel = () => {
		setOpenFormResponseApprovalModal(false);
		setFormResponseNotes("");
		handleOpenChange(true);
	};

	const handleSaveResponse = (
		payload: Types.FormResponseTypes.UpdateFormResponsePayload
	) => {
		updateFormResponse(
			{
				id: formResponseData?.data?.id || "",
				data: payload,
			},
			{
				onSuccess: (data) => {
					customToast(
						data.message || "Form response updated successfully 🎉",
						{
							id: "update-form-response",
							type: "success",
						}
					);
					refetchFormResponse();
					refetchForms?.();
				},
				onError: (error: any) => {
					customToast(
						error.response.data.message ||
							"Failed to update form response 🤕",
						{
							id: "update-form-response",
							type: "error",
						}
					);
				},
			}
		);
	};

	const handleUnMarkFormResponse = (response_uuid: string) => {
		handleOpenChange(false);
		openModal("confirmation", {
			size: "md",
			data: {
				title: "Unmark form response?",
				message:
					"Are you sure you want to unmark this form response? This action cannot be undone.",
				confirmText: "Yes, Unmark",
				cancelText: "No",
				variant: "default",
				cancelClassName: "",
				confirmClassName:
					"text-primary hover:text-primary/90 hover:bg-white",
				onConfirm: () => {
					deleteFormResponseMark(
						{ response_uuid },
						{
							onSuccess: (data) => {
								closeModal();
								refetchForms?.();
								refetchFormResponse();
								handleOpenChange(true);
								customToast(
									data.message ||
										"Form response unmarked successfully 🎉",
									{
										id: "unmark-form-response",
										type: "success",
									}
								);
							},
							onError: () => {
								// Optionally show an error toast
							},
						}
					);
				},
				onClose: () => {
					closeModal();
				},
			},
		});
	};

	// Update modal loading state when isDeletingFormResponseMark changes
	useEffect(() => {
		if (activeModal === "confirmation") {
			updateModalData({ isLoading: isDeletingFormResponseMark });
		}
	}, [isDeletingFormResponseMark, activeModal, updateModalData]);

	const handleEditSave = () => {
		// Call the onSubmit function from FormResponseEditor
		formResponseEditorRef.current?.onSubmit();
	};

	const handleEditReset = () => {
		// Call the handleReset function from FormResponseEditor
		formResponseEditorRef.current?.handleReset();
		onSheetControlChange({
			open: true,
			formMode: "view",
		});
	};

	return (
		<Sheet open={sheetControl.open} onOpenChange={handleOpenChange}>
			<SheetContent className="z-[1003] w-1/2 px-3 py-[22px] sm:max-w-1/2 sm:min-w-[540px] [&>button]:hidden">
				<SheetHeader className="p-0 px-2.5">
					<SheetTitle className="flex items-center justify-between">
						<span className="text-3xl font-semibold">
							{sheetControl.formMode === "view"
								? "View Form Response"
								: sheetControl.formMode === "edit"
									? "Edit Form Response"
									: "Manage Form Response"}
						</span>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => handleOpenChange(false)}
							className="h-6 w-6"
						>
							<X className="h-4 w-4" />
						</Button>
					</SheetTitle>
					<p className="text-muted-foreground text-[15px]">
						Add the customer's details to schedule an appointment.
					</p>
				</SheetHeader>

				<div className="space-y-6 px-0.5">
					<Card className="px-3 py-2">
						<CardContent className="flex flex-col p-0">
							<div className="flex flex-1 items-start justify-between">
								<div className="flex flex-1 items-start gap-3">
									<div className="bg-foreground-disable text-subtle2 flex h-9 w-9 items-center justify-center rounded-full text-sm font-medium">
										{formResponseData?.data?.client?.name
											?.split(" ")
											.map((name) => name.charAt(0))
											.join("")
											.toUpperCase()}
									</div>

									<div className="space-y-2">
										<div className="flex flex-col">
											<h1 className="text-base font-medium">
												{
													formResponseData?.data
														?.client?.name
												}
											</h1>
											<p className="text-muted text-xs">
												{
													formResponseData?.data
														?.client?.email
												}
											</p>
										</div>
									</div>
								</div>
								<div className="flex flex-1 flex-col items-end justify-end gap-1.5">
									<h1 className="text-xs font-medium">
										{formResponseData?.data?.id}
									</h1>
									<div className="text-xxs mb-4 flex items-center gap-3">
										<div className="flex items-center space-x-1">
											<Calendar className="text-muted size-3" />
											<span className="mt-0.5 leading-none">
												{formResponseData?.data
													?.created_at
													? format(
															new Date(
																formResponseData
																	?.data
																	?.created_at ||
																	""
															),
															"dd MMM yyyy"
														)
													: ""}
											</span>
										</div>
										<div className="flex items-center space-x-1">
											<Clock className="text-muted size-3" />
											<span className="mt-0.5 leading-none">
												{`${
													formResponseData?.data
														?.created_at
														? format(
																new Date(
																	formResponseData
																		?.data
																		?.created_at ||
																		""
																),
																"hh:mm a"
															)
														: ""
												} - ${
													formResponseData?.data
														?.submitted_at
														? format(
																new Date(
																	formResponseData
																		?.data
																		?.submitted_at ||
																		""
																),
																"hh:mm a"
															)
														: ""
												}`}
											</span>
										</div>
									</div>
								</div>
							</div>
							{formResponseData?.data?.station?.name && (
								<div className="flex items-center gap-1.5 py-2 pl-12">
									<Badge
										variant="outline"
										className="bg-foreground-muted border-transparent px-2 py-1.5 text-xs font-medium text-[#27272A]"
									>
										{formResponseData?.data?.station?.name}
									</Badge>
								</div>
							)}
						</CardContent>
					</Card>
				</div>

				{formResponseData?.data?.mark_type && (
					<div className="space-y-6 px-0.5">
						<Card className="px-3 py-2.5">
							<CardContent className="flex flex-col gap-2 p-0">
								<div className="flex flex-1 items-center justify-between">
									<Badge
										variant="outline"
										className={cn(
											"flex items-center border-[#E4E4E7] px-2 py-1 text-xs font-medium",
											formResponseData?.data
												?.mark_color &&
												`text-${formResponseData?.data?.mark_color}`
										)}
										style={{
											color:
												formResponseData?.data
													?.mark_type &&
												formResponseData?.data
													?.mark_type ===
													"missing_information"
													? "#27272A"
													: "white",
											backgroundColor:
												formResponseData?.data
													?.mark_color || "",
										}}
									>
										<Flag className="mr-0.5 size-3" />
										<span className="mt-0.5 text-xs leading-none">
											{formResponseData?.data?.mark_label}
										</span>
									</Badge>
									<div className="flex flex-1 items-center justify-end gap-3">
										<div className="text-xxs flex items-center gap-3">
											<div className="flex items-center space-x-1">
												<Calendar className="text-muted size-3" />
												<span className="mt-0.5 leading-none">
													{formResponseData?.data
														?.marked_at
														? format(
																new Date(
																	formResponseData
																		?.data
																		?.marked_at ||
																		""
																),
																"dd MMM yyyy"
															)
														: ""}
												</span>
											</div>
											<div className="flex items-center space-x-1">
												<Clock className="text-muted size-3" />
												<span className="mt-0.5 leading-none">
													{`${
														formResponseData?.data
															?.marked_at
															? format(
																	new Date(
																		formResponseData
																			?.data
																			?.marked_at ||
																			""
																	),
																	"hh:mm a"
																)
															: ""
													}`}
												</span>
											</div>
										</div>
										<div className="flex items-center justify-end space-x-1.5">
											<div className="bg-foreground-disable text-subtle2 text-xxs flex h-6 w-6 items-center justify-center rounded-full font-medium">
												{formResponseData?.data?.marked_by?.name
													?.split(" ")
													.map((name) =>
														name.charAt(0)
													)
													.join("")
													.toUpperCase()}
											</div>
											<h1 className="mt-0.5 text-xs leading-none font-medium">
												{
													formResponseData?.data
														?.marked_by?.name
												}
											</h1>
										</div>
									</div>
								</div>
								<div className="ml-7">
									<p className="text-muted text-sm text-slate-900">
										{formResponseData?.data
											?.marking_notes ||
											"No marking notes"}
									</p>
								</div>
								<div className="flex items-center justify-end">
									<Button
										variant="secondary"
										type="button"
										onClick={() =>
											handleUnMarkFormResponse(
												formResponseData?.data?.id || ""
											)
										}
										className="hover:text-foreground w-[130px] cursor-pointer text-sm font-medium"
									>
										UnMark
									</Button>
								</div>
							</CardContent>
						</Card>
					</div>
				)}

				<div className="space-y-6 overflow-y-auto px-0.5">
					{formData?.data && (
						<FormResponseEditor
							formData={formData.data}
							responseData={
								formResponseData?.data?.responses || []
							}
							onSave={handleSaveResponse}
							mode={sheetControl?.formMode || "view"}
							formResponseData={formResponseData?.data || null}
							ref={formResponseEditorRef}
						/>
					)}
				</div>

				<SheetFooter className="flex-row justify-between px-4 py-0">
					{sheetControl.formMode === "manage" && (
						<>
							<Button
								variant="secondary"
								type="button"
								onClick={handleMarkFormResponse}
								className="hover:text-foreground w-[130px] cursor-pointer text-sm font-medium"
							>
								<Flag className="mr-2 size-3" />
								Mark Form
							</Button>
							<div className="flex gap-3">
								<Button
									className="w-[130px] cursor-pointer text-sm font-medium text-[#DC2626] hover:border-[#DC2626] hover:bg-[#DC2626]/10 hover:text-[#DC2626]"
									variant="outline"
									type="button"
									onClick={() => handleApproval("reject")}
								>
									Decline
								</Button>
								<Button
									type="button"
									className="w-[130px] cursor-pointer bg-[#289144] text-sm font-medium text-white hover:bg-[#289144]/90"
									onClick={() => handleApproval("approve")}
								>
									Approve
								</Button>
							</div>
						</>
					)}

					{/* View Form Response */}
					{sheetControl.formMode === "view" && (
						<>
							<Button
								variant="secondary"
								type="button"
								onClick={handleMarkFormResponse}
								className="hover:text-foreground w-[130px] cursor-pointer text-sm font-medium"
							>
								<Flag className="mr-2 size-3" />
								Mark Form
							</Button>
							<div className="flex gap-3">
								<Button
									type="button"
									className="w-[130px] cursor-pointer text-sm font-medium"
									onClick={handleViewEditResponse}
								>
									Edit Response
								</Button>
							</div>
						</>
					)}

					{/* Edit Form Response */}
					{sheetControl.formMode === "edit" && (
						<>
							<Button
								type="button"
								variant="outline"
								className="w-[130px] cursor-pointer text-sm font-medium"
								onClick={handleEditReset}
							>
								Cancel
							</Button>
							<Button
								type="button"
								className="w-[130px] cursor-pointer text-sm font-medium"
								onClick={handleEditSave}
							>
								Save Response
							</Button>
						</>
					)}
				</SheetFooter>
			</SheetContent>

			{/* Form Response Approval Modal */}
			<FormResponseApprovalModal
				open={openFormResponseApprovalModal}
				onOpenChange={setOpenFormResponseApprovalModal}
				response={formResponseData?.data}
				type={type}
				handleSubmit={handleSubmit}
				handleCancel={handleCancel}
				formResponseNotes={formResponseNotes}
				handleNotesChange={handleNotesChange}
				isLoading={
					isApproveFormResponsePending || isRejectFormResponsePending
				}
			/>

			{/* Mark Type Form Modal */}
			<MarkTypeFormModal
				open={showMarkTypeFormModal}
				onOpenChange={setShowMarkTypeFormModal}
				response={formResponseData?.data || null}
				onSuccess={() => {
					setShowMarkTypeFormModal(false);
					refetchForms?.();
					refetchFormResponse();
					handleOpenChange(true);
				}}
				onClose={() => {
					handleOpenChange(true);
				}}
			/>
		</Sheet>
	);
};
