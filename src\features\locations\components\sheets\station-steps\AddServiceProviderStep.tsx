import { useState, useEffect } from "react";
import { ChevronLeft } from "lucide-react";
import { InputText } from "@/components/common/InputText";
import type { CreateProviderStationRequest } from "../../../types";
import type { UploadedFile } from "@/components/common/Uploader/types";

interface AddServiceProviderStepProps {
	onBack?: () => void;
	onSendInvite?: (data: ServiceProviderData) => void;
	providerFormData?: CreateProviderStationRequest;
	onProviderDataChange?: (
		field: keyof CreateProviderStationRequest,
		value: string
	) => void;
	selectedFile?: File | null;
	uploadedFiles?: UploadedFile[];
	onImageChange?: (files: File[]) => void;
	onFileRemove?: (fileId: string) => void;
	onFileEdit?: (fileId: string, newName: string) => void;
	isSubmitting?: boolean;
}

interface ServiceProviderData {
	firstName: string;
	lastName: string;
	email: string;
	phone: string;
}

export function AddServiceProviderStep({
	onBack,
	onSendInvite,
	providerFormData,
	onProviderDataChange,
	selectedFile,
	uploadedFiles,
	onImageChange,
	onFileRemove,
	onFileEdit,
	isSubmitting,
}: AddServiceProviderStepProps) {
	const [formData, setFormData] = useState<ServiceProviderData>({
		firstName: "",
		lastName: "",
		email: "",
		phone: "",
	});

	// Sync local form data with provider form data when it changes
	useEffect(() => {
		if (providerFormData) {
			setFormData({
				firstName: providerFormData.service_provider_first_name || "",
				lastName: providerFormData.service_provider_last_name || "",
				email: providerFormData.service_provider_email || "",
				phone: providerFormData.service_provider_phone || "",
			});
		}
	}, [providerFormData]);

	// Debug: Log when provider form data changes
	useEffect(() => {
		console.log(
			"AddServiceProviderStep - providerFormData updated:",
			providerFormData
		);
		console.log("AddServiceProviderStep - local formData:", formData);
	}, [providerFormData, formData]);

	const handleInputChange = (
		field: keyof ServiceProviderData,
		value: string
	) => {
		setFormData((prev) => ({ ...prev, [field]: value }));

		// Also update the parent provider form data
		if (onProviderDataChange) {
			switch (field) {
				case "firstName":
					onProviderDataChange("service_provider_first_name", value);
					break;
				case "lastName":
					onProviderDataChange("service_provider_last_name", value);
					break;
				case "email":
					onProviderDataChange("service_provider_email", value);
					break;
				case "phone":
					onProviderDataChange("service_provider_phone", value);
					break;
			}
		}
	};

	// Get current form data for submission
	const getCurrentFormData = (): ServiceProviderData => {
		return {
			firstName: formData.firstName,
			lastName: formData.lastName,
			email: formData.email,
			phone: formData.phone,
		};
	};

	// Handle form submission
	const handleSendInvite = () => {
		const currentData = getCurrentFormData();
		console.log("Sending invite with data:", currentData);
		onSendInvite?.(currentData);
	};

	// Expose the form submission function to parent component
	useEffect(() => {
		// This ensures the parent component always has access to the latest form data
		if (onSendInvite) {
			// We don't need to do anything here as the form data is already up to date
			// The parent will call onSendInvite when the user clicks "Send Invite"
		}
	}, [formData, onSendInvite]);

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center gap-3">
				<button
					onClick={onBack}
					className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-md hover:bg-gray-100"
				>
					<ChevronLeft className="h-5 w-5 text-gray-600" />
				</button>
				<div>
					<h3 className="text-lg font-bold text-gray-900">
						Add New Service Provider User
					</h3>
					<p className="text-sm text-gray-500">
						Provide details to add a new Service Provider User
					</p>
				</div>
			</div>

			{/* Form Fields */}
			<div className="space-y-4">
				{/* First Name and Last Name */}
				<div className="grid grid-cols-2 gap-4">
					<div className="space-y-1.5">
						<label className="text-sm font-medium text-gray-900">
							First name <span className="text-red-500">*</span>
						</label>
						<InputText
							placeholder="Enter first name"
							value={formData.firstName}
							onChange={(e) =>
								handleInputChange("firstName", e.target.value)
							}
							className="w-full border-gray-200"
							variant="default"
						/>
					</div>
					<div className="space-y-1.5">
						<label className="text-sm font-medium text-gray-900">
							Last name <span className="text-red-500">*</span>
						</label>
						<InputText
							placeholder="Enter last name"
							value={formData.lastName}
							onChange={(e) =>
								handleInputChange("lastName", e.target.value)
							}
							className="w-full border-gray-200"
							variant="default"
						/>
					</div>
				</div>

				{/* Email */}
				<div className="space-y-1.5">
					<label className="text-sm font-medium text-gray-900">
						Email
					</label>
					<InputText
						placeholder="Enter email address"
						type="email"
						value={formData.email}
						onChange={(e) =>
							handleInputChange("email", e.target.value)
						}
						className="w-full border-gray-200"
						variant="default"
					/>
				</div>

				{/* Phone */}
				<div className="space-y-1.5">
					<label className="text-sm font-medium text-gray-900">
						Phone
					</label>
					<InputText
						placeholder="Enter phone number"
						type="tel"
						value={formData.phone}
						onChange={(e) =>
							handleInputChange("phone", e.target.value)
						}
						className="w-full border-gray-200"
						variant="default"
					/>
				</div>
			</div>
		</div>
	);
}
