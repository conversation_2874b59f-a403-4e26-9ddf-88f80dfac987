import React from "react";
import type { FormField } from "../../types/Form";

type TextFieldRendererProps = {
	field: FormField;
	value: any;
	mode: "preview" | "view" | "edit";
	onChange?: (value: any) => void;
};

export const TextFieldRenderer: React.FC<TextFieldRendererProps> = ({
	field,
	value,
	mode,
	onChange,
}) => {
	if (mode === "view") {
		return (
			<div>
				{value ?? <span className="text-slate-400">No response</span>}
			</div>
		);
	}
	return (
		<input
			type="text"
			className="w-full rounded border px-2 py-1"
			value={value ?? ""}
			onChange={
				mode === "edit" && onChange
					? (e) => onChange(e.target.value)
					: undefined
			}
			disabled={mode !== "edit"}
			placeholder={field.description || field.title}
		/>
	);
};
