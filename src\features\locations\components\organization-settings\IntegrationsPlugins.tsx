import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import accuroEmrIcon from "./images/acurro.png";
import oscarProIcon from "./images/oscar.png";
import IntegrateOscarPro from "./integrate/oscar-pro";
import IntegrateAccuroEHR from "./integrate/accuro-emr";
import ConnectedEHR from "./integrate/connected";
import { ChevronLeft } from "lucide-react";

const integrations = [
	{
		id: "oscar-pro",
		name: "OSCAR Pro",
		group: "EMR Solutions",
		icon: <img src={oscarProIcon} alt="OSCAR Pro" className="size-20" />,
		steps: [
			{
				title: "Step 1",
				content: (
					<div className="rounded-lg bg-white p-6 shadow">
						<div className="mb-2 font-semibold">
							Sign in to your EMR
						</div>
						<div className="mb-4 text-sm text-gray-600">
							Enter your EMR URL, admin credentials, and sign in
							to get your access!
						</div>
						<div className="mb-4 flex flex-col gap-3">
							<Input placeholder="EMR URL" />
							<Input placeholder="Email" type="email" />
							<Input placeholder="Password" type="password" />
						</div>
						<Button>Continue</Button>
					</div>
				),
			},
			{
				title: "Step 2",
				content: (
					<div className="rounded-lg bg-white p-6 shadow">
						<div className="mb-2 font-semibold">
							Navigate to REST Clients
						</div>
						<ol className="mb-4 list-inside list-decimal text-sm text-gray-700">
							<li>
								Go to the{" "}
								<span className="font-medium">
									Administrative Panel
								</span>{" "}
								(top right of the navigation panel)
							</li>
							<li>
								Click on{" "}
								<span className="font-medium">
									Integrations
								</span>{" "}
								(left hand panel)
							</li>
							<li>
								Then, click on{" "}
								<span className="font-medium">
									REST Clients
								</span>{" "}
								(right below Integrations)
							</li>
						</ol>
						<img
							src="https://via.placeholder.com/400x120?text=REST+Clients+Screenshot"
							alt="REST Clients Screenshot"
							className="my-2 rounded border"
						/>
					</div>
				),
			},
			{
				title: "Step 3",
				content: (
					<div className="rounded-lg bg-white p-6 shadow">
						<div className="mb-2 font-semibold">
							Once at REST Clients, click “Add New” client to your
							EMR
						</div>
						<div className="mb-2 text-sm text-gray-700">
							Click on “Add New” under Manage Clients
						</div>
						<img
							src="https://via.placeholder.com/400x120?text=Add+New+Client+Screenshot"
							alt="Add New Client Screenshot"
							className="my-2 rounded border"
						/>
						<div className="mt-4 mb-2 font-semibold">
							Fill in this information to complete adding a new
							client
						</div>
						<div className="mb-4 flex flex-col gap-3">
							<Input placeholder="Client Name" />
							<Input placeholder="Client Secret" />
							<Input placeholder="EMR Base URL" />
						</div>
						<Button>Create Client</Button>
					</div>
				),
			},
			{
				title: "Step 4",
				content: (
					<div className="rounded-lg bg-white p-6 shadow">
						<div className="mb-2 font-semibold">
							Add details for EMR to connect the system
						</div>
						<div className="mb-2 text-sm text-gray-700">
							Add Client Name, Client Secret, and EMR Base URL to
							connect your system.
						</div>
						<img
							src="https://via.placeholder.com/400x120?text=Connection+Details+Screenshot"
							alt="Connection Details Screenshot"
							className="my-2 rounded border"
						/>
						<div className="mt-4 mb-4 flex flex-col gap-3">
							<Input placeholder="Client ID" />
							<Input placeholder="Client Secret" />
							<Input placeholder="EMR Base URL" />
						</div>
						<Button>Connect</Button>
					</div>
				),
			},
		],
	},
	{
		id: "accuro-emr",
		name: "Accuro EMR",
		group: "EMR Solutions",
		icon: <img src={accuroEmrIcon} alt="Accuro EMR" className="size-20" />,
		steps: [
			{
				title: "Step 1",
				content: <div>Accuro EMR Step 1 instructions...</div>,
			},
		],
	},
	{
		id: "oscar-pro-2",
		name: "OSCAR Pro",
		group: "Other Tools",
		icon: <img src={oscarProIcon} alt="OSCAR Pro" className="size-20" />,
		steps: [
			{
				title: "Step 1",
				content: <div>Other Tool Step 1 instructions...</div>,
			},
		],
	},
	{
		id: "oscar-pro-3",
		name: "OSCAR Pro",
		group: "Other Tools",
		icon: <img src={oscarProIcon} alt="OSCAR Pro" className="size-20" />,
		steps: [
			{
				title: "Step 1",
				content: <div>Other Tool Step 1 instructions...</div>,
			},
		],
	},
];

const groups = ["EMR Solutions", "Other Tools"];

const IntegrationsPlugins: React.FC = () => {
	const [search, setSearch] = useState("");
	const [selected, setSelected] = useState<string | null>(null);
	const [step, setStep] = useState(0);
	const [connected, setConnected] = useState(false);
	// const [started, setStarted] = useState(false);

	const filtered = integrations.filter((i) =>
		i.name.toLowerCase().includes(search.toLowerCase())
	);

	const selectedIntegration = integrations.find((i) => i.id === selected);

	// Show not connected panel before starting integration
	if (selectedIntegration && !connected) {
		return (
			<div className="max-w-4xl">
				<div className="mb-6 flex items-center gap-2 border-b pb-3">
					<Button
						variant="ghost"
						size="icon"
						onClick={() => {
							setSelected(null);
							setStep(0);
							setConnected(false);
							// setStarted(false);
						}}
					>
						<ChevronLeft className="h-5 w-5" />
					</Button>
					<span className="text-lg font-semibold">
						Integrate {selectedIntegration.name}
					</span>
				</div>
				{selectedIntegration.id === "oscar-pro" && <IntegrateOscarPro />}
				{selectedIntegration.id === "accuro-emr" && <IntegrateAccuroEHR />}
			</div>
		);
	} else if (selectedIntegration && connected) {
		return (
			<div>
				<div className="mb-6 flex items-center gap-2 border-b pb-3">
					<Button
						variant="ghost"
						size="icon"
						onClick={() => {
							setSelected(null);
							setStep(0);
							setConnected(false);
							// setStarted(false);
						}}
					>
						<ChevronLeft className="h-5 w-5" />
					</Button>
					<span className="text-lg font-semibold">
						Integrate {selectedIntegration.name}
					</span>
				</div>
				<ConnectedEHR selectedIntegration={selectedIntegration.id as "accuro-emr" | "oscar-pro"} />
			</div>
		);
	}

	return (
		<div className="max-w-3xl mt-4">
			<h2 className="mb-5 text-xl font-semibold border-b pb-4">
				Integrations & Plugins
			</h2>
			<Input
				placeholder="Search"
				value={search}
				onChange={(e) => setSearch(e.target.value)}
				className="mb-6 w-full"
			/>
			{groups.map((group) => {
				const groupItems = filtered.filter((i) => i.group === group);
				if (groupItems.length === 0) return null;
				return (
					<div key={group} className="mb-4">
						<h3 className="mb-2 text-xl font-semibold text-[#27272A]">
							{group}
						</h3>
						<div className="flex flex-wrap gap-8">
							{groupItems.map((integration) => (
								<button
									key={integration.id}
									className="flex min-w-[120px] cursor-pointer flex-col items-center gap-2 px-6 py-4"
									onClick={() => {
										setSelected(integration.id);
										setStep(0);
										setConnected(false);
									}}
									type="button"
								>
									<div className="bg-[#fafafa] rounded-lg">
										{integration.icon}
									</div>
									<span className="mt-1 text-sm font-normal text-gray-800">
										{integration.name}
									</span>
								</button>
							))}
						</div>
					</div>
				);
			})}
		</div>
	);
};

export default IntegrationsPlugins;
