import type { FormDatePickerFieldProps } from "../../libs/form";
import type { FieldValues } from "react-hook-form";

import clsx from "clsx";
import { format } from "date-fns";
import { useState } from "react";

import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";

export default function FormDatePickerField<T extends FieldValues>({
    form,
    name,
    label,
    placeholder = "Pick a date",
    formItemStyles,
    labelStyles,
    required,
    disabled,
}: FormDatePickerFieldProps<T>) {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <FormField
            control={form.control}
            name={name}
            render={({ field }) => (
                <FormItem className={clsx(formItemStyles)}>
                    {label && (
                        <FormLabel className={clsx(labelStyles)}>
                            {label}
                            {required && <span className="ml-1 text-red-600">*</span>}
                        </FormLabel>
                    )}
                    <FormControl>
                        <DatePicker
                            variant="default"
                            value={field.value ? new Date(field.value) : undefined}
                            disabled={disabled}
                            onChange={(date) => {
                                if (date) {
                                    field.onChange(format(date as Date, "yyyy-MM-dd"));
                                    setIsOpen(false);
                                }
                            }}
                            placeholder={placeholder}
                        />
                    </FormControl>
                    <FormMessage />
                </FormItem>
            )}
        />
    );
}
