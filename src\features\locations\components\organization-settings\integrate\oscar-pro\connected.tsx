import { ChevronDown, X } from "lucide-react"
import migranium from "../../images/migranium.png"
import oscarPro from "../../images/oscar.png"
import vector from "../../images/vector.png"
import { Button } from "@/components/ui/Button/Button"
import { useRef, useState } from "react"
import { Input } from "@/components/ui/input"

function SlideDown({ show, children }: { show: boolean, children: React.ReactNode }) {
    const ref = useRef<HTMLDivElement>(null);

    return (
        <div
            ref={ref}
            style={{
                maxHeight: show ? ref.current?.scrollHeight : 0,
                overflow: "hidden",
                transition: "max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
            }}
            aria-hidden={!show}
        >
            <div className={show ? "opacity-100 transition-opacity duration-300" : "opacity-0 transition-opacity duration-200"}>
                {children}
            </div>
        </div>
    );
}

export default function IntegrateOscarProConnected({
    setDisconnected
}: { setDisconnected: () => void }) {
    const [show, setShow] = useState(false);
    const [open, setOpen] = useState(false);
    return (
        <div>
            <div className="flex items-center justify-center flex-col text-center">
                <div className="flex items-center gap-x-7 mt-7">
                    <div className="border border-[#D4D4D8] p-2 rounded-lg">
                        <img
                            src={migranium}
                            alt="Migranium"
                            className="size-7"
                        />
                    </div>
                    <img
                        src={vector}
                        alt="Vector"
                        className="size-7"
                    />
                    <div className="rounded-lg bg-white shadow-[0px_4px_20px_0px_#9BAAB42E] p-2 scale-90">
                        <img
                            src={oscarPro}
                            alt="Oscar Pro"
                            className="size-9"
                        />
                    </div>
                </div>
                <h1 className="text-[#27272A] font-medium text-lg mt-3">
                    You are <span className="text-[#34A853] font-medium text-xl">connected!</span>
                    <p className="text-[#71717A] font-light text-sm mt-1">Connected since: DD MM YYYY</p>
                </h1>
            </div>

            <div className="border border-[#E4E4E7] mt-6 shadow-[0px_0px_16px_0px_#9BAAB412] py-5 px-6 rounded-md">
                <div
                    className="flex items-center justify-between"
                    onClick={() => setShow((prev) => !prev)}
                    role="button"
                    tabIndex={0}
                    aria-expanded={open}
                >
                    <div className="flex-1">
                        <h1 className="font-medium text-base">EMR Integration Information </h1>
                        <p className="text-[#71717A] font-light text-sm mt-1">View your connection details</p>
                    </div>
                    <ChevronDown
                        size={20}
                        color="#27272A"
                        className={`transition-transform duration-300 ${open ? "rotate-180" : ""}`}
                    />
                </div>
                <SlideDown show={show}>
                    <div className="flex flex-col gap-x-4 gap-y-3 mt-6">
                        <div className="flex flex-col gap-1">
                            <label htmlFor="client-id" className="text-[#27272A] font-normal text-sm">Client ID / Key</label>
                            <Input
                                type="text"
                                className="bg-white py-5"
                            />
                        </div>
                        <div className="flex flex-col gap-1">
                            <label htmlFor="client-secret" className="text-[#27272A] font-normal text-sm">
                                Client Secret
                            </label>
                            <Input
                                type="text"
                                className="bg-white py-5"
                            />
                        </div>
                        <div className="flex flex-col gap-1">
                            <label htmlFor="emr-base-url" className="text-[#27272A] font-normal text-sm">
                                EMR Base URL
                            </label>
                            <Input
                                type="url"
                                className="bg-white py-5"
                            />
                        </div>
                    </div>
                    <div className="flex items-center justify-end mt-5 gap-x-3">
                        <Button className="w-[10rem]" variant="outline">
                            Cancel
                        </Button>
                        <Button className="w-[10rem]">
                            Connect
                        </Button>
                    </div>
                </SlideDown>
            </div>

            <div className="bg-[#F4F4F5] py-5 px-6 rounded-xl mt-6 flex items-start justify-between">
                <div className="flex-1">
                    <h1 className="text-[#DC2626] font-normal text-base">Disconnect</h1>
                    <p className="text-[#71717A] font-light mt-2 text-sm">Disconnecting this EMR will limit certain functions. You may disconnect here or contact
                        {" "}
                        <span className="text-[#005893]">support.</span>
                    </p>
                </div>
                <div>
                    <Button className="w-[10rem]" variant="outline" onClick={setDisconnected}>
                        Disconnect
                    </Button>
                </div>

            </div>
        </div>
    )
}