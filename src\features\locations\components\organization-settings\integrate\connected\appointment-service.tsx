import { Button } from "@/components/ui/Button/Button";
import { Input } from "@/components/ui/input";
import ReusableSelect from "../component/reusable-select";

const migraniumTitles = ["Name", "Phone number", "Email", "Address", "City", "State", "Zip", "Country", "Other"]

export default function AppointmentService() {
    return (
        <div>
            <h1 className="text-sm mb-4">Appointment Service / Reason</h1>
            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label htmlFor="name" className="text-[#27272A] font-medium text-sm">EMR Titles</label>
                    <Input
                        type="text"
                        placeholder="Full Name"
                        className="bg-white py-5 mt-4"
                    />
                    <Input
                        type="text"
                        placeholder="Phone number"
                        className="bg-white py-5 mt-4"
                    />
                    <Button variant="secondary" className="mt-5 font-normal w-[7rem] cursor-pointer">
                        Add
                    </Button>
                </div>
                <div>
                    <label htmlFor="name" className="text-[#27272A] font-medium text-sm">
                        Migranium Titles
                    </label>
                    <ReusableSelect
                        placeholder="Select a title"
                        options={migraniumTitles}
                    />
                    <ReusableSelect
                        placeholder="Select a title"
                        options={migraniumTitles}
                    />
                </div>
            </div>

            <div className="bg-[#F4F4F5] py-5 px-6 rounded-xl mt-6 flex items-start justify-between">
                <div className="flex-1">
                    <h1 className="text-[#005893] font-normal text-base">Import Later</h1>
                    <p className="text-[#71717A] font-light mt-2 text-sm">
                        You can go to the Services section and bulk import ‘Services’ and/or ‘Reason’ for appointment.
                    </p>
                </div>
                <div className="flex items-center gap-x-3">
                    <Button className="w-[8rem]" variant="ghost">
                        Skip for now
                    </Button>
                    <Button className="w-[8rem]" variant="outline">
                        Import Now
                    </Button>
                </div>
            </div>

            <div className="flex items-center justify-end mt-5">
                <Button>
                    Save & Next
                </Button>
            </div>

        </div>
    )
}