import { Button } from "@/components/ui/Button/Button";
import { cn } from "@/lib/utils";
import { X } from "lucide-react";
import { useEffect, useState } from "react";
import { CgDanger } from "react-icons/cg";

type TestingProps = {
    open: boolean;
    onClose: () => void;
}

export default function Testing({ open, onClose }: TestingProps) {
    const [fullyOpen, setFullyOpen] = useState(false);

    useEffect(() => {
        if (open) {
            setTimeout(() => {
                setFullyOpen(open);
            }, 10); 
        }
    }, [open]);

    return (
        <div className={cn("z-50 fixed top-5 right-[4%] w-[30rem] bg-white border border-[#00589340] shadow-[0px_2px_4px_-1px_#0000000F,0px_0px_6px_-1px_#0000001A] transition-transform duration-300 ease-in-out will-change-transform rounded-xl py-5", fullyOpen ? "translate-x-0" : "translate-x-[115%]")}>
            <div className="flex items-center justify-between">
                <h1 className="text-[#27272A] text-xl font-semibold ml-6">Add Trigger</h1>
                <Button
                    variant="ghost"
                    className="!px-0 w-11 h-10.5 rounded-lg cursor-pointer mr-3" onClick={onClose}>
                    <X className="text-base" color="#27272A" />
                </Button>
            </div>

            <div className="min-h-[85vh] max-h-[85vh] h-full overflow-y-auto flex flex-col justify-between">

                <div className="px-6 mt-6">
                    <h1 className="text-[#289144] flex items-center gap-1.5 mb-6">
                        <CgDanger className="size-5" />
                        <span className="text-sm">Automation test run successfully</span>
                    </h1>
                    <div className="border-y border-[#E4E4E7] pt-2.5 pb-3 mt-6">
                        <div className="flex justify-between">
                            <div>
                                <h1 className="text-[#27272A] font-medium text-sm mb-1.5">Trigger</h1>
                                <p className="text-[#71717A] text-sm font-light">Trigger is Appointment Scheduled</p>
                            </div>
                            <p className="text-[#289144] font-medium text-sm">Success</p>
                        </div>
                    </div>
                    <div className="border-b border-[#E4E4E7] pt-2.5 pb-3">
                        <div className="flex justify-between">
                            <div>
                                <h1 className="text-[#27272A] font-medium text-sm mb-1.5">Condition</h1>
                                <p className="text-[#71717A] text-sm font-light leading-[1.6]">
                                    Category check is Blueberry, Gen Z, Millennium, Assessments, Report Review, Immunizations, Follow Up Consultation, Cancer Screening, Mental Health Assessment, Referral Appointment, Assessments
                                </p>
                            </div>
                            <p className="text-[#289144] font-medium text-sm">Success</p>
                        </div>
                    </div>
                    <div className="border-b border-[#E4E4E7] pt-2.5 pb-3">
                        <div className="flex justify-between">
                            <div>
                                <h1 className="text-[#27272A] font-medium text-sm mb-1.5">
                                    Action (If Match)
                                </h1>
                                <p className="text-[#71717A] text-sm font-light leading-[1.6]">
                                    Then Redirect To Registration Form and Create an account
                                </p>
                            </div>
                            <p className="text-[#DC2626] font-medium text-sm">Failed</p>
                        </div>
                        <h1 className="text-[#DC2626] flex items-center gap-1.5 mt-4">
                            <CgDanger className="size-5" />
                            <span className="text-sm">Automation test run failed</span>
                        </h1>
                    </div>
                    <div className="border-b border-[#E4E4E7] pt-2.5 pb-3">
                        <div className="flex justify-between">
                            <div>
                                <h1 className="text-[#27272A] font-medium text-sm mb-1.5">
                                    Action (If Not a Match)
                                </h1>
                                <p className="text-[#71717A] text-sm font-light leading-[1.6]">
                                    Then sent Form: http://examplelink.com
                                </p>
                            </div>
                            <p>-</p>
                        </div>
                    </div>
                    <div className="border-b border-[#E4E4E7] pt-2.5 pb-3">
                        <div className="flex justify-between">
                            <div>
                                <h1 className="text-[#27272A] font-medium text-sm mb-1.5">
                                    Check
                                </h1>
                                <p className="text-[#71717A] text-sm font-light leading-[1.6]">
                                    Will check for Form submission in every 48 hours
                                </p>
                            </div>
                            <p>-</p>
                        </div>
                    </div>
                </div>

                <div className="flex justify-end px-4 gap-x-3">
                    <Button
                        onClick={onClose}
                        variant="secondary"
                        className="cursor-pointer">
                        Close
                    </Button>

                    <Button
                        onClick={onClose}
                        className="cursor-pointer">
                        Re-Run Automation
                    </Button>
                </div>

            </div>
        </div>
    )
}