import { apiClient } from "@/lib/api/clients";
import type {
	Location,
	LocationsResponse,
	LocationsFilters,
	CreateLocationRequest,
	UpdateLocationRequest,
} from "../types";

// Import operating hours, schedule settings, and schedule optimizer types from organizations API
import type {
	OrganizationOperatingHour,
	UpdateOperatingHoursRequest,
	LocationOperatingHoursResponse,
	ScheduleSettings,
	UpdateScheduleSettingsRequest,
	LocationScheduleSettingsResponse,
	ScheduleOptimizer,
	UpdateScheduleOptimizerRequest,
	LocationScheduleOptimizerResponse
} from "@/features/organizations/api/organizationsApi";

const LOCATIONS_ENDPOINTS = {
	base: "/api/v1/locations",
	byId: (id: string) => `/api/v1/locations/${id}`,
	operatingHours: (locationId: string) => `/schedule/api/v1/locations/${locationId}/operating-hours`,
	scheduleSettings: (locationId: string) => `/schedule/api/v1/locations/${locationId}/schedule-settings`,
	scheduleOptimizer: (locationId: string) => `/schedule/api/v1/locations/${locationId}/schedule-optimizer`,
} as const;

export const locationsApi = {
	// Get all locations with filters
	getLocations: async (
		filters: LocationsFilters = {},
		orgId: number
	): Promise<LocationsResponse["data"]> => {
		const params = new URLSearchParams();

		Object.entries(filters).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				if (Array.isArray(value)) {
					value.forEach((item) =>
						params.append(key, item.toString())
					);
				} else {
					params.append(key, value.toString());
				}
			}
		});
		params.append(
			"include",
			"provider_count,service_count,avg_waittime,stations"
		);
		const response = await apiClient.get(
			`${LOCATIONS_ENDPOINTS.base}?${params}`,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data.data;
	},

	// Get single location by ID
	getLocation: async (id: string, orgId: number): Promise<Location> => {
		const response = await apiClient.get(LOCATIONS_ENDPOINTS.byId(id), {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data.data;
	},

	// Create new location
	createLocation: async (
		data: CreateLocationRequest,
		orgId: number
	): Promise<Location> => {
		const response = await apiClient.post(LOCATIONS_ENDPOINTS.base, data, {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data;
	},

	// Update existing location
	updateLocation: async (
		data: UpdateLocationRequest,
		orgId: number
	): Promise<Location> => {
		const { id, ...updateData } = data;
		const response = await apiClient.put(
			LOCATIONS_ENDPOINTS.byId(id),
			updateData,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data;
	},

	// Delete location
	deleteLocation: async (id: string, orgId: number): Promise<void> => {
		await apiClient.delete(LOCATIONS_ENDPOINTS.byId(id), {
			headers: {
				"X-organizationId": orgId,
			},
		});
	},

	// Toggle location active status
	toggleLocationStatus: async (
		id: string,
		isActive: boolean,
		orgId: number
	): Promise<Location> => {
		const response = await apiClient.patch(
			LOCATIONS_ENDPOINTS.byId(id),
			{ isActive },
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data;
	},

	// Location Operating Hours API methods
	getLocationOperatingHours: async (
		locationId: string,
		orgId: number
	): Promise<LocationOperatingHoursResponse["data"]> => {
		const response = await apiClient.get(
			LOCATIONS_ENDPOINTS.operatingHours(locationId),
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object including hours and inheritance_info
		return response.data.data;
	},

	updateLocationOperatingHours: async (
		locationId: string,
		orgId: number,
		data: UpdateOperatingHoursRequest
	): Promise<LocationOperatingHoursResponse["data"]> => {
		const response = await apiClient.put(
			LOCATIONS_ENDPOINTS.operatingHours(locationId),
			data, // Send the array directly, not wrapped in an object
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object including hours and inheritance_info
		return response.data.data;
	},

	// Location Schedule Settings API methods
	getLocationScheduleSettings: async (
		locationId: string,
		orgId: number
	): Promise<LocationScheduleSettingsResponse["data"]> => {
		const response = await apiClient.get(
			LOCATIONS_ENDPOINTS.scheduleSettings(locationId),
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object with settings and inheritance_info at same level
		return response.data.data;
	},

	updateLocationScheduleSettings: async (
		locationId: string,
		orgId: number,
		data: UpdateScheduleSettingsRequest
	): Promise<LocationScheduleSettingsResponse["data"]> => {
		const response = await apiClient.patch(
			LOCATIONS_ENDPOINTS.scheduleSettings(locationId),
			data,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object with settings and inheritance_info at same level
		return response.data.data;
	},

	// Location Schedule Optimizer API methods
	getLocationScheduleOptimizer: async (
		locationId: string,
		orgId: number
	): Promise<LocationScheduleOptimizerResponse["data"]> => {
		const response = await apiClient.get(
			LOCATIONS_ENDPOINTS.scheduleOptimizer(locationId),
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object with optimizer and inheritance_info at same level
		return response.data.data;
	},

	updateLocationScheduleOptimizer: async (
		locationId: string,
		orgId: number,
		data: UpdateScheduleOptimizerRequest
	): Promise<LocationScheduleOptimizerResponse["data"]> => {
		const response = await apiClient.patch(
			LOCATIONS_ENDPOINTS.scheduleOptimizer(locationId),
			data,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object with optimizer and inheritance_info at same level
		return response.data.data;
	},
};
