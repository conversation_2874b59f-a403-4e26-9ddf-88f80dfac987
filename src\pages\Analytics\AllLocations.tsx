import React, { useState } from "react";

import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@/components/ui/accordion";
// import { StationsProps } from "@type/Location";
// import useLocationsStore from "@store/useLocationsStore";
// Remove API import
// import { GetAnalyticsQuerySlice } from "@/src/store/slices/analytics/getAnalyticsSlice";
import { dummyFilterData } from './data';
import { Checkbox } from "@/components/common/Checkbox";
// Define proper types
interface Location {
	id: number;
	name: string;
	stations: Array<{ id: number; name: string }>;
	selected_stations?: number[];
}

interface AllLocationsProps {
	locationNameHandler: (location: Location) => void;
	stationHandler: (locations: Location[]) => void;
}

const AllLocations: React.FC<AllLocationsProps> = ({ locationName<PERSON><PERSON><PERSON>, station<PERSON><PERSON><PERSON> }) => {
	const [selectedLocations, setSelectedLocations] = useState<Location[]>([]);

	// Use dummy data instead of API
	const locationsData = dummyFilterData.locations;

	const handleLocationCheckboxChange = (location: Location, isSelected: boolean) => {
		if (isSelected) {
			// Remove location
			setSelectedLocations(prev => prev.filter(loc => loc.id !== location.id));
		} else {
			// Add location
			setSelectedLocations(prev => [...prev, location]);
		}
		
		// Call the parent handler
		locationNameHandler(location);
	};

	const handleStationCheckboxChange = (location: Location, stationId: number, isSelected: boolean) => {
		setSelectedLocations(prev => {
			const updatedLocations = prev.map(loc => {
				if (loc.id === location.id) {
					const updatedLocation = { ...loc };
					if (!updatedLocation.selected_stations) {
						updatedLocation.selected_stations = [];
					}
					
					if (isSelected) {
						// Remove station
						updatedLocation.selected_stations = updatedLocation.selected_stations.filter((id: number) => id !== stationId);
					} else {
						// Add station
						if (!updatedLocation.selected_stations.includes(stationId)) {
							updatedLocation.selected_stations.push(stationId);
						}
					}
					return updatedLocation;
				}
				return loc;
			});
			
			// Call the parent handler
			stationHandler(updatedLocations);
			return updatedLocations;
		});
	};

	return (
		<Command className="rounded-lg border shadow-md">
			<CommandInput placeholder="Search by name" />
			<CommandList>
				<CommandEmpty>No results found.</CommandEmpty>
				<CommandGroup>
					{locationsData?.map(
									(location) => {
										const isLocationSelected =
								selectedLocations?.some(
									(l: Location) => l.id === location.id
											);
										return (
											<CommandItem key={location.id}>
												<Accordion
													type="single"
													collapsible
													className="w-full"
													value={
														isLocationSelected
															? "item-2"
															: ""
													}
												>
													<AccordionItem
														value="item-2"
														className="space-y-2.5 border-0"
													>
														<AccordionTrigger className="flex items-start justify-start gap-x-2 py-1 text-main-1 hover:no-underline">
															<Checkbox
													checked={
																	isLocationSelected
																}
													onCheckedChange={() => {
														handleLocationCheckboxChange(
															location,
															isLocationSelected
																	);
																}}
																className="rounded-sm border-2 border-[#D1D1D1]"
																id={
																	"location-" +
																	location.id
																}
															/>
															<label
																htmlFor={
																	"location-" +
																	location.id
																}
																className="flex flex-1 flex-col items-start space-y-2.5"
															>
																<h3 className="m-0 cursor-pointer text-sm font-medium leading-none">
																	{
																		location.name
																	}
																</h3>
															</label>
														</AccordionTrigger>
														<AccordionContent className="flex flex-col pb-0">
															<div className="ml-6 space-y-2.5">
																<>
																	{/*<div className="flex items-center gap-x-2">*/}
																	{/*	<Checkbox*/}
																	{/*		isChecked={*/}
																	{/*			field?.value?.find(*/}
																	{/*				(*/}
																	{/*					loc: any*/}
																	{/*				) =>*/}
																	{/*					loc.id ===*/}
																	{/*					location.id*/}
																	{/*			)*/}
																	{/*				?.apply_to_all_stations ||*/}
																	{/*			false*/}
																	{/*		}*/}
																	{/*		handleCheckboxChange={() => {*/}
																	{/*			const updatedLocations =*/}
																	{/*				[*/}
																	{/*					...field.value,*/}
																	{/*				];*/}

																	{/*			const selectedLocation =*/}
																	{/*				updatedLocations?.find(*/}
																	{/*					(*/}
																	{/*						l*/}
																	{/*					) =>*/}
																	{/*						l.id ===*/}
																	{/*						location.id*/}
																	{/*				);*/}

																	{/*			if (*/}
																	{/*				selectedLocation*/}
																	{/*			) {*/}
																	{/*				selectedLocation.apply_to_all_stations =*/}
																	{/*					selectedLocation.apply_to_all_stations*/}
																	{/*						? 0*/}
																	{/*						: 1;*/}
																	{/*				if (*/}
																	{/*					selectedLocation.apply_to_all_stations*/}
																	{/*				) {*/}
																	{/*					selectedLocation.selected_stations =*/}
																	{/*						location.stations?.map(*/}
																	{/*							(*/}
																	{/*								station*/}
																	{/*							) =>*/}
																	{/*								station.id*/}
																	{/*						);*/}
																	{/*				} else {*/}
																	{/*					selectedLocation.selected_stations =*/}
																	{/*						[];*/}
																	{/*				}*/}
																	{/*				field.onChange(*/}
																	{/*					updatedLocations*/}
																	{/*				);*/}
																	{/*			}*/}

																	{/*		}}*/}
																	{/*		className="rounded-sm border-2 border-[#D1D1D1]"*/}
																	{/*		id={`apply-to-all-${location.id}`}*/}
																	{/*	/>*/}
																	{/*	<label*/}
																	{/*		htmlFor={`apply-to-all-${location.id}`}*/}
																	{/*		className="cursor-pointer space-y-2.5"*/}
																	{/*	>*/}
																	{/*		<h3 className="m-0 text-sm font-medium leading-none">*/}
																	{/*			All*/}
																	{/*			Stations*/}
																	{/*			in*/}
																	{/*			this*/}
																	{/*			Location*/}
																	{/*		</h3>*/}
																	{/*	</label>*/}
																	{/*</div>*/}

																	{location.stations?.map(
																		(
																			station
																		) => (
																			<div
																				key={
																					station.id
																				}
																				className="flex items-center gap-x-2"
																			>
																				<Checkbox
																		checked={
																			selectedLocations
																							?.find(
																								(
																						loc: Location
																								) =>
																									loc.id ===
																									location.id
																							)
																							?.selected_stations?.includes(
																								station.id
																							) ||
																						false
																					}
																		onCheckedChange={() => {
																			handleStationCheckboxChange(
																				location,
																				station.id,
																				selectedLocations
																					?.find(
																								(
																							loc: Location
																								) =>
																							loc.id ===
																									location.id
																					)
																					?.selected_stations?.includes(
																									station.id
																					) ||
																				false
																						);
																					}}
																					className="rounded-sm border-2 border-[#D1D1D1]"
																					id={
																						"station-" +
																						station.id
																					}
																				/>

																				<label
																					htmlFor={
																						"station-" +
																						station.id
																					}
																					className="cursor-pointer space-y-2.5"
																				>
																					<h3 className="m-0 text-sm font-medium leading-none">
																						{
																							station.name
																						}
																					</h3>
																				</label>
																			</div>
																		)
																	)}
																</>
															</div>
														</AccordionContent>
													</AccordionItem>
												</Accordion>
											</CommandItem>
										);
									}
								)}
				</CommandGroup>
			</CommandList>
		</Command>
	);
};

export default AllLocations;
