import { useState } from "react";
import {
	X,
	Plus,
	Edit,
	Trash2,
	<PERSON><PERSON>,
	Power,
	PhoneCall,
	User,
	Video,
	Settings,
	Pen,
	Check,
	Clock4,
	<PERSON><PERSON><PERSON>,
	Send,
} from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { InputText } from "@/components/common/InputText";
import { Skeleton } from "@/components/ui/skeleton";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import type { ServiceData } from "../../api/servicesApi";
import { Checkbox } from "@/components/ui/checkbox";
import { useService } from "../../hooks/useService";

interface ServiceInfoSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	service: ServiceData | null;
	onEdit?: (service: ServiceData) => void;
}

export function ServiceInfoSheet({
	open,
	onOpenChange,
	service,
	onEdit,
}: ServiceInfoSheetProps) {
	const { organizationId } = useOrganizationContext();
	const [activeTab, setActiveTab] = useState("forms");

	// Fetch detailed service data with forms, locations, and stations
	const {
		data: serviceResponse,
		isLoading: isLoadingService,
		error: serviceError,
	} = useService({
		serviceId: service?.id,
		organizationId: organizationId || undefined,
		enabled: open && !!service?.id && !!organizationId,
		options: {
			basic: false,
			include: ["forms", "locations", "stations"],
		},
	});

	// Use the detailed service data if available, otherwise fall back to the passed service
	const detailedService = serviceResponse?.data || service;
	const forms = detailedService?.forms || [];
	const locations = detailedService?.locations || [];
	const stations = detailedService?.stations || [];

	const handleClose = () => {
		onOpenChange(false);
	};

	const handleEdit = () => {
		if (detailedService && onEdit) {
			onEdit(detailedService);
		}
	};

	const getServiceMethods = () => {
		if (!detailedService || !detailedService.appointment_methods) return [];
		return detailedService.appointment_methods.map((method) => ({
			id: method.id.toString(),
			label: method.name,
			icon: getMethodIcon(method.name),
		}));
	};

	const getMethodIcon = (name: string) => {
		const lowerName = name.toLowerCase();
		if (lowerName.includes("audio") || lowerName.includes("phone")) {
			return <PhoneCall className="h-4 w-4" />;
		} else if (
			lowerName.includes("person") ||
			lowerName.includes("office")
		) {
			return <User className="h-4 w-4" />;
		} else if (
			lowerName.includes("video") ||
			lowerName.includes("virtual")
		) {
			return <Video className="h-4 w-4" />;
		}
		return <User className="h-4 w-4" />; // Default icon
	};

	const formatDuration = (minutes: number) => {
		return `${minutes} mins`;
	};

	const formatPrice = (price: number) => {
		return `$${price.toFixed(2)}`;
	};

	if (!service) return null;

	// Show loading skeleton while fetching detailed service data
	if (isLoadingService) {
		return (
			<Sheet open={open} onOpenChange={onOpenChange}>
				<SheetContent className="z-[1003] w-[600px] overflow-y-auto p-10 sm:max-w-[800px]">
					<div className="space-y-4">
						<Skeleton className="h-8 w-64" />
						<Skeleton className="h-4 w-full" />
						<Skeleton className="h-4 w-3/4" />
						<Skeleton className="h-32 w-full" />
					</div>
				</SheetContent>
			</Sheet>
		);
	}

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="just z-[1003] flex w-[600px] overflow-y-auto p-10 sm:max-w-[800px]">
				<div className="rounded-md p-3 shadow-md">
					{/* Header */}
					<div className="flex items-center justify-between">
						<div>
							<SheetTitle className="text-xl font-semibold">
								{detailedService?.name}
							</SheetTitle>
						</div>
						<div className="flex items-center gap-2">
							<Button
								size="icon"
								className="h-8 w-8 bg-[#f5f5f5] text-[#56758A]"
							>
								<Settings className="h-4 w-4" />
							</Button>
							<Button
								size="icon"
								className="h-8 w-8 bg-[#f5f5f5] text-[#56758A]"
							>
								<Pen className="h-4 w-4" />
							</Button>
							<Button
								variant="ghost"
								size="icon"
								className="h-8 w-8 bg-[#f5f5f5] text-[#56758A]"
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</div>
					</div>

					{/* Service Details Header */}
					<div className="">
						<div className="flex items-start justify-between">
							<div className="flex flex-1 flex-col">
								{/* Service Status and Settings */}
								<div className="mb-6 flex items-center gap-4">
									<div className="flex items-center gap-2">
										<Badge
											variant={
												detailedService?.is_available
													? "default"
													: "secondary"
											}
											className={
												detailedService?.is_available
													? "bg-[#D8F0D9B2] text-[#18181B]"
													: "bg-[#F4F4F5] text-[#18181B]"
											}
										>
											{detailedService?.is_available
												? "Active"
												: "Inactive"}
										</Badge>
										<Badge
											variant="outline"
											className={
												detailedService?.is_available
													? "bg-[#D8F0D9B2] text-[#18181B]"
													: "bg-[#F4F4F5] text-[#18181B]"
											}
										>
											<Check className="h-4 w-4" />{" "}
											Service Available
										</Badge>

										<Badge
											variant="outline"
											className={
												detailedService?.auto_approve
													? "bg-[#D8F0D9B2] text-[#18181B]"
													: "bg-[#F4F4F5] text-[#18181B]"
											}
										>
											<Power />
											Auto Approve
										</Badge>
									</div>
								</div>

								<div className="flex items-center justify-between">
									{/* Service Methods */}
									<div className="my-3 flex items-center gap-3">
										{getServiceMethods().map((method) => (
											<div
												key={method.id}
												className="flex items-center gap-1 rounded-md bg-[#F5F5F5] px-3 py-1 text-sm"
											>
												<span>{method.icon}</span>
												<span>{method.label}</span>
											</div>
										))}
									</div>

									{/* Duration and Price */}
									<div className="flex items-center gap-6 text-sm text-gray-600">
										<div className="flex items-center gap-1">
											<Clock4 className="h-4 w-4" />
											<span>
												{formatDuration(
													detailedService?.time_in_minute ||
														30
												)}
											</span>
										</div>
										<div className="flex items-center gap-1 font-bold">
											<span>
												{formatPrice(12.99)}
											</span>{" "}
										</div>
									</div>
								</div>
							</div>

							{/* Action Buttons */}
						</div>
					</div>
				</div>

				{/* Tabs Content */}
				<div className="">
					<Tabs
						value={activeTab}
						onValueChange={setActiveTab}
						className="w-full"
					>
						<TabsList className="mb-6 grid w-full grid-cols-3 bg-[#F4F4F5]">
							<TabsTrigger value="forms">Forms</TabsTrigger>
							<TabsTrigger value="locations">
								Locations
							</TabsTrigger>
							<TabsTrigger value="providers">
								Stations
							</TabsTrigger>
						</TabsList>

						{/* Forms Tab */}
						<TabsContent value="forms" className="space-y-4">
							<div className="flex h-[40vh] w-full flex-col overflow-hidden rounded-lg border border-zinc-200">
								<div className="text-muted flex h-12 items-center justify-between border-b py-1 pl-4">
									<div className="flex items-center pr-4">
										<Checkbox />
									</div>
									<div className="flex flex-2 items-center px-3">
										<div className="flex items-center gap-3 text-sm">
											<p>Form name</p>
										</div>
									</div>

									<div className="flex flex-1 items-center px-3">
										<div className="flex items-center gap-3 text-sm text-nowrap">
											<p>Status</p>
										</div>
									</div>
									<div className="flex flex-1 items-center px-3">
										<div className="flex items-center gap-3">
											<p>Actions</p>
										</div>
									</div>
								</div>
								<div className="flex flex-col gap-0.5">
									{forms.length > 0 ? (
										forms.map((form) => (
											<div
												key={form.id}
												className="hover:bg-foreground-muted flex cursor-pointer flex-wrap items-center justify-start border-b border-zinc-200 bg-white last:border-b-0"
											>
												<div
													className="flex h-16 items-center px-4"
													onClick={(e) =>
														e.stopPropagation()
													}
												>
													<Checkbox />
												</div>
												<div className="flex-2">
													<p className="text-sm font-medium text-gray-900">
														{form.name}
													</p>
												</div>

												<div className="flex flex-1 items-center px-3">
													<Badge
														variant={
															form.status ===
															"active"
																? "default"
																: "secondary"
														}
														className="text-xs"
													>
														{form.status}
													</Badge>
												</div>
												<div className="flex flex-1 items-center gap-1 pr-4">
													<Button
														variant="outline"
														size="icon"
														className="h-8 w-8"
													>
														<Pencil className="h-4 w-4" />
													</Button>
													<Button
														variant="outline"
														size="icon"
														className="h-8 w-8"
													>
														<Send className="h-4 w-4" />
													</Button>
													<Button
														variant="outline"
														size="icon"
														className="h-8 w-8"
													>
														<Copy className="h-4 w-4" />
													</Button>
												</div>
											</div>
										))
									) : (
										<div className="flex h-32 items-center justify-center text-gray-500">
											<p>
												No forms associated with this
												service
											</p>
										</div>
									)}
								</div>
							</div>
						</TabsContent>

						{/* Locations Tab */}
						<TabsContent value="locations" className="space-y-4">
							<div className="flex h-[40vh] w-full flex-col overflow-hidden rounded-lg border border-zinc-200">
								<div className="text-muted flex h-12 items-center justify-between border-b py-1 pl-4">
									<div className="flex items-center pr-4">
										<Checkbox />
									</div>
									<div className="flex flex-2 items-center px-3">
										<div className="flex items-center gap-3 text-sm">
											<p>Location name</p>
										</div>
									</div>
									<div className="flex flex-1 items-center px-3">
										<div className="flex items-center gap-3 text-sm text-nowrap">
											<p>Address</p>
										</div>
									</div>
									<div className="flex flex-1 items-center px-3">
										<div className="flex items-center gap-3">
											<p></p>
										</div>
									</div>
								</div>
								<div className="flex flex-col gap-0.5">
									{locations.length > 0 ? (
										locations.map((location) => (
											<div
												key={location.id}
												className="hover:bg-foreground-muted flex cursor-pointer flex-wrap items-center justify-start border-b border-zinc-200 bg-white last:border-b-0"
											>
												<div
													className="flex h-16 items-center px-4"
													onClick={(e) =>
														e.stopPropagation()
													}
												>
													<Checkbox />
												</div>
												<div className="flex-2">
													<p className="text-sm font-medium text-gray-900">
														{location.name}
													</p>
												</div>
												<div className="flex flex-1 items-center px-3">
													<p className="text-sm text-gray-600">
														{location.address ||
															"No address provided"}
													</p>
												</div>
												<div className="flex flex-1 items-center gap-1 pr-4">
													<Button
														variant="outline"
														size="icon"
														className="h-8 w-8"
													>
														<Pencil className="h-4 w-4" />
													</Button>
													<Button
														variant="outline"
														size="icon"
														className="h-8 w-8"
													>
														<Send className="h-4 w-4" />
													</Button>
												</div>
											</div>
										))
									) : (
										<div className="flex h-32 items-center justify-center text-gray-500">
											<p>
												No locations associated with
												this service
											</p>
										</div>
									)}
								</div>
							</div>
						</TabsContent>

						{/* Stations Tab */}
						<TabsContent value="providers" className="space-y-4">
							<div className="flex h-[40vh] w-full flex-col overflow-hidden rounded-lg border border-zinc-200">
								<div className="text-muted flex h-12 items-center justify-between border-b py-1 pl-4">
									<div className="flex items-center pr-4">
										<Checkbox />
									</div>
									<div className="flex flex-2 items-center px-3">
										<div className="flex items-center gap-3 text-sm">
											<p>Providers</p>
										</div>
									</div>
									<div className="flex flex-1 items-center px-3">
										<div className="flex items-center gap-3">
											<p></p>
										</div>
									</div>
								</div>
								<div className="flex flex-col gap-0.5">
									{stations.length > 0 ? (
										stations.map((station) => (
											<div
												key={station.id}
												className="hover:bg-foreground-muted flex cursor-pointer flex-wrap items-center justify-start border-b border-zinc-200 bg-white last:border-b-0"
											>
												<div
													className="flex h-16 items-center px-4"
													onClick={(e) =>
														e.stopPropagation()
													}
												>
													<Checkbox />
												</div>
												<div className="flex-2">
													<p className="text-sm font-medium text-gray-900">
														{station.name}
													</p>
												</div>

												<div className="flex flex-1 items-center gap-1 pr-4">
													<Button
														variant="outline"
														size="icon"
														className="h-8 w-8"
													>
														<Pencil className="h-4 w-4" />
													</Button>
													<Button
														variant="outline"
														size="icon"
														className="h-8 w-8"
													>
														<Send className="h-4 w-4" />
													</Button>
												</div>
											</div>
										))
									) : (
										<div className="flex h-32 items-center justify-center text-gray-500">
											<p>
												No stations associated with this
												service
											</p>
										</div>
									)}
								</div>
							</div>
						</TabsContent>
					</Tabs>
				</div>

				{/* Footer */}
				<SheetFooter className="mt-6 flex justify-self-end">
					<div className="mt-8 border-t px-6 py-4">
						<div className="flex items-center justify-end gap-3">
							<Button variant="outline" onClick={handleClose}>
								Close
							</Button>
							<Button
								onClick={handleEdit}
								className="bg-blue-600 hover:bg-blue-700"
							>
								Edit
							</Button>
						</div>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
