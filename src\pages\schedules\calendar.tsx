import {
    Calendar as Big<PERSON>ale<PERSON><PERSON>,
    momentLocalizer, Views
} from "react-big-calendar";
import type { SlotInfo } from "react-big-calendar";
import moment from "moment";
import { useCallback, useRef, useState, useEffect, useLayoutEffect } from "react";
import { CustomEventComponent } from "./components/event";
import CustomToolbar from "./components/custom-toolbar";
import { CustomResourceHeader } from "./components/custom-resource-header";
import { getAppointmentColors } from "@/pages/schedules/utils/index";
import { CustomTimeSlotWrapper } from "./components/custom-time-slot";
import { generateAppointments, resources } from "./db";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuShortcut
} from "@/components/ui/dropdown-menu";
import { TbUserSquare } from "react-icons/tb";
import { PiBellRinging } from "react-icons/pi";
import clsx from "clsx";
import { Button } from "@/components/ui/Button/Button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { getWeekDays } from "@/pages/schedules/utils/index";
import ScheduleOptimizer from "./schedule-optimizer";
import BookAppointment from "./book-appointment";

type Keys = keyof typeof Views;

interface Appointment {
    title: string;
}

export default function Calendar() {
    const localizer = momentLocalizer(moment);
    const clickRef = useRef<number>(0);
    const lastMouseEvent = useRef<MouseEvent | null>(null);
    const [open, setOpen] = useState(false)
    const [date, setDate] = useState<Date | undefined>(undefined)
    const [view, setView] = useState<(typeof Views)[Keys]>(Views.DAY);
    const [optimizerOpen, setOptimizerOpen] = useState<boolean>(false);
    const [activeDoctor, setActiveDoctor] = useState<number>(0);
    const [arrowPosition, setArrowPosition] = useState(0);
    const [arrowWidth, setArrowWidth] = useState(0);
    const [appointmentOpen, setAppointmentOpen] = useState<boolean>(false);
    const [contextMenuInfo, setContextMenuInfo] = useState<{
        xPosition: number;
        yPosition: number;
        selectedTime: string;
        resourceId: number;
    }>();

    const dayHeadersRef = useRef<HTMLDivElement>(null);
    const calendarContentRef = useRef<HTMLDivElement>(null);

    const resourceRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

    // Ref for resources (doctors) scroll container
    const resourcesScrollRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleMouseDown = (e: MouseEvent) => {
            lastMouseEvent.current = e;
        };
        document.addEventListener('mousedown', handleMouseDown);
        return () => {
            document.removeEventListener('mousedown', handleMouseDown);
        };
    }, []);

    useEffect(() => {
        const calendarContent = document.querySelector('.rbc-time-view-resources .rbc-time-content') as HTMLElement;
        const dayHeaders = dayHeadersRef.current;
        const providerHeaders = resourcesScrollRef.current;

        if (calendarContent && dayHeaders && view === Views.WEEK) {
            const handleCalendarScroll = () => {
                dayHeaders.scrollLeft = calendarContent.scrollLeft;
                if (providerHeaders) {
                    providerHeaders.scrollLeft = calendarContent.scrollLeft;
                }
            };

            const handleHeaderScroll = () => {
                calendarContent.scrollLeft = dayHeaders.scrollLeft;
                if (providerHeaders) {
                    providerHeaders.scrollLeft = dayHeaders.scrollLeft;
                }
            };

            const handleProviderScroll = () => {
                if (providerHeaders) {
                    calendarContent.scrollLeft = providerHeaders.scrollLeft;
                    dayHeaders.scrollLeft = providerHeaders.scrollLeft;
                }
            };

            calendarContent.addEventListener('scroll', handleCalendarScroll);
            dayHeaders.addEventListener('scroll', handleHeaderScroll);
            if (providerHeaders) {
                providerHeaders.addEventListener('scroll', handleProviderScroll);
            }

            return () => {
                calendarContent.removeEventListener('scroll', handleCalendarScroll);
                dayHeaders.removeEventListener('scroll', handleHeaderScroll);
                if (providerHeaders) {
                    providerHeaders.removeEventListener('scroll', handleProviderScroll);
                }
            };
        }
    }, [view]);

    useLayoutEffect(() => {
        const activeTabElement = resourceRefs.current[activeDoctor];
        if (activeTabElement) {
            setArrowPosition(activeTabElement.offsetLeft);
            setArrowWidth(activeTabElement.offsetWidth);
        }
    }, [activeDoctor, resources.length, view]);

    const events = generateAppointments({
        view: view === Views.WEEK ? 'week' : view === Views.MONTH ? 'month' : 'day',
        activeDoctor,
        date: date || new Date(),
    });

    const CustomEvent = ({ event }: { event: any }) => (
        <div style={{
            display: 'flex',
            flexDirection: 'row',
            gap: '3px',
            width: '100%',
            height: '100%',
            alignItems: 'stretch'
        }}>
            {event.appointments.map((appt: Appointment, index: number, arr: Appointment[]) => {
                const appointmentColors = getAppointmentColors(arr.length)

                return <CustomEventComponent
                    key={index}
                    index={index}
                    start={event.start}
                    end={event.end}
                    title={appt.title}
                    arrLength={arr.length}
                    appointmentColors={appointmentColors}
                />
            }
            )}
        </div>
    );

    const components = {
        resourceHeader: CustomResourceHeader,
        event: CustomEvent,
        timeSlotWrapper: CustomTimeSlotWrapper,
    }

    const onPrevClick = useCallback(() => {
        if (view === Views.DAY) {
            setDate(moment(date).subtract(1, "d").toDate());
        } else if (view === Views.WEEK) {
            setDate(moment(date).subtract(1, "w").toDate());
        } else {
            setDate(moment(date).subtract(1, "M").toDate());
        }
    }, [view, date]);

    const onNextClick = useCallback(() => {
        if (view === Views.DAY) {
            setDate(moment(date).add(1, "d").toDate());
        } else if (view === Views.WEEK) {
            setDate(moment(date).add(1, "w").toDate());
        } else {
            setDate(moment(date).add(1, "M").toDate());
        }
    }, [view, date]);

    const onSelectSlot = useCallback((slotInfo: SlotInfo) => {
        window.clearTimeout(clickRef?.current)
        clickRef.current = window.setTimeout(() => {
            const mouseEvent = lastMouseEvent.current;
            if (!mouseEvent) return;
            const selectedTime = moment(slotInfo.start).format('HH:mm');
            setContextMenuInfo({
                xPosition: mouseEvent.clientX,
                yPosition: mouseEvent.clientY,
                selectedTime,
                resourceId: typeof slotInfo.resourceId === 'number' ? slotInfo.resourceId : 0
            });
        }, 250)
    }, [])

    return (
        <div className="w-full border border-b-0 pt-2 rounded-lg overflow-hidden scrollbar-hide">

            <DropdownMenu open={!!contextMenuInfo} onOpenChange={() => setContextMenuInfo(undefined)}>
                <DropdownMenuContent
                    className="w-80" align="end"
                    style={{
                        position: 'fixed',
                        left: contextMenuInfo?.xPosition || 0,
                        top: contextMenuInfo?.yPosition || 0,
                        zIndex: 30000
                    }}>
                    <DropdownMenuLabel className="font-bold">Time Slot Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuGroup>
                        <DropdownMenuItem onClick={() => {
                            setAppointmentOpen(true);
                            setContextMenuInfo(undefined);
                        }}>
                            <TbUserSquare />
                            <p className="ml-1">Book an Appointment</p>
                            <DropdownMenuShortcut>
                                ⇧⌘P
                            </DropdownMenuShortcut>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {
                            setContextMenuInfo(undefined);
                            setOptimizerOpen(true);
                        }}>
                            <PiBellRinging />
                            <p className="ml-1">Trigger Schedule Optimizer</p>
                            <DropdownMenuShortcut>⌘⇧C</DropdownMenuShortcut>
                        </DropdownMenuItem>

                    </DropdownMenuGroup>
                </DropdownMenuContent>
            </DropdownMenu>

            <style>
                {`
                                            .rbc-time-view.rbc-time-view-resources {
                            border: none !important;
                        }

                        .rbc-time-view-resources .rbc-time-header {
                            display: flex !important;
                            align-items: stretch !important;
                            border-bottom: 1px solid #e5e7eb !important;
                        }

                        .rbc-time-view-resources .rbc-time-header .rbc-header {
                            flex: 1 !important;
                            display: flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                            border-right: 1px solid #e5e7eb !important;
                            margin: 0 !important;
                            padding: 0 !important;
                        }

                        .rbc-time-view-resources .rbc-time-header .rbc-header:last-child {
                            border-right: none !important;
                        }
                    .rbc-label.rbc-time-header-gutter {
                        border: none !important;
                    }
                `}
            </style>

            {(view === Views.DAY || view === Views.WEEK) && (
                <style>
                    {`                 
                        .rbc-header {
                            min-width: 12rem;
                            padding: 3px 0 !important;
                            height: auto !important;
                            display: flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                        }

                        .rbc-day-slot {
                            min-width: 12rem !important;
                            width: 12rem !important;
                            border-right: 1px solid #e5e7eb !important;
                            margin: 0 !important;
                            padding: 0 !important;
                        }

                        .rbc-day-slot:last-child {
                            border-right: none !important;
                        }

                        .rbc-row-content {
                            display: none;
                        }

                        .rbc-time-content {
                            border-top-width: 1px !important;
                        }

                        .rbc-time-view-resources .rbc-time-content .rbc-day-slot {
                            border-right: 1px solid #e5e7eb !important;
                        }

                        .rbc-time-view-resources .rbc-time-content .rbc-day-slot:last-child {
                            border-right: none !important;
                        }

                        .rbc-row {
                            border: none !important;
                        }

                        .rbc-label.rbc-time-header-gutter {
                            min-width: 75px !important;
                            max-width: 75px !important;
                            width: 75px !important;
                            z-index: 1001 !important;
                            font-size: 11px !important;
                            display: flex !important;
                            align-items: center !important;
                            justify-content: flex-end !important;
                            padding-right: 8px !important;
                            border-right: 1px solid #e5e7eb !important;
                            border-bottom: 1px solid #e5e7eb !important;
                        }
                        
                        .rbc-time-slot {
                            min-height: 9px !important;
                            height: 9px !important;
                            border-bottom: 1px solid #f0f0f0 !important;
                        }

                        .rbc-time-gutter .rbc-timeslot-group {
                            min-height: 18px !important;
                            height: 18px !important;
                            border-right: 1px solid #e5e7eb !important;
                            width: 75px !important;
                        }
                        
                        .rbc-time-view-resources .rbc-time-gutter {
                            width: 75px !important;
                            min-width: 75px !important;
                            max-width: 75px !important;
                        }

                        .rbc-time-gutter .rbc-time-slot {
                            height: 9px !important;
                            font-size: 11px !important;
                        }

                        .rbc-time-header {
                            height: auto !important;
                            min-height: 15px !important;
                        }

                        .rbc-header {
                            padding: 2px 0 !important;
                            font-size: 14px !important;
                        }

                        .rbc-time-content {
                            min-height: auto !important;
                        }

                        .rbc-calendar {
                            height: auto !important;
                        }

                        .rbc-time-view .rbc-time-gutter {
                            white-space: nowrap;
                        }

                        .rbc-time-view {
                            border: none !important;
                        }

                        .rbc-time-gutter .rbc-timeslot-group .rbc-time-slot {
                            padding: 0 !important;
                            margin: 0 !important;
                            line-height: 0.8 !important;
                        }

                        .rbc-time-gutter .rbc-timeslot-group .rbc-label {
                            padding: 1px 4px !important;
                            margin: 0 !important;
                            line-height: 1 !important;
                            font-weight: 500 !important;
                            font-size: 11px !important;
                        }

                        .rbc-event-label {
                            display: none !important;
                        }

                        .rbc-event-content {
                            display: flex !important;
                            flex-direction: row !important;
                            gap: 3px !important;
                            overflow-x: auto !important;
                            overflow-y: hidden !important;
                            align-items: stretch !important;
                            scrollbar-width: none;
                            -ms-overflow-style: none;

                            &::-webkit-scrollbar {
                                display: none;
                            }
                        }

                        .rbc-timeslot-group {
                            padding: 0px !important;
                            background: white !important;
                            min-height: 18px !important;
                            height: 18px !important;
                        }

                        .rbc-timeslot-group {
                            padding: 0px !important;
                            min-height: 18px !important;
                            height: 18px !important;
                        }

                        .rbc-day-slot .rbc-event,
                        .rbc-day-slot .rbc-background-event {
                            background: transparent !important;
                            border: none !important;
                            display: flex !important;
                            flex-direction: row !important;
                            width: 100% !important;
                        }

                        .rbc-events-container {
                            margin-right: 0px !important;
                            display: flex !important;
                            flex-direction: row !important;
                            width: 100% !important;
                            align-items: stretch !important;
                        }

                        .rbc-event:focus-visible,
                        .rbc-event-content:focus-visible,
                        .rbc-event-content:focus,
                        .rbc-event:focus {
                            outline: none !important;
                            border: none !important;
                        }

                        .rbc-slot-selection {
                            z-index: 10 !important;
                        }

                        .rbc-time-slot .rbc-event {
                            display: flex !important;
                            flex-direction: row !important;
                            flex: 1 !important;
                            min-width: 0 !important;
                        }

                        .rbc-addons-dnd .rbc-addons-dnd-drag-preview {
                            z-index: 999 !important;
                        }

                        .rbc-event .rbc-event-content > div {
                            display: flex !important;
                            flex-direction: row !important;
                            gap: 2px !important;
                            width: 100% !important;
                        }

                        .rbc-day-slot .rbc-events-container {
                            padding: 1px !important;
                        }

                        .scrollbar-hide {
                            scrollbar-width: none;
                            -ms-overflow-style: none;
                        }

                        .scrollbar-hide::-webkit-scrollbar {
                            display: none;
                        }

                        .rbc-calendar {
                            scrollbar-width: none !important;
                            -ms-overflow-style: none !important;
                        }

                        .rbc-calendar::-webkit-scrollbar {
                            display: none !important;
                        }

                        .rbc-time-view {
                            scrollbar-width: none !important;
                            -ms-overflow-style: none !important;
                        }

                        .rbc-time-view::-webkit-scrollbar {
                            display: none !important;
                        }

                        .rbc-time-content {
                            scrollbar-width: none !important;
                            -ms-overflow-style: none !important;
                        }

                        .rbc-time-content::-webkit-scrollbar {
                            display: none !important;
                        }

                        .rbc-time-content > * {
                            scrollbar-width: none !important;
                            -ms-overflow-style: none !important;
                        }

                        .rbc-time-content > *::-webkit-scrollbar {
                            display: none !important;
                        }

                        .rbc-time-view-resources {
                            scrollbar-width: none !important;
                            -ms-overflow-style: none !important;
                        }

                        .rbc-time-view-resources::-webkit-scrollbar {
                            display: none !important;
                        }

                        .rbc-time-view-resources .rbc-time-content {
                            scrollbar-width: none !important;
                            -ms-overflow-style: none !important;
                        }

                        .rbc-time-view-resources .rbc-time-content::-webkit-scrollbar {
                            display: none !important;
                        }
                    `}
                </style>
            )}

            {view === Views.MONTH && (
                <style>
                    {`
                        .rbc-header {
                            min-width: 8rem !important;
                            padding: 4px 0 !important;
                            font-size: 12px !important;
                            font-weight: 600 !important;
                        }
                        .rbc-day-bg.rbc-off-range-bg {
                            width: 8rem !important;
                            min-width: 8rem !important;
                        }
                        .rbc-day-bg {
                            width: 8rem !important;
                            min-width: 8rem !important;
                        }
                        .rbc-month-view {
                            width: 100% !important;
                            min-width: fit-content !important;
                        }

                        .rbc-month-row {
                            border-bottom: 1px solid #e5e7eb !important;
                        }

                        .rbc-date-cell {
                            min-height: 100px !important;
                            height: 100px !important;
                            position: relative !important;
                        }
                        .rbc-day-bg.rbc-off-range-bg,
                        .rbc-day-bg.rbc-today {
                            background: transparent !important;
                        }
                        .rbc-date-cell.rbc-off-range {
                            color: #000 !important;
                        }
                        .rbc-header > span {
                            font-weight: 600 !important;
                        }
                        .rbc-date-cell {
                            text-align: start !important;
                            padding-top: 4px !important;
                            padding-left: 8px !important;
                            font-size: 12px !important;
                            font-weight: 600 !important;
                        }
                        .rbc-date-cell.rbc-now.rbc-current > button {
                            background: #005893 !important;
                            color: #fff !important;
                            padding: 4px 6px;
                            border-radius: 6px;
                            font-size: 11px !important;
                            font-weight: 600 !important;
                        }

                        .rbc-month-view .rbc-event {
                            font-size: 11px !important;
                            padding: 2px 6px !important;
                            margin: 2px 4px !important;
                            border-radius: 4px !important;
                            display: block !important;
                            max-width: calc(100% - 8px) !important;
                            overflow: hidden !important;
                            text-overflow: ellipsis !important;
                            white-space: nowrap !important;
                        }

                        .rbc-show-more {
                            color: #005893 !important;
                            font-size: 10px !important;
                            font-weight: 600 !important;
                            cursor: pointer !important;
                            text-decoration: none !important;
                        }

                        .rbc-show-more:hover {
                            color: #003d66 !important;
                            text-decoration: underline !important;
                        }

                        .rbc-month-view .rbc-date-cell {
                            border-right: 1px solid #e5e7eb !important;
                            border-bottom: 1px solid #e5e7eb !important;
                            overflow: visible !important;
                        }

                        .rbc-month-view .rbc-events-container {
                            margin-top: 4px !important;
                        }

                        .rbc-month-header {
                            border-bottom: 2px solid #e5e7eb !important;
                        }
                    `}
                </style>
            )}

            {view === Views.WEEK && (
                <style>
                    {`
                    .rbc-header, .rbc-time-header {
                        display: none;
                    }
                    
                    .rbc-time-view-resources .rbc-time-header-content {
                        display: none !important;
                    }
                    
                    .rbc-time-view-resources .rbc-time-content {
                        border-left: none !important;
                    }
                    
                                            .rbc-time-view-resources .rbc-time-content .rbc-day-slot {
                            border-left: none !important;
                        }

                        .rbc-time-view-resources .rbc-time-content {
                            overflow-x: auto !important;
                            scrollbar-width: none !important;
                            -ms-overflow-style: none !important;
                            scroll-behavior: auto !important;
                        }

                        .rbc-time-view-resources .rbc-time-content::-webkit-scrollbar {
                            display: none !important;
                        }
                    .rbc-day-slot {
	                    min-width: 12rem !important;
                        width: 12rem !important;
                    }
                                                                .rbc-time-view.rbc-time-view-resources {
                        border-top-width: 0;
                        border-collapse: separate !important;
                        border-spacing: 0 !important;
                    }

                    .rbc-time-view-resources table {
                        border-collapse: separate !important;
                        border-spacing: 0 !important;
                    }
                    `}
                </style>
            )}

            <ScheduleOptimizer open={optimizerOpen} onOpenChange={setOptimizerOpen} />

            <BookAppointment open={appointmentOpen} onOpenChange={setAppointmentOpen} />


            <div className={clsx("flex flex-col gap-y-2", (view === Views.WEEK || view === Views.MONTH) && "flex-col-reverse", (view !== Views.MONTH) && "px-4", (view === Views.MONTH) && "px-2")}>

                {view === Views.WEEK ? (
                    <div>
                        <div className="flex items-center justify-between gap-x-2 mb-4 -mt-2">
                            <Button variant="outline" className="!px-1.5 !py-1.5 h-8 rounded-lg cursor-pointer"
                                onClick={() => {
                                    resourcesScrollRef.current?.scrollBy({ left: -200, behavior: 'smooth' });
                                }}
                            >
                                <ChevronLeft style={{ width: "1rem", height: "1rem" }} />
                            </Button>
                            <div className="relative flex max-w-[100%] overflow-x-auto scrollbar-hide" ref={resourcesScrollRef}>
                                {resources.map((resource, index) => (
                                    <button
                                        key={index}
                                        tabIndex={0}
                                        ref={(el) => {
                                            resourceRefs.current[index] = el;
                                        }}
                                        onClick={() => setActiveDoctor(index)}
                                        className={clsx("cursor-pointer relative w-[12rem] flex-shrink-0 whitespace-nowrap p-1.5 px-2 border border-transparent transition-all duration-300 ease-in-out", activeDoctor === index && "bg-[#0058930A]")}
                                    >
                                        <p className="font-medium text-sm">{resource.resourceTitle}</p>
                                    </button>
                                ))}
                                <div className="absolute bottom-0 left-0 w-full h-[2.4px] bg-[#005893] transition-all duration-300 ease-in-out" style={{
                                    left: arrowPosition,
                                    width: arrowWidth,
                                }} />
                            </div>
                            <Button variant="outline" className="!px-1.5 !py-1.5 h-8 rounded-lg cursor-pointer"
                                onClick={() => {
                                    resourcesScrollRef.current?.scrollBy({ left: 200, behavior: 'smooth' });
                                }}
                            >
                                <ChevronRight style={{ width: "1rem", height: "1rem" }} />
                            </Button>
                        </div>
                        <div
                            ref={dayHeadersRef}
                            className="flex max-w-[100%] overflow-x-auto scrollbar-hide border-b border-gray-200"
                            style={{ marginLeft: '0px' }}
                        >
                            <div className="w-[75px] flex-shrink-0 border-r border-gray-200" style={{ borderRight: '1px solid #e5e7eb' }}></div>
                            {getWeekDays(date || new Date()).map((d, i, arr) => {
                                const dayName = d.toLocaleDateString('en-US', { weekday: 'short' });
                                return (
                                    <div
                                        className="w-[12rem] flex justify-center py-2 flex-shrink-0"
                                        key={i}
                                        style={{
                                            borderRight: i < arr.length - 1 ? '1px solid #e5e7eb' : 'none'
                                        }}
                                    >
                                        <div className={clsx("px-2 py-0.5 rounded-md font-semibold text-xs", d.getDate() === new Date().getDate() ? "bg-[#00589329]" : "")}>
                                            <span className="mr-1">{dayName}</span>
                                            <span>{d.getDate()}</span>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                ) : view === Views.MONTH ? (
                    <div className="relative grid grid-flow-col max-w-[100%] overflow-x-auto scrollbar-hide" ref={resourcesScrollRef}>
                        {resources.map((resource, index, arr) => (
                            <button
                                key={index}
                                tabIndex={0}
                                ref={(el) => {
                                    resourceRefs.current[index] = el;
                                }}
                                onClick={() => setActiveDoctor(index)}
                                className={clsx("cursor-pointer relative min-w-[8rem] whitespace-nowrap py-1.5 transition-all duration-300 ease-in-out")}
                            >
                                <div className={clsx("border-r border-[#005893] h-5", index === arr.length - 1 && "border-r-0")}>
                                    <p className="font-medium text-sm truncate">{resource.resourceTitle}</p>
                                </div>
                            </button>
                        ))}
                        <div className="absolute bottom-0 left-0 w-full h-[2.4px] bg-[#005893] transition-all duration-300 ease-in-out" style={{
                            left: arrowPosition,
                            width: arrowWidth,
                        }} />
                    </div>
                ) : null}

                <CustomToolbar
                    onPrevClick={onPrevClick}
                    onNextClick={onNextClick}
                    date={date || new Date()}
                    view={view}
                    setDate={(date: Date) => setDate(date)}
                    setView={(view: string) => setView(view as (typeof Views)[Keys])}
                    open={open}
                    setOpen={setOpen}
                />
            </div>

            <BigCalendar
                selectable={true}
                onSelectSlot={onSelectSlot}
                events={events}
                localizer={localizer}
                resources={(view === Views.DAY) ? resources : undefined}
                resourceIdAccessor="resourceId"
                resourceTitleAccessor="resourceTitle"
                defaultView={Views.DAY}
                components={{
                    ...components,
                }}
                view={view}
                date={date}
                views={['day', 'week', 'month']}
                className="scrollbar-hide"
                style={{
                    height: "calc(100vh - 200px)",
                    width: "100%",
                    overflow: "auto",
                    scrollbarWidth: "none",
                    msOverflowStyle: "none"
                }}
                startAccessor="start"
                endAccessor="end"
                toolbar={false}
                step={60}
                timeslots={1}
            />
        </div>
    )
}
