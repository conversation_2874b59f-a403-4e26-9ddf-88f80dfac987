import React, { useState } from 'react';
import { ChevronLeft, Search, SlidersHorizontal, Plus, Edit, Trash2, GripVertical } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import type { DragEndEvent } from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface ServiceRule {
  id: string;
  title: string;
  frequency: string;
  occurrence: string;
  availability: string;
  timePeriod: string;
  createdDate: string;
  priority: number;
}

interface ServiceRulesListProps {
  serviceName: string;
  rules: ServiceRule[];
  onBack: () => void;
  onAddPreference: () => void;
  onEditRule: (ruleId: string) => void;
  onDeleteRule: (ruleId: string) => void;
  onReorderRules: (rules: ServiceRule[]) => void;
  sortable?: boolean;
}

export const ServiceRulesList: React.FC<ServiceRulesListProps> = ({
  serviceName,
  rules,
  onBack,
  onAddPreference,
  onEditRule,
  onDeleteRule,
  onReorderRules,
  sortable = false
}) => {
  const [localRules, setLocalRules] = useState(rules);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRules, setSelectedRules] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedRules(localRules.map(rule => rule.id));
    } else {
      setSelectedRules([]);
    }
  };

  const handleSelectRule = (ruleId: string, checked: boolean) => {
    if (checked) {
      setSelectedRules(prev => [...prev, ruleId]);
    } else {
      setSelectedRules(prev => prev.filter(id => id !== ruleId));
      setSelectAll(false);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id || !sortable) return;

    const oldIndex = localRules.findIndex((item) => item.id === active.id);
    const newIndex = localRules.findIndex((item) => item.id === over.id);

    const newItems = arrayMove(localRules, oldIndex, newIndex);

    // Update priority based on new order
    const updatedRules = newItems.map((rule, index) => ({
      ...rule,
      priority: index + 1
    }));

    setLocalRules(updatedRules);
    onReorderRules(updatedRules);
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const filteredRules = localRules.filter(rule =>
    rule.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.frequency.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rule.occurrence.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const SortableRule: React.FC<{ rule: ServiceRule; index: number }> = ({ rule, index }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({ id: rule.id });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging ? 0.5 : 1,
    };

    return (
      <div ref={setNodeRef} style={style}>
        {renderRule(rule, index, isDragging, { attributes, listeners })}
      </div>
    );
  };

  const renderRule = (
    rule: ServiceRule,
    index: number,
    isDragging?: boolean,
    dragHandleProps?: { attributes: any; listeners: any }
  ) => (
    <div className={`bg-white border rounded-lg p-4 ${isDragging ? 'shadow-lg' : ''}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <Checkbox
            checked={selectedRules.includes(rule.id)}
            onCheckedChange={(checked) => handleSelectRule(rule.id, checked as boolean)}
          />
          <div className="text-blue-600 font-medium">{String(rule.priority).padStart(2, '0')}</div>
          <h3 className="font-medium">{rule.title}</h3>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Created: {rule.createdDate}</span>
          <div className="w-6 h-6 rounded-full border border-gray-300 flex items-center justify-center">
            <span className="text-xs">?</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Badge variant="outline" className="bg-gray-100">Frequency</Badge>
            <span className="text-sm">{rule.frequency}</span>
          </div>
          <div className="flex space-x-2">
            <Badge variant="secondary" className="bg-green-100 text-green-800">Availability</Badge>
            <span className="text-sm">{rule.availability}</span>
          </div>
        </div>
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Badge variant="outline" className="bg-gray-100">Occurrence</Badge>
            <span className="text-sm">{rule.occurrence}</span>
          </div>
          <div className="flex space-x-2">
            <Badge variant="outline" className="bg-gray-100">Time Period</Badge>
            <span className="text-sm">{rule.timePeriod}</span>
          </div>
        </div>
      </div>

      <div className="flex items-center justify-between">
        {sortable && dragHandleProps && (
          <div className="flex items-center space-x-2 text-gray-400">
            <div {...dragHandleProps.attributes} {...dragHandleProps.listeners}>
              <GripVertical className="h-4 w-4 cursor-move" />
            </div>
            <span className="text-xs">Drag to reorder</span>
          </div>
        )}
        <div className="flex items-center space-x-2 ml-auto">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => onEditRule(rule.id)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
            onClick={() => onDeleteRule(rule.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6 pt-6">
      <div className="flex flex-col gap-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold">{serviceName}</h1>
            <p className="text-sm text-gray-600">Set Priority for [Service Name]</p>
          </div>
          <Button
            onClick={onAddPreference}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Preference
          </Button>
        </div>

        {/* Rules List */}
        {filteredRules.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">
              {searchTerm ? 'No rules found matching your search.' : 'No rules added for this service yet.'}
            </div>
            {!searchTerm && (
              <Button
                onClick={onAddPreference}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add First Preference
              </Button>
            )}
          </div>
        ) : sortable ? (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={filteredRules.map(rule => rule.id)}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-4">
                {filteredRules.map((rule, index) => (
                  <SortableRule key={rule.id} rule={rule} index={index} />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        ) : (
          <div className="space-y-4">
            {filteredRules.map((rule, index) => (
              <div key={rule.id}>
                {renderRule(rule, index)}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};