import { useEffect, useState } from "react";
import {
	Search,
	Filter,
	Plus,
	MapPin,
	Settings,
	Settings2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";
import { LocationCard } from "./LocationCard";
import { AddLocationSheet } from "./sheets";
import { useLocations } from "../hooks/useLocations";
import type {
	LocationsFilters,
	LocationsResponse,
	CreateLocationRequest,
	Location,
} from "../types";
import LocationDetailsSheet from "./sheets/location-details/LocationDetailsSheet";
import { SendBookingLinkSheet } from "@/features/schedule";
import { useNavigate } from "react-router";
import { locationsApi } from "../api/locationsApi";
import { toast } from "sonner";
import { useOrganizationContext } from "@/features/organizations/context";

interface LocationTabProps {
	className?: string;
	onRefetchReady?: (refetchFn: () => void) => void;
}

export function LocationTab({ className, onRefetchReady }: LocationTabProps) {
	const navigate = useNavigate();
	const organization = useOrganizationContext();
	console.log(organization);
	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});
	const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showAddLocationForm, setShowAddLocationForm] = useState(false);
	const [showLocationDetails, setShowLocationDetails] = useState(false);
	const [selectedLocation, setSelectedLocation] = useState<Location | null>(
		null
	);
	const [showSendBookingLinkSheet, setShowSendBookingLinkSheet] =
		useState(false);

	const {
		data: locationsData2,
		isLoading,
		refetch,
	} = useLocations(
		{
			page: currentPage,
			search: searchTerm,
			limit: 10,
		},
		organization?.organizationId!
	);

	// Debounced search filter
	useEffect(() => {
		const timer = setTimeout(() => {
			setFilters((prev) => ({
				...prev,
				search: searchTerm || undefined,
				page: 1,
			}));
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// Pass refetch function to parent
	useEffect(() => {
		if (onRefetchReady && refetch) {
			onRefetchReady(refetch);
		}
	}, [onRefetchReady, refetch]);

	// Use real API data instead of mock data
	const locationsData = locationsData2 || [];

	const handleFilterChange = (newFilters: Partial<LocationsFilters>) => {
		setFilters((prev) => ({ ...prev, ...newFilters, page: 1 }));
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked && locationsData?.length) {
			setSelectedLocations(locationsData.map((location) => location.id));
		} else {
			setSelectedLocations([]);
		}
	};

	const handleLocationSelection = (locationId: string, selected: boolean) => {
		if (selected) {
			setSelectedLocations((prev) => [...prev, locationId]);
		} else {
			setSelectedLocations((prev) =>
				prev.filter((id) => id !== locationId)
			);
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleAddLocation = async (
		data: CreateLocationRequest & { images?: File[] }
	) => {
		try {
			await locationsApi.createLocation(
				data,
				organization.organizationId!
			);
			refetch();
			toast.success("Location created successfully");
			// Optionally refresh locations list here if you have a refetch function
		} catch (error: any) {
			toast.error(error?.message || "Failed to create location");
		}
		setShowAddLocationForm(false);
	};

	const handleViewLocation = (location: Location) => {
		navigate(`/dashboard/workplace/providers/${location.id}`);
		// setSelectedLocation(location);
		// setShowLocationDetails(true);
	};

	console.log(locationsData2);
	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-1 pl-4">
				<Checkbox
					label="Select All"
					checked={
						selectedLocations.length === locationsData?.length &&
						locationsData?.length > 0
					}
					className="border-primary"
					onCheckedChange={handleSelectAll}
				/>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search locations..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className="cursor-pointer"
						size="icon"
						onClick={() => setShowAddLocationForm(true)}
					>
						<Settings2 className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						className="cursor-pointer"
						onClick={() => setShowAddLocationForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add Location
					</Button>
				</div>
			</div>

			{/* Loading State */}
			{isLoading && (
				<div className="grid grid-cols-1 gap-6">
					{[...Array(6)].map((_, i) => (
						<div key={i} className="animate-pulse">
							<div className="h-20 rounded-lg bg-gray-200"></div>
						</div>
					))}
				</div>
			)}

			{/* Locations Grid */}
			{!isLoading && (
				<>
					{locationsData?.length === 0 ? (
						<div className="py-12 text-center">
							<MapPin className="mx-auto h-12 w-12 text-gray-400" />
							<h3 className="mt-2 text-sm font-medium text-gray-900">
								No locations found
							</h3>
							<p className="mt-1 text-sm text-gray-500">
								Get started by creating your first location.
							</p>
							<Button
								className="mt-4"
								onClick={() => setShowAddLocationForm(true)}
							>
								<Plus className="mr-2 h-4 w-4" />
								Add Location
							</Button>
						</div>
					) : (
						<div className="flex flex-col gap-0.5">
							{locationsData?.map((location) => (
								<LocationCard
									key={location.id}
									location={location}
									isSelected={selectedLocations.includes(
										location.id
									)}
									onSelectionChange={(selected) =>
										handleLocationSelection(
											location.id,
											selected
										)
									}
									onEdit={() =>
										console.log(
											"Edit location:",
											location.id
										)
									}
									onView={() => handleViewLocation(location)}
								/>
							))}
						</div>
					)}

					{/* Pagination removed - using simple array data */}
				</>
			)}

			{/* Add Location Sheet */}
			<AddLocationSheet
				open={showAddLocationForm}
				onOpenChange={setShowAddLocationForm}
				onSubmit={handleAddLocation}
			/>

			{/* Location Details Sheet */}
			<LocationDetailsSheet
				open={showLocationDetails}
				onClose={() => setShowLocationDetails(false)}
				onSendBookingLink={() => setShowSendBookingLinkSheet(true)}
			/>

			{/* Send Booking Link Sheet */}
			<SendBookingLinkSheet
				open={showSendBookingLinkSheet}
				onOpenChange={setShowSendBookingLinkSheet}
			/>
		</div>
	);
}
