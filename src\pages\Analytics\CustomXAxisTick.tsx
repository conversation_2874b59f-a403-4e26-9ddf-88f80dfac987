import React from "react";

const CustomXAxisTick: React.FC<any> = ({ x, y, payload }) => {
	const truncatedText =
		payload.value.length > 10
			? `${payload.value.substring(0, 10)}...`
			: payload.value;

	return (
		<g transform={`translate(${x},${y})`}>
			<title>{payload.value}</title>
			<text
				x={0}
				y={0}
				dy={16}
				textAnchor="middle"
				fill="#666"
				className="text-xs capitalize"
			>
				{truncatedText}
			</text>
		</g>
	);
};

export default CustomXAxisTick;
