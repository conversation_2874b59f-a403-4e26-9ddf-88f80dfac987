import React from "react";
import type { FormField } from "../../types/Form";
import { Uploader } from "@/components/common/Uploader";
import { Upload, Download } from "lucide-react";

type AttachmentFieldRendererProps = {
	field: FormField;
	value: any;
	mode: "preview" | "view" | "edit";
	onChange?: (value: any) => void;
};

const formatMap: Record<string, string> = {
	"Word Doc": ".doc,.docx",
	PNG: ".png",
	PDF: ".pdf",
	CSV: ".csv",
	JPEG: ".jpeg",
};

const getAcceptedFormats = (field: FormField) => {
	if (!field.approved_formats || field.approved_formats.length === 0)
		return "";
	return field.approved_formats
		.map((format) => formatMap[format] || `.${format.toLowerCase()}`)
		.join(",");
};

export const AttachmentFieldRenderer: React.FC<
	AttachmentFieldRendererProps
> = ({ field, value, mode, onChange }) => {
	if (mode === "view") {
		return (
			<div className="flex items-center gap-2">
				{value ? (
					<div className="flex items-center gap-2">
						<Download className="h-4 w-4" />
						<a
							href={value}
							target="_blank"
							rel="noopener noreferrer"
						>
							{value}
						</a>
					</div>
				) : (
					<span className="text-slate-400">No response</span>
				)}
			</div>
		);
	}
	return (
		<div className="mx-auto max-w-[480px]">
			<Uploader
				files={[]}
				onFilesChange={() => {}}
				onFileRemove={() => {
					onChange && onChange("");
				}}
				descriptionText={`Recommended file type: ${getAcceptedFormats(
					field
				)} (Max of 10 mb)`}
				accept={getAcceptedFormats(field)}
				maxFileSize={10 * 1024 * 1024}
				multiple={false}
				maxFiles={1}
				size="sm"
				uploadText="Click or drag file here to upload file"
				uploadIcon={<Upload className="h-4 w-4 text-black" />}
				enableServerUpload={true}
				onUploadSuccess={(_, url: string) => {
					onChange && onChange(url);
				}}
			/>
		</div>
	);
};
