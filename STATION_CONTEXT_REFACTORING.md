# Station Context Refactoring

## Summary

This refactoring replaces prop drilling with a React Context pattern for managing station operations throughout the application.

## Changes Made

### 1. Created StationContext (`src/features/locations/context/StationContext.tsx`)

- Centralized station data management
- Handles API calls and state management
- Provides stations data, loading states, and CRUD operations
- Automatically handles refetching after mutations

### 2. Created useStationOperations Hook (`src/features/locations/hooks/useStationOperations.ts`)

- Simplified interface for station operations
- Provides `addStation`, `addStationOnly`, and `refetchStations` methods
- Abstracts complex context logic

### 3. Updated Components

#### `LocationProviders.tsx`

- Wrapped with `StationProvider`
- Removed prop drilling
- Simplified station submission logic

#### `LocationProvidersList.tsx`

- Removed `locationId`, `organizationId`, `onProviderCreated`, `onRefetchReady` props
- Now only needs `onAddStation` callback

#### `LocationProvidersTab.tsx`

- Uses `useStationContext()` instead of manual API calls
- Removed prop dependencies
- Automatic refetching through context

#### `AddStationSheet.tsx`

- Uses `useStationOperations()` for simplified station creation
- Fallback to `onSubmit` prop for backward compatibility

## Usage

### Basic Setup

```tsx
// Wrap your component tree with StationProvider
<StationProvider locationId={locationId} organizationId={organizationId}>
	<YourComponents />
</StationProvider>
```

### Using the Hook

```tsx
import { useStationOperations } from "@/features/locations/hooks";

function YourComponent() {
	const { stations, isLoading, addStation, addStationOnly, refetchStations } =
		useStationOperations();

	const handleAddStation = async () => {
		await addStation({
			name: "New Station",
			description: "Station description",
			service_provider_first_name: "John",
			service_provider_last_name: "Doe",
			service_provider_email: "<EMAIL>",
			service_provider_phone: "+**********",
		});
		// Automatic refetch happens via context
	};

	return (
		<div>
			{stations.map((station) => (
				<div key={station.id}>{station.name}</div>
			))}
			<button onClick={handleAddStation}>Add Station</button>
		</div>
	);
}
```

## Benefits

1. **Eliminated Prop Drilling**: No more passing callbacks through multiple component layers
2. **Centralized State**: Single source of truth for station data
3. **Automatic Refetching**: Context automatically handles data refetching after mutations
4. **Simplified API**: `useStationOperations` provides a clean interface
5. **Type Safety**: Full TypeScript support with proper error handling
6. **Backward Compatibility**: Components still accept fallback props

## Migration Guide

If you have existing components using the old prop-based approach:

1. Wrap your component tree with `<StationProvider>`
2. Replace `useStations` hook with `useStationOperations`
3. Remove prop drilling of callbacks
4. Use context methods instead of manual API calls

The refactoring maintains backward compatibility, so existing code will continue to work while you migrate to the new pattern.
