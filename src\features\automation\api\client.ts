import { apiClient } from "@/lib/api/clients"

export type ClientStatusData = {
    success: boolean,
    data: {
        id: string,
        name: string
    }[],
}
export const clientStatus = {
    getClientStatus: async (organizationId: number): Promise<ClientStatusData> => {
        const response = await apiClient.get(`/api/v1/clients/statuses`, {
            headers: {
                "X-organizationId": organizationId,
            }
        })
        return response.data
    }
}