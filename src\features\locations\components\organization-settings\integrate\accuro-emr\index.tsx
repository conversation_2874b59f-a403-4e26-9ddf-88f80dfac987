import { useState } from "react";
import accuroEHRIcon from "../../images/acurro.png";
import { Button } from "@/components/ui/Button/Button";
import { cn } from "@/lib/utils";
import IntegrateAccuroEHRStep1 from "./step-1";
import IntegrateAccuroEHRStep2 from "./step-2";
import IntegrateAccuroEHRStep3 from "./step-3";
import IntegrateAccuroEHRConnected from "./connected";
import IntegrateAccuroEHRDisconnected from "./disconnected";

export default function IntegrateAccuroEHR() {
    const [step, setStep] = useState(1);
    const [connected, setConnected] = useState(false);
    const [disconnected, setDisconnected] = useState(true);

    return (
        <div>
            <div className="flex items-center gap-2 border-b pb-3">
                <img src={accuroEHRIcon} alt="Accuro EMR" className="size-12" />
                <h2 className="text-base font-medium">Accuro EMR</h2>
            </div>

            {!connected ? (
                <>
                    <div className="mt-6 w-full grid grid-cols-[repeat(2,1fr)_auto] gap-3">
                        {Array.from({ length: 3 }).map((_, index) => (
                            <div key={index} className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className={cn("border-current rounded-full size-7 text-sm transition-all duration-300 cursor-pointer", step === index + 1 ? "text-[#005893] hover:border-[#005893] hover:text-[#005893]" : "text-[#000] border-[#E4E4E7] hover:border-[#E4E4E7] hover:text-[#000]")}
                                    onClick={() => {
                                        if (index === 2) {
                                            setConnected(true)
                                        } else {
                                            setStep(index + 1)
                                        }
                                    }}
                                >
                                    {index + 1}
                                </Button>
                                <div className="bg-[#E4E4E7] w-full h-[1px]"></div>
                            </div>
                        ))}
                    </div>
                    <div className="mt-6">
                        {step === 1 && <IntegrateAccuroEHRStep1 />}
                        {step === 2 && <IntegrateAccuroEHRStep2 />}
                        {step === 3 && <IntegrateAccuroEHRStep3 />}
                    </div></>
            ) : (
                disconnected ? (
                    <IntegrateAccuroEHRDisconnected
                        setConnected={() => {
                            setConnected(true)
                            setDisconnected(false)
                        }}
                    />
                ) : (
                    <IntegrateAccuroEHRConnected
                        setDisconnected={() => {
                            setDisconnected(true)
                        }}
                    />
                )
            )}
        </div>
    )
}