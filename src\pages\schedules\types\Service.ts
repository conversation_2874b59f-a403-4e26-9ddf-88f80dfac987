export type GetServicesParams = {
    search?: string;
    location?: string;
    station?: string;
    roles?: string;
    sort_by?: string;
    from?: string;
    to?: string;
    is_available?: boolean;
    is_visible?: boolean;
    location_id?: string;
    station_id?: string;
    methods?: string;
    status?: string;
    basic?: boolean;
    include?: string;
}

export type GetServicesResponse = {
    success: true,
    message: string,
    data:
    {
        id: number,
        name: string,
        description: string | null,
        business_id: number,
        time_in_minute: number,
        appointment_methods: {
            id: number,
            name: string
        }[],
        is_available: boolean,
        auto_approve: boolean,
        is_visible: boolean,
        apply_to_all_locations: boolean,
        location_selections: {
            location_id: number,
            location_name: string,
            all_stations: boolean,
            station_ids: number[],
            stations: {
                id: number,
                name: string
            }[],
        }[],
        total_locations: number,
        total_stations: number
    }[],
}