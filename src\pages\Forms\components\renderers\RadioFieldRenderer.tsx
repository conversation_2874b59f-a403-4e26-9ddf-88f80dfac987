import React from "react";
import type { FormField } from "../../types/Form";

type RadioFieldRendererProps = {
	field: FormField;
	value: any;
	mode: "preview" | "view" | "edit";
	onChange?: (value: any) => void;
};

export const RadioFieldRenderer: React.FC<RadioFieldRendererProps> = ({
	field,
	value,
	mode,
	onChange,
}) => {
	if (mode === "view") {
		const selected = field.options?.find((opt) => opt.id === value)
			?.value || <span className="text-slate-400">No response</span>;
		return <div>{selected}</div>;
	}
	return (
		<div className="ml-3 flex flex-col gap-2.5">
			{field.options?.map((opt) => (
				<label
					key={opt.id}
					className="flex items-center gap-2 text-xs font-medium text-[#27272A]"
				>
					<input
						type="radio"
						className="border-primary checked:bg-primary h-3 w-3 appearance-none rounded-full border-1"
						name={field.id}
						value={opt.id}
						checked={value === opt.id}
						disabled={mode !== "edit"}
						onChange={
							mode === "edit" && onChange
								? () => onChange(opt.id)
								: undefined
						}
					/>
					{opt.value}
				</label>
			))}
		</div>
	);
};
