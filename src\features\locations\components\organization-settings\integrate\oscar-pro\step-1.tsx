import { Button } from "@/components/ui/Button/Button";
import { Input } from "@/components/ui/input";

export default function IntegrateOscarProStep1() {
    return (
        <div>
            <h1 className="text-[#09090B] font-medium text-lg mb-3">Step 1</h1>
            <div className="bg-[#F4F4F5] py-5 px-6 rounded-xl">
                <h1 className="text-[#27272A] font-medium text-base">Sign in to your EMR!</h1>
                <p className="tet-[#71717A] font-light text-sm opacity-80 mt-1">Go to your EMR website, paste the login link here, and then sign into your account!</p>
                <div className="mt-6">
                    <h1 className="text-[#27272A] font-normal text-sm mb-1">Paste Below</h1>
                    <Input
                        type="url"
                        placeholder="Enter the URL here"
                        className="bg-white py-5"
                    />
                    <div className="flex items-center justify-end mt-3">
                        <Button className="w-[8rem]">
                            Connect
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
}