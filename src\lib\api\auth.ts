import type { AxiosResponse } from "axios";
import { apiClient } from "./clients";
import type * as Auth from "@/types/api/auth";

export const registerUser = async (
	data: Auth.RegisterRequest
): Promise<Auth.RegisterResponse> => {
	const response = await apiClient.post("/api/v1/auth/register", data);
	return response.data;
};

export const loginUser = async (
	data: Auth.LoginRequest
): Promise<Auth.LoginResponse> => {
	const response = await apiClient.post("/api/v1/auth/login", data);
	return response.data;
};

export const loginTemp = async (
	data: Auth.LoginTempRequest
): Promise<Auth.LoginTempResponse> => {
	const response = await apiClient.post("/api/v1/auth/login/temp", data);
	return response.data;
};

export const refreshToken = async (): Promise<Auth.RefreshTokenResponse> => {
	const response = await apiClient.post("/api/v1/auth/refresh");
	return response.data;
};

export const enable2FA = async (): Promise<Auth.TwoFAEnableResponse> => {
	const response = await apiClient.get("/api/v1/auth/2fa/enable");
	return response.data;
};

// 2FA Confirm
export const confirm2FA = async (
	data: Auth.TwoFAConfirmRequest
): Promise<Auth.TwoFAConfirmResponse> => {
	const response = await apiClient.post("/api/v1/auth/2fa/confirm", data);
	return response.data;
};

// 2FA Verify
export const verify2FA = async (
	data: Auth.TwoFAVerifyRequest
): Promise<Auth.TwoFAVerifyResponse> => {
	const response = await apiClient.post("/api/v1/auth/2fa/verify", data);
	return response.data;
};

// 2FA Email OTP
export const send2FAEmailOTP = async (
	data: Auth.TwoFAEmailOTPRequest
): Promise<Auth.TwoFAEmailOTPResponse> => {
	const response = await apiClient.post("/api/v1/auth/2fa/code/email", data);
	return response.data;
};

export const disable2FA = async (): Promise<Auth.TwoFADisableResponse> => {
	const response = await apiClient.post("/api/v1/auth/2fa/disable");
	return response.data;
};

export const get2FAStatus = async (): Promise<Auth.TwoFAStatusResponse> => {
	const response = await apiClient.get("/api/v1/auth/2fa/status");
	return response.data;
};

export const skip2FA = async (): Promise<Auth.TwoFASkipResponse> => {
	const response = await apiClient.post("/api/v1/auth/2fa/skip");
	return response.data;
};

export const reconfigure2FA =
	async (): Promise<Auth.TwoFAReconfigureResponse> => {
		const response = await apiClient.post("/api/v1/auth/2fa/reconfigure");
		return response.data;
	};

export const forgotPassword = async (
	data: Auth.ForgotPasswordRequest
): Promise<Auth.ForgotPasswordResponse> => {
	const response = await apiClient.post("/api/v1/auth/forgot-password", data);
	return response.data;
};

export const resetPassword = async (
	data: Auth.ResetPasswordRequest
): Promise<Auth.ResetPasswordResponse> => {
	const response = await apiClient.post("/api/v1/auth/reset-password", data);
	return response.data;
};

export const changePassword = async (
	data: Auth.ChangePasswordRequest
): Promise<Auth.ChangePasswordResponse> => {
	const response = await apiClient.put("/api/v1/profile/password", data);
	return response.data;
};

export const getProfile = async (): Promise<Auth.ProfileResponse> => {
	const response = await apiClient.get("/api/v1/profile");
	return response.data;
};

export const updateProfile = async (
	data: Auth.UpdateProfileRequest
): Promise<Auth.UpdateProfileResponse> => {
	const response = await apiClient.put("/api/v1/profile", data);
	return response.data;
};

export const logoutUser = async (): Promise<Auth.LogoutResponse> => {
	const response = await apiClient.put("/api/v1/auth/logout");
	return response.data;
};

export const googleLogin = async (data: { token: string }): Promise<any> => {
	// TODO: Type this endpoint properly once backend contract is confirmed
	const response = await apiClient.post("/sign-in-with-google", data);
	return response.data;
};

export const microsoftLogin = async (data: { token: string }): Promise<any> => {
	const response = await apiClient.post("/sign-in-with-microsoft", data);
	return response.data;
};

export const APIVersion1SendVerifyEmail = async (data: {
	email: string;
}): Promise<AxiosResponse<Record<string, string>>> =>
	apiClient.post("/user/send-verification", data).then((res) => res.data);

export const APIVersion1VerifySSODomain = async (
	domain?: string
): Promise<any> => {
	const params = new URLSearchParams();
	if (domain) {
		params.append("domain", domain);
	}

	return apiClient
		.get(
			`/api/v1/sso/verify${params.toString() ? `?${params.toString()}` : ""}`
		)
		.then((res) => res.data);
};

export const APIVersion1VerifyTenant = async (
	domain?: string
): Promise<any> => {
	return apiClient
		.get(`/api/v1/verify`, {
			headers: {
				"request-domain": domain,
			},
		})
		.then((res) => res.data);
};

export const APIVersion1SSOUserVerify = async (token: string) => {
	return apiClient
		.get(`/api/v1/sso-user-verify?token=${token}`)
		.then((res) => res.data);
};

// New SSO Configuration API functions
export const APIVersion1GetSSOConfig = async (organizationId: number): Promise<import('../../types/auth').SSOConfigResponse> => {
	return apiClient
		.get('/api/v1/sso', {
			headers: {
				'X-organizationId': organizationId.toString(),
			},
		})
		.then((res) => res.data);
};

export const APIVersion1EnableSSO = async (
	ssoConfig: import('../../types/auth').SSOEnableRequest,
	organizationId: number
): Promise<import('../../types/auth').SSOEnableResponse> => {
	return apiClient
		.post('/api/v1/sso/enable', ssoConfig, {
			headers: {
				'X-organizationId': organizationId.toString(),
			},
		})
		.then((res) => res.data);
};

export const APIVersion1UpdateSSO = async (
	ssoConfig: import('../../types/auth').SSOEnableRequest,
	organizationId: number
): Promise<import('../../types/auth').SSOEnableResponse> => {
	return apiClient
		.put('/api/v1/sso/update', ssoConfig, {
			headers: {
				'X-organizationId': organizationId.toString(),
			},
		})
		.then((res) => res.data);
};

export const APIVersion1DeleteSSO = async (
	organizationId: number
): Promise<import('../../types/auth').SSOEnableResponse> => {
	return apiClient
		.delete('/api/v1/sso', {
			headers: {
				'X-organizationId': organizationId.toString(),
			},
		})
		.then((res) => res.data);
};

// // Auth Callback Token Verification
// export const verifyCallbackToken = async (data: { token: string }): Promise<any> => {
// 	const response = await apiClient.post("/api/v1/auth/callback/verify", data);
// 	return response.data;
// };
