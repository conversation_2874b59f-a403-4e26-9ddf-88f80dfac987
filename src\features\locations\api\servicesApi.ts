import { apiClient } from "@/lib/api/clients";

// Service data interfaces
export interface ServiceData {
	id: number;
	name: string;
	description: string;
	business_id: number;
	time_in_minute: number;
	is_available: boolean;
	auto_approve: boolean;
	is_visible: boolean;
	appointment_methods: Array<{
		id: number;
		name: string;
	}>;
	location_selections?: Array<{
		location_id: number;
		location_name: string;
		all_stations: boolean;
		station_ids: number[];
		stations: Array<{
			id: number;
			name: string;
		}>;
	}>;
	forms?: Array<{
		id: number;
		name: string;
		description: string;
		status: string;
	}>;
	locations?: Array<{
		id: number;
		name: string;
		address: string;
	}>;
	stations?: Array<{
		id: number;
		name: string;
		description: string;
	}>;
}

export interface GetServicesResponse {
	success: boolean;
	message: string;
	data: ServiceData[];
}

// Frontend form data interface
export interface CreateServiceRequest {
	serviceName: string;
	description?: string;
	autoApprove: boolean;
	serviceVisibility: boolean;
	serviceAvailability: boolean;
	availableMethods: string[];
	serviceDuration: number;
	durationUnit: "minutes" | "hours";
	locationId?: string;
	applyServiceTo: "all-locations" | "selected-locations";
	selectedLocations?: string[];
}

// Backend API payload interface
interface LocationSelection {
	location_id: number;
	all_stations: boolean;
	station_ids: number[];
}

interface CreateServiceApiPayload {
	name: string;
	description?: string;
	time_in_minute: number;
	appointment_methods: number[];
	is_available: boolean;
	auto_approve: boolean;
	is_visible: boolean;
	apply_to_all_locations: boolean;
	location_selections: LocationSelection[];
}

export interface CreateServiceResponse {
	success: boolean;
	message: string;
	data: {
		id: number;
		serviceName: string;
		description: string;
		autoApprove: boolean;
		serviceVisibility: boolean;
		serviceAvailability: boolean;
		availableMethods: string[];
		serviceDuration: number;
		durationUnit: string;
		locationId?: string;
		isActive: boolean;
		createdAt: string;
		updatedAt: string;
	};
}

export const servicesApi = {
	// Get services for a specific location
	getLocationServices: async (
		locationId: string,
		orgId: number
	): Promise<GetServicesResponse> => {
		const response = await apiClient.get(`/api/v1/locations/${locationId}/services`, {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data;
	},

	createService: async (
		data: CreateServiceRequest,
		orgId: number,
		selectedLocationIds: Set<string> = new Set(),
		selectedStationIds: Set<string> = new Set(),
		locationStationMap: Map<string, Set<string>> = new Map()
	): Promise<CreateServiceResponse> => {
		// Convert duration to minutes if necessary
		const timeInMinute =
			data.durationUnit === "hours"
				? data.serviceDuration * 60
				: data.serviceDuration;

		// Build location selections based on the new schema
		const locationSelections: LocationSelection[] = [];

		// If applying to all locations, use the applyServiceTo field
		const applyToAllLocations = data.applyServiceTo === "all-locations";

		if (!applyToAllLocations) {
			// Build location selections for each selected location
			selectedLocationIds.forEach((locationId) => {
				const locationIdNum = parseInt(locationId);
				const stationsForLocation =
					locationStationMap.get(locationId) || new Set();
				const stationIds = Array.from(stationsForLocation).map((id) =>
					parseInt(id)
				);

				locationSelections.push({
					location_id: locationIdNum,
					all_stations: stationIds.length === 0, // If no specific stations selected, use all
					station_ids: stationIds,
				});
			});
		}

		// Transform frontend data to backend API format
		const apiPayload: CreateServiceApiPayload = {
			name: data.serviceName,
			description: data.description || "",
			time_in_minute: timeInMinute,
			appointment_methods: data.availableMethods.map((id) =>
				parseInt(id)
			),
			is_available: data.serviceAvailability,
			auto_approve: data.autoApprove,
			is_visible: data.serviceVisibility,
			apply_to_all_locations: applyToAllLocations,
			location_selections: locationSelections,
		};

		const response = await apiClient.post("/api/v1/services", apiPayload, {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data;
	},

	// Get services for an organization
	getServices: async (
		orgId: number,
		filters: Record<string, any> = {}
	): Promise<GetServicesResponse> => {
		// Build query parameters from filters
		const params = new URLSearchParams(filters);

		const queryString = params.toString();
		const url = queryString
			? `/api/v1/services?${queryString}`
			: "/api/v1/services";

		const response = await apiClient.get(url, {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data;
	},

	// Get a single service by ID
	getService: async (
		serviceId: number,
		orgId: number,
		options: {
			basic?: boolean;
			include?: string[];
		} = {}
	): Promise<{ success: boolean; message: string; data: ServiceData }> => {
		const params = new URLSearchParams();

		if (options.basic !== undefined) {
			params.append("basic", options.basic.toString());
		}

		if (options.include && options.include.length > 0) {
			params.append("include", options.include.join(","));
		}

		const queryString = params.toString();
		const url = queryString
			? `/api/v1/services/${serviceId}?${queryString}`
			: `/api/v1/services/${serviceId}`;

		const response = await apiClient.get(url, {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data;
	},

	// Update a service
	updateService: async (
		id: string,
		data: Partial<CreateServiceRequest>,
		orgId: number
	): Promise<CreateServiceResponse> => {
		const response = await apiClient.put(`/api/v1/services/${id}`, data, {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data;
	},

	// Delete a service
	deleteService: async (id: string, orgId: number): Promise<void> => {
		await apiClient.delete(`/api/v1/services/${id}`, {
			headers: {
				"X-organizationId": orgId,
			},
		});
	},
};
