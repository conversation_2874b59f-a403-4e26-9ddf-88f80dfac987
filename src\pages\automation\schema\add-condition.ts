import { z } from "zod";

export type Condition = "Category" | "Date & Time" | "Registration" | "Location" | "Provider" | "Service" | "Appointment Status"

export const AddConditionSchema = z.object({
    condition: z.array(z.string()).min(1, { message: "Condition is required" }),
    // All fields optional by default, validation will be handled in the component
    category: z.array(z.string()).optional(),
    dateTime: z.object({
        before: z.date().optional(),
        after: z.date().optional()
    }).optional(),
    registration: z.enum(["registered", "not_registered", "all"]).optional(),
    location: z.array(z.string()).optional(),
    provider: z.array(z.string()).optional(),
    service: z.array(z.string()).optional(),
    appointmentStatus: z.array(z.string()).optional(),
    patientStatus: z.string().optional()
}).superRefine((data, ctx) => {
    // Custom validation based on selected conditions
    if (data.condition.includes("Category") && (!data.category || data.category.length === 0)) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Please select at least one category",
            path: ["category"]
        });
    }

    if (data.condition.includes("Patient Status") && (!data.patientStatus || data.patientStatus.length === 0)) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Please select at least one patient status",
            path: ["patientStatus"]
        });
    }
    
    if (data.condition.includes("Date & Time") && (!data.dateTime?.before && !data.dateTime?.after)) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Please select at least one date (before or after)",
            path: ["dateTime"]
        });
    }
    
    if (data.condition.includes("Registration") && !data.registration) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Please select a registration status",
            path: ["registration"]
        });
    }
    
    if (data.condition.includes("Location") && (!data.location || data.location.length === 0)) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Please select at least one location",
            path: ["location"]
        });
    }
    
    if (data.condition.includes("Provider") && (!data.provider || data.provider.length === 0)) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Please select at least one provider",
            path: ["provider"]
        });
    }
    
    if (data.condition.includes("Service") && (!data.service || data.service.length === 0)) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Please select at least one service",
            path: ["service"]
        });
    }
    
    if (data.condition.includes("Appointment Status") && (!data.appointmentStatus || data.appointmentStatus.length === 0)) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Please select at least one appointment status",
            path: ["appointmentStatus"]
        });
    }
});

export type AddConditionSchemaType = z.infer<typeof AddConditionSchema>;
