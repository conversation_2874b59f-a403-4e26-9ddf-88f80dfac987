import { useEffect, useState, type FC } from "react";
import { StationDetailsCard } from "@/features/locations";
import { LocationProviderDetailsList } from "@/features/provider-details";
import { useUIStore } from "@/stores/uiStore";
import type { StationDetails } from "@/features/locations/components/StationDetailsCard";
import { StationInformationSheet } from "@/features/locations/components/sheets/station-information";
import { SendBookingLinkSheet } from "@/features/schedule/components/SendBookingLinkSheet";
import { useParams } from "react-router";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useStation } from "@/features/locations/hooks/useStations";
import { Button } from "@/components/ui/button";
import { RefreshCcw } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

const LocationProviderDetails: FC = () => {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
	const setPageHeaderContent = useUIStore((state) => state.setPageHeaderContent);
	const { organizationId } = useOrganizationContext();
	const [openOrganizationDetailsSheet, setOpenOrganizationDetailsSheet] =
		useState(false);
	const [showSendBookingLinkSheet, setShowSendBookingLinkSheet] =
		useState(false);
	const [showStationDetails, setShowStationDetails] = useState(false);
	const { providerId } = useParams();

	// Use React Query hook for station data - providerId is actually a station ID
	const {
		data: stationResponse,
		isLoading: isLoadingStation,
		error: stationError,
		refetch: refetchStation,
	} = useStation({
		stationId: providerId,
		organizationId: organizationId || undefined,
		enabled: !!providerId && !!organizationId,
	});

	// Get the station data from the response
	const stationData = stationResponse?.data;

	// Set breadcrumbs when component mounts
	useEffect(() => {
		setBreadcrumbs([
			{
				label: "Workplace",
				href: "/dashboard/workplace",
			},
			{
				label: "Organizations",
				href: "/dashboard/workplace/locations",
			},
			{
				label: "Locations",
				href: `/dashboard/workplace/providers/${stationData?.locations?.[0]?.id || providerId}`,
			},
			{
				label: stationData?.name || "[Station Name]",
				isCurrentPage: true,
			},
		]);
		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs, providerId, stationData]);

	// Set page header with refresh functionality
	useEffect(() => {
		const headerContent = (
			<div className="flex flex-1 items-center justify-between">
				<h1 className="text-foreground text-2xl font-bold">
					{stationData?.name || "Provider Details"}
				</h1>
				<div className="flex items-center space-x-3">
					<Button
						variant="outline"
						size="icon"
						className="h-9 w-9"
						onClick={() => {
							// Trigger refetch of station data
							refetchStation();
						}}
						disabled={isLoadingStation}
					>
						<RefreshCcw className={`h-4 w-4 ${isLoadingStation ? 'animate-spin' : ''}`} />
					</Button>
				</div>
			</div>
		);

		setPageHeaderContent(headerContent);

		return () => {
			setPageHeaderContent(null);
		};
	}, [setPageHeaderContent, refetchStation, isLoadingStation, stationData]);

	const handleView = (station: StationDetails) => {
		console.log("View:", station.name);
		setOpenOrganizationDetailsSheet(true);
	};

	// Show loading state
	if (isLoadingStation) {
		return (
			<div className="flex flex-col gap-4 py-6">
				{/* StationDetailsCard Skeleton */}
				<div className="rounded-lg border border-gray-200 bg-white p-6">
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<div className="mb-2 flex items-center gap-3">
								<Skeleton className="h-8 w-56" />
								<Skeleton className="h-6 w-12 rounded-full" />
							</div>
							<Skeleton className="mb-4 h-4 w-80" />
							<div className="flex items-center gap-8">
								<div className="flex items-center gap-2">
									<Skeleton className="h-4 w-4" />
									<Skeleton className="h-4 w-24" />
								</div>
								<div className="flex items-center gap-2">
									<Skeleton className="h-4 w-4" />
									<Skeleton className="h-4 w-28" />
								</div>
								<div className="flex items-center gap-2">
									<Skeleton className="h-4 w-4" />
									<Skeleton className="h-4 w-20" />
								</div>
							</div>
						</div>
						<div className="flex items-center gap-2">
							<Skeleton className="h-9 w-16" />
							<Skeleton className="h-9 w-14" />
							<Skeleton className="h-9 w-18" />
						</div>
					</div>
				</div>

				{/* LocationProviderDetailsList Skeleton */}
				<div className="rounded-lg border border-gray-200 bg-white">
					<div className="space-y-3 p-6">
						{Array.from({ length: 4 }).map((_, index) => (
							<Skeleton key={index} className="h-16 w-full" />
						))}
					</div>
				</div>
			</div>
		);
	}

	// Show error state
	if (stationError || (!stationData && !isLoadingStation)) {
		return (
			<div className="flex flex-col gap-4 py-6">
				<div className="flex items-center justify-center py-8">
					<span className="text-sm text-red-500">
						{String(stationError) || "Station not found"}
					</span>
				</div>
			</div>
		);
	}

	return (
		<div className="flex flex-col gap-4 py-6">
			{stationData && (
				<StationDetailsCard
					station={{
						id: stationData.id.toString(),
						name: stationData.name,
						address: stationData.locations?.[0]?.name || "No address available",
						rating: 4, // TODO: Get from station data if available
						locationsCount: stationData.locations?.length || 0,
						providersCount: stationData.service_providers?.length || 0,
						averageWaitTime: "N/A", // TODO: Get from station data if available
					}}
					onView={handleView}
					onEdit={(station) => console.log("Edit:", station.name)}
					onDelete={(station) => console.log("Delete:", station.name)}
				/>
			)}
			<LocationProviderDetailsList />
			{/* Station Information Sheet */}
			<StationInformationSheet
				open={showStationDetails}
				onClose={() => setShowStationDetails(false)}
				onSendBookingLink={() => setShowSendBookingLinkSheet(true)}
			/>
			{/* Send Booking Link Sheet */}
			<SendBookingLinkSheet
				open={showSendBookingLinkSheet}
				onOpenChange={setShowSendBookingLinkSheet}
			/>
		</div>
	);
};

export default LocationProviderDetails;
