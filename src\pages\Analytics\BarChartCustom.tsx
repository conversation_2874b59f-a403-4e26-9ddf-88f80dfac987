import React, { useRef, useState } from "react";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	type ChartConfig,
	ChartContainer,
	ChartTooltip,
	ChartTooltipContent,
} from "@/components/ui/chart";
import { <PERSON>, <PERSON><PERSON><PERSON>, LabelList, ResponsiveContainer, XAxis } from "recharts";
import { Maximize2 } from "lucide-react";
import { Dialog, DialogContent, DialogHeader } from "@/components/ui/dialog";
import { UnExpandIcon } from "./UnexpandIcon";
import CustomLegend from "./CustomLegend";
import CustomXAxisTick from "./CustomXAxisTick";
import { cn } from "@/lib/utils";

const BarChartCustom: React.FC<{
	data: Record<string, string>[];
	config: ChartConfig;
	barKeys: string[];
	title: string;
	description: string;
	descriptionValue: number | string;
}> = ({ data = [], config, barKeys, title, description, descriptionValue }) => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const visibleBars = 5;
	const containerRef = useRef(null);

	const calculateStackTotal = (entry: any): number => {
		return barKeys.reduce((sum, key) => sum + (Number(entry[key]) || 0), 0);
	};

	const presentBarKeys = React.useMemo(() => {
		return barKeys.filter((key) =>
			data.some((entry) => {
				const value = Number(entry[key]);
				return !isNaN(value) && value > 0;
			})
		);
	}, [barKeys, data]);

	const firstValidKey = presentBarKeys[0];
	const lastValidKey = presentBarKeys[presentBarKeys.length - 1];

	const renderChart = () => {
		const needsScrolling = data.length > 6;
		const minBarWidth = 120;
		const chartWidth = needsScrolling ? Math.max(data.length * minBarWidth, 600) : '100%';

		return (
			<div className={`${needsScrolling ? "overflow-x-auto custom-scrollbar" : ""} w-full`}>
				{/* {needsScrolling && (
					<div className="mb-2 text-xs text-gray-500 text-center bg-gray-50 py-1 rounded">
						← Scroll to see more data →
					</div>
				)} */}
				<div style={{ width: needsScrolling ? chartWidth : '100%', minWidth: needsScrolling ? chartWidth : 'auto' }}>
					<ResponsiveContainer width="100%" height={400}>
						<BarChart
							data={data}
							margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
							barSize={50}
						>
							<XAxis
								dataKey="name"
								tickLine={false}
								tickMargin={10}
								axisLine={false}
								interval={0}
								tick={(props) => (
									<CustomXAxisTick
										{...props}
										dataLength={data.length}
									/>
								)}
							/>
							<ChartTooltip content={<ChartTooltipContent />} />
							{barKeys.map((key, index) => (
								<Bar
									key={key}
									dataKey={key}
									stackId="a"
									fill={config[key]?.color || "#000"}
									radius={[
										key === lastValidKey ? 4 : 0, // Top-left
										key === lastValidKey ? 4 : 0, // Top-right
										key === firstValidKey ? 4 : 0, // Bottom-right
										key === firstValidKey ? 4 : 0, // Bottom-left
									]}
								>
									{index === barKeys.length - 1 && (
										<LabelList
											dataKey={calculateStackTotal}
											position="top"
											content={({ value, x, width, y }: any) => {
												if (value > 0) {
													return (
														<text
															x={x + width / 2}
															y={y - 5}
															textAnchor="middle"
															fill="#596574"
															fontSize={12}
															fontWeight={500}
														>
															{value}
														</text>
													);
												}
												return null;
											}}
										/>
									)}
								</Bar>
							))}
						</BarChart>
					</ResponsiveContainer>
				</div>
			</div>
		);
	};

	return (
		<div>
			<Card className="h-full">
				<CardHeader className="relative" ref={containerRef}>
					<CardTitle className="text-base text-[#09090B]">
						{title}
					</CardTitle>
					<CardDescription className="text-xs text-[#596574]">
						{description}{" "}
						<span className="font-semibold text-[#09090B]">
							{descriptionValue}
						</span>
					</CardDescription>
					<div className="flex justify-end overflow-hidden">
						<CustomLegend
							containerRef={containerRef}
							items={Object.entries(config).map(
								([_, value]: any) => ({
									label: value.label,
									color: value.color,
								})
							)}
						/>
					</div>
					<button
						onClick={() => setIsModalOpen(true)}
						className="absolute right-[20px] top-[15px] text-gray-500 transition-colors hover:text-gray-700"
					>
						<Maximize2 className="h-4 w-4" />
					</button>
				</CardHeader>
				<CardContent className="min-h-[450px] overflow-hidden">
					{data.length ? (
						<ChartContainer
							config={config}
							className="h-[400px] w-full [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-transparent"
						>
							<div className="h-full w-full">
								{renderChart()}
							</div>
						</ChartContainer>
					) : (
						<p>No Data 🎉</p>
					)}
				</CardContent>
			</Card>

			<Dialog open={isModalOpen} onOpenChange={setIsModalOpen} >
				<DialogContent className="max-w-[90vw] overflow-hidden rounded-lg bg-white p-0 sm:h-[600px] lg:max-w-4xl" showCloseButton={false}>
					<div className="flex h-full flex-col">
						{/* Fixed Header Section */}
						<div className="sm:p-6">
							<DialogHeader className="relative">
								<CardTitle className="pr-8 text-base text-[#09090B]">
									{title}
								</CardTitle>
								<CardDescription className="text-xs text-[#596574]">
									{description}{" "}
									<span className="font-semibold text-[#09090B]">
										{descriptionValue}
									</span>
								</CardDescription>
								<div className="mt-4 flex justify-end">
									<CustomLegend
										containerRef={containerRef}
										isFullScreen={true}
										items={Object.entries(config).map(
											([_, value]: any) => ({
												label: value.label,
												color: value.color,
											})
										)}
									/>
								</div>
								<button
									onClick={() => setIsModalOpen(false)}
									className="absolute right-0 top-0 text-gray-500 hover:text-gray-700"
								>
									<UnExpandIcon />
								</button>
							</DialogHeader>
						</div>

						{/* Scrollable Chart Section */}
						<div className="flex-1 p-6">
							<ChartContainer config={config}>
								{renderChart()}
							</ChartContainer>
						</div>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
};

export default BarChartCustom;
