import React, { useState } from "react";
import { Search, Filter, Plus, Building2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";

interface OrganizationTabProps {
	className?: string;
}

interface Organization {
	id: string;
	name: string;
	type: string;
	status: "active" | "inactive";
	memberCount: number;
	location: string;
	theme: string;
	createdAt: string;
}

// Mock data for demonstration
const mockOrganizations: Organization[] = [
	{
		id: "1",
		name: "Healthcare Partners Inc.",
		type: "Medical Group",
		status: "active",
		memberCount: 45,
		location: "New York, NY",
		createdAt: "2024-01-15",
		theme: "",
	},
	{
		id: "2",
		name: "City Medical Center",
		type: "Hospital",
		status: "active",
		memberCount: 120,
		location: "Los Angeles, CA",
		createdAt: "2024-01-10",
		theme: "",
	},
	{
		id: "3",
		name: "Wellness Clinic Network",
		type: "Clinic Chain",
		status: "inactive",
		memberCount: 25,
		location: "Chicago, IL",
		createdAt: "2024-01-05",
		theme: "",
	},
];

export function OrganizationTab({ className }: OrganizationTabProps) {
	const [selectedOrganizations, setSelectedOrganizations] = useState<
		string[]
	>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [isLoading] = useState(false);
	const [showCreateForm, setShowCreateForm] = useState(false);

	// Filter organizations based on search term
	const filteredOrganizations = mockOrganizations.filter(
		(org) =>
			org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			org.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
			org.location.toLowerCase().includes(searchTerm.toLowerCase())
	);

	const handleSelectAll = (checked: boolean) => {
		if (checked) {
			setSelectedOrganizations(
				filteredOrganizations.map((org) => org.id)
			);
		} else {
			setSelectedOrganizations([]);
		}
	};

	const handleOrganizationSelection = (orgId: string, selected: boolean) => {
		if (selected) {
			setSelectedOrganizations((prev) => [...prev, orgId]);
		} else {
			setSelectedOrganizations((prev) =>
				prev.filter((id) => id !== orgId)
			);
		}
	};

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-1 pl-4">
				<Checkbox
					label="Select All"
					checked={
						selectedOrganizations.length ===
						filteredOrganizations.length &&
						filteredOrganizations.length > 0
					}
					className="border-primary"
					onCheckedChange={handleSelectAll}
				/>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search organizations..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className="cursor-pointer"
						size="icon"
						onClick={() => console.log("Filter organizations")}
					>
						<Filter className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						className="cursor-pointer"
						onClick={() => setShowCreateForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add Organization
					</Button>
				</div>
			</div>

			{/* Loading State */}
			{isLoading && (
				<div className="grid grid-cols-1 gap-4">
					{[...Array(6)].map((_, i) => (
						<div key={i} className="animate-pulse">
							<div className="h-24 rounded-lg bg-gray-200"></div>
						</div>
					))}
				</div>
			)}

			{/* Organizations List */}
			{!isLoading && (
				<>
					{filteredOrganizations.length === 0 ? (
						<div className="py-12 text-center">
							<Building2 className="mx-auto h-12 w-12 text-gray-400" />
							<h3 className="mt-2 text-sm font-medium text-gray-900">
								No organizations found
							</h3>
							<p className="mt-1 text-sm text-gray-500">
								Get started by creating your first organization.
							</p>
							<Button
								className="mt-4"
								onClick={() => setShowCreateForm(true)}
							>
								<Plus className="mr-2 h-4 w-4" />
								Add Organization
							</Button>
						</div>
					) : (
						<div className="flex flex-col gap-0.5">
							{filteredOrganizations.map((organization) => (
								<div
									key={organization.id}
									className="flex items-center justify-between rounded-lg border p-4 transition-colors hover:bg-gray-50"
								>
									<div className="flex items-center gap-3">
										<Checkbox
											checked={selectedOrganizations.includes(
												organization.id
											)}
											onCheckedChange={(checked) =>
												handleOrganizationSelection(
													organization.id,
													checked
												)
											}
										/>
										<Building2 className="h-8 w-8 text-gray-400" />
										<div>
											<h4 className="font-medium text-gray-900">
												{organization.name}
											</h4>
											<p className="text-sm text-gray-500">
												{organization.type} •{" "}
												{organization.location}
											</p>
										</div>
									</div>
									<div className="flex items-center gap-4">
										<div className="text-right">
											<p className="text-sm font-medium text-gray-900">
												{organization.memberCount}{" "}
												members
											</p>
											<p
												className={`text-xs ${organization.status ===
														"active"
														? "text-green-600"
														: "text-red-600"
													}`}
											>
												{organization.status}
											</p>
										</div>
										<Button
											variant="ghost"
											size="sm"
											onClick={() =>
												console.log(
													"View organization:",
													organization.id
												)
											}
										>
											View
										</Button>
									</div>
								</div>
							))}
						</div>
					)}
				</>
			)}

			{/* Create Organization Modal Placeholder */}
			{showCreateForm && (
				<div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
					<div className="mx-4 w-full max-w-md rounded-lg bg-white p-6">
						<h3 className="mb-4 text-lg font-semibold">
							Create Organization
						</h3>
						<p className="mb-4 text-gray-600">
							Organization creation form would go here.
						</p>
						<div className="flex justify-end gap-2">
							<Button
								variant="outline"
								onClick={() => setShowCreateForm(false)}
							>
								Cancel
							</Button>
							<Button onClick={() => setShowCreateForm(false)}>
								Create
							</Button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
