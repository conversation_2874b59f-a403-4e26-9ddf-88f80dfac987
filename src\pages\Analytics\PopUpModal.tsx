import React from "react";
import {
	Dialog,
	DialogContent,
	Di<PERSON>Header,
	DialogTitle,
} from "@/components/ui/dialog";
import { UnExpandIcon } from "./UnexpandIcon";
import TooltipButton from "./TooltipButton";
import StarRating from "./StarRating";
import InfoTooltip from "./InfoTooltip";
import { Info } from "lucide-react";

interface PopUpModalProps {
	title: string;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	modalOpenHandler: (isModalOpen: boolean) => void;
	children: React.ReactNode;
	participation: number;
	totalReviews: number;
	averageRating: number;
	tooltipContent: string;
}

const PopUpModal = ({
	title,
	open,
	onOpenChange,
	modalOpenHandler,
	children,
	participation,
	totalReviews,
	averageRating,
	tooltipContent,
}: PopUpModalProps) => {
	return (
		<>
			<Dialog open={open} onOpenChange={onOpenChange}>
				<DialogContent className="max-w-[90vw] max-h-[90vh] overflow-hidden" showCloseButton={false}>
					<button
						onClick={() => modalOpenHandler(false)}
						className="absolute right-6 top-6 text-gray-500 transition-colors hover:text-gray-700 z-10"
					>
						<UnExpandIcon />
					</button>
					<DialogHeader className="pr-12">
						<DialogTitle>{title}</DialogTitle>
						<div className="mt-4 flex flex-wrap items-center gap-4 text-gray-500">
							<div className="relative flex">
								<span className="mr-1 text-xs">
									Participation:{" "}
								</span>
								<InfoTooltip
									trigger={
										<Info className="h-[16px] w-[24px] rounded bg-[#f5f5f5] p-[2px] text-[#323539]" />
									}
								>
									{tooltipContent}
								</InfoTooltip>
							</div>
							<div className="relative ml-1 flex items-center gap-2">
								<span className="text-xs font-semibold text-black">
									{participation}%
								</span>
							</div>
							<div className="flex items-center gap-2">
								<span className="text-xs">Total Reviews: </span>
								<span className="text-xs font-semibold text-black">
									{totalReviews.toLocaleString()}
								</span>
							</div>
							<div className="flex items-center gap-2">
								<span className="text-xs">Average: </span>
								<span className="text-xs font-semibold text-black">
									{averageRating.toFixed(1)}
								</span>
								<div className="flex">
									<StarRating rating={averageRating} />
								</div>
							</div>
						</div>
					</DialogHeader>
					<div className="flex-1 overflow-y-auto overflow-x-hidden p-6">
						{children}
					</div>
				</DialogContent>
			</Dialog>
		</>
	);
};

export default PopUpModal;
