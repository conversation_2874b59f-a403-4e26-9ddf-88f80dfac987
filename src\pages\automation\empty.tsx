import { Button } from "@/components/ui/Button/Button";
import { useNavigate } from "react-router-dom";

export default function NoAutomation() {
    const navigate = useNavigate();
    return (
        <div className="h-full w-full min-h-[70vh] grid place-items-center">
            <div className="flex flex-col items-center gap-y-2">
                <svg width="30" height="25" viewBox="0 0 30 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M27.334 13.8333H21.3808L19.11 17.2396C18.8627 17.6105 18.4464 17.8333 18.0006 17.8333H12.6673C12.2215 17.8333 11.8052 17.6105 11.5579 17.2396L9.28709 13.8333H3.33396V20.5C3.33396 20.8536 3.47454 21.1927 3.72459 21.4427C3.97464 21.6928 4.31368 21.8333 4.6673 21.8333H26.0006C26.3542 21.8333 26.6933 21.6928 26.9433 21.4427C27.1934 21.1927 27.334 20.8536 27.334 20.5V13.8333ZM8.80271 3.17969C8.62003 3.20542 8.44372 3.26925 8.28579 3.36719C8.07526 3.49782 7.90523 3.68441 7.7949 3.90625L7.7923 3.91016L4.15948 11.1667H10.0006C10.4464 11.1667 10.8627 11.3895 11.11 11.7604L13.3808 15.1667H17.2871L19.5579 11.7604L19.6582 11.6289C19.9096 11.3376 20.2771 11.1667 20.6673 11.1667H26.5084L22.8756 3.91016L22.873 3.90625C22.7627 3.68441 22.5927 3.49782 22.3821 3.36719C22.2242 3.26925 22.0479 3.20542 21.8652 3.17969L21.6803 3.16667H8.98761L8.80271 3.17969ZM30.0006 20.5C30.0006 21.5609 29.5789 22.578 28.8288 23.3281C28.0786 24.0783 27.0615 24.5 26.0006 24.5H4.6673C3.60643 24.5 2.58932 24.0783 1.83917 23.3281C1.08903 22.578 0.667297 21.5609 0.667297 20.5V12.5C0.667297 12.293 0.715354 12.0888 0.807922 11.9036L5.40818 2.71615C5.73939 2.05136 6.24958 1.49171 6.88084 1.10026C7.51314 0.708227 8.24233 0.500447 8.98631 0.5H21.6816L21.959 0.510417C22.6058 0.555771 23.2336 0.7571 23.7871 1.10026C24.4182 1.49161 24.9272 2.05159 25.2584 2.71615H25.2597L29.86 11.9036L29.9212 12.0456C29.9739 12.1909 30.0006 12.3447 30.0006 12.5V20.5Z" fill="#27272A" />
                </svg>
                <h1 className="text-xl font-semibold text-[#27272A]">No automation added</h1>
                <p className="text-[#A1A1AA] text-sm">Get started by creating an automation first.</p>
                <Button className="py-5 cursor-pointer mt-3 w-36" onClick={() => navigate("/dashboard/automation/create")}>
                    Add New
                </Button>
            </div>
        </div>
    )
}