import type { FieldValues, Path, UseFormReturn } from "react-hook-form";

export type FormInputFieldProps<T extends FieldValues> = {
    label?: string | React.ReactNode;
    labelStyles?: string;
    name: Path<T>;
    id?: string;
    inputStyles?: string;
    placeholder?: string;
    pattern?: string;
    sideLabel?: string;
    error?: boolean;
    value?: string | number;
    minLength?: number;
    maxLength?: number;
    required?: boolean;
    inputType?: string;
    inputWithSide?: boolean;
    textarea?: boolean;
    disabled?: boolean;
    password?: boolean;
    inputMode?: InputMode;
    onChange?: (e: React.ChangeEvent<HTMLInputElement> | React.ChangeEvent<HTMLTextAreaElement>) => void;
    form: UseFormReturn<T>;
    floatingLabel?: boolean;
    formItemStyles?: string;
    defaultValue?: number | string;
    autoComplete?: string;
};

export type FormDatePickerFieldProps<T extends FieldValues> = {
    form: UseFormReturn<T>;
    name: Path<T>;
    label?: string;
    placeholder?: string;
    required?: boolean;
    disabled?: boolean;
    formItemStyles?: string;
    labelStyles?: string;
    className?: string;
};

export const InputMode = {
    Url: "url",
    Text: "text",
    Search: "search",
    Numeric: "numeric",
    None: "none",
    Tel: "tel",
    Email: "email",
    Decimal: "decimal",
} as const;

export type InputMode = typeof InputMode[keyof typeof InputMode];
