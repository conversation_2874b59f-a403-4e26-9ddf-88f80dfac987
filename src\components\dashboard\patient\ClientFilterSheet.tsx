import { useState } from "react";
import { X } from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { ToggleButton } from "@/components/common/ToggleButton";
import { MultiSelectDropdown } from "@/components/common/MultiSelectDropdown";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import type { DateRange } from "@/components/common/Datepicker/DatePicker";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useLocations } from "@/features/locations/hooks/useLocations";
import { useAllStations } from "@/features/locations/hooks/useStations";

export interface ClientFilterData {
	patientsFrom: string[];
	stations: string[];
	locations: string[];
	dateRange: DateRange | undefined;
}

interface ClientFilterSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onApplyFilters: (filters: ClientFilterData) => void;
	onResetFilters: () => void;
}

export function ClientFilterSheet({
	open,
	onOpenChange,
	onApplyFilters,
	onResetFilters,
}: ClientFilterSheetProps) {
	const { organizationId } = useOrganizationContext();

	const [patientsFrom, setPatientsFrom] = useState<string[]>(["Waitlist"]);
	const [selectedStations, setSelectedStations] = useState<string[]>(["all"]);
	const [selectedLocations, setSelectedLocations] = useState<string[]>([
		"all",
	]);
	const [dateRange, setDateRange] = useState<DateRange | undefined>(
		undefined
	);

	// Fetch real data from APIs
	const { data: locationsData, isLoading: isLoadingLocations } = useLocations(
		{},
		organizationId || undefined
	);

	const { data: stationsData, isLoading: isLoadingStations } = useAllStations(
		{
			organizationId: organizationId || undefined,
			enabled: !!organizationId,
		}
	);

	const patientsFromOptions = ["Waitlist", "Schedule"];

	// Transform API data to options format
	const locationOptions = [
		{ value: "all", label: "All" },
		...(locationsData?.map((location) => ({
			value: location.id.toString(),
			label: location.name,
		})) || []),
	];

	const stationOptions = [
		{ value: "all", label: "All" },
		...(stationsData?.data?.map((station) => ({
			value: station.id.toString(),
			label: station.name,
		})) || []),
	];

	const handleApply = () => {
		// Transform selected values to actual data for filtering
		const filters: ClientFilterData = {
			patientsFrom,
			stations:
				selectedStations.length === 1 &&
				selectedStations.includes("all")
					? [] // Only "all" selected - no filter needed
					: selectedStations.filter((id) => id !== "all"), // Remove "all" but keep specific stations
			locations:
				selectedLocations.length === 1 &&
				selectedLocations.includes("all")
					? [] // Only "all" selected - no filter needed
					: selectedLocations.filter((id) => id !== "all"), // Remove "all" but keep specific locations
			dateRange,
		};
		onApplyFilters(filters);
		onOpenChange(false);
	};

	const handleReset = () => {
		setPatientsFrom(["Waitlist"]);
		setSelectedStations(["all"]);
		setSelectedLocations(["all"]);
		setDateRange(undefined);
		onResetFilters();
	};

	const handleCancel = () => {
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-9 py-9 sm:w-[540px] sm:max-w-[525px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<SheetTitle className="flex items-center justify-between">
						<span>Filter Patients</span>
						<Button
							variant="ghost"
							size="icon"
							onClick={handleCancel}
							className="h-6 w-6"
						>
							<X className="h-4 w-4" />
						</Button>
					</SheetTitle>
					<p className="text-muted-foreground text-sm">
						Select options below to help filter your search
					</p>
				</SheetHeader>

				<div className="space-y-4 py-6">
					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Patients from
						</Label>
						<div className="flex flex-wrap gap-2">
							{patientsFromOptions.map((option) => (
								<ToggleButton
									key={option}
									label={option}
									isSelected={patientsFrom.includes(option)}
									onClick={() => {
										if (patientsFrom.includes(option)) {
											setPatientsFrom((prev) =>
												prev.filter(
													(item) => item !== option
												)
											);
										} else {
											setPatientsFrom((prev) => [
												...prev,
												option,
											]);
										}
									}}
									className="max-w-[120px] min-w-[154px]"
								/>
							))}
						</div>
					</div>

					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Stations
						</Label>
						<MultiSelectDropdown
							options={stationOptions}
							onValueChange={(values) => {
								setSelectedStations(values);
							}}
							value={selectedStations}
							placeholder={
								isLoadingStations
									? "Loading stations..."
									: "Select stations"
							}
							className="w-full"
							disabled={isLoadingStations}
						/>
					</div>

					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Locations
						</Label>
						<MultiSelectDropdown
							options={locationOptions}
							onValueChange={(values) => {
								setSelectedLocations(values);
							}}
							value={selectedLocations}
							placeholder={
								isLoadingLocations
									? "Loading locations..."
									: "Select locations"
							}
							className="w-full"
							disabled={isLoadingLocations}
						/>
					</div>

					<div className="space-y-2">
						<Label className="text-sm font-medium text-zinc-900">
							Select a Date or Range
						</Label>
						<DatePicker
							variant="range"
							value={dateRange}
							onChange={(range) =>
								setDateRange(range as DateRange)
							}
							placeholder="Pick a date"
							className="w-full"
						/>
					</div>
				</div>

				<SheetFooter className="flex-row justify-between">
					<Button
						variant="ghost"
						onClick={handleReset}
						className="text-muted-foreground hover:text-foreground"
					>
						Reset
					</Button>
					<div className="flex gap-3">
						<Button variant="outline" onClick={handleCancel}>
							Cancel
						</Button>
						<Button onClick={handleApply}>Apply</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
