import { useOrganizationContext } from "@/features/organizations/context"
import { useQuery } from "@tanstack/react-query"
import { checkApi, type CheckTypesData } from "../api/check"

export function useGetCheckTypes({
    business_id,
    enabled,
}: {
    business_id: string,
    enabled: boolean,
}) {
    const organizationId = useOrganizationContext()?.organizationId
    return useQuery<{
        success: boolean,
        data: CheckTypesData[],
    }
    >({
        queryKey: ["checks", organizationId, business_id],
        queryFn: () => {
            if (!business_id || !organizationId) {
                throw new Error("Business ID and Organization ID are required")
            }
            return checkApi.getChecks(organizationId, business_id)
        },
        enabled
    })
}