import React from "react";
import type { FormField } from "../../types/Form";
import { ChevronDown } from "lucide-react";

type DropdownFieldRendererProps = {
	field: FormField;
	value: any;
	mode: "preview" | "view" | "edit";
	onChange?: (value: any) => void;
};

export const DropdownFieldRenderer: React.FC<DropdownFieldRendererProps> = ({
	field,
	value,
	mode,
	onChange,
}) => {
	if (mode === "view") {
		const selected = field.options?.find((opt) => opt.id === value)
			?.value || <span className="text-slate-400">No response</span>;
		return <div>{selected}</div>;
	}
	return (
		<div className="relative">
			<select
				className="w-full appearance-none rounded border px-3 py-2.5 text-xs font-normal text-[#18181B]"
				value={value ?? ""}
				onChange={
					mode === "edit" && onChange
						? (e) => onChange(e.target.value)
						: undefined
				}
				disabled={mode !== "edit"}
			>
				<option value="" disabled>
					Select an option
				</option>
				{field.options?.map((opt) => (
					<option key={opt.id} value={opt.id}>
						{opt.value}
					</option>
				))}
			</select>
			<ChevronDown className="absolute top-1/2 right-3 h-4 w-4 -translate-y-1/2" />
		</div>
	);
};
