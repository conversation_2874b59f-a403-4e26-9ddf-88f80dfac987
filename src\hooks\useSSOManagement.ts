import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { APIVersion1GetSSOConfig, APIVersion1EnableSSO, APIVersion1UpdateSSO, APIVersion1DeleteSSO } from '../lib/api/auth';
import type { SSOConfigResponse, SSOEnableRequest, SSOEnableResponse, SSOConfigItem } from '../types/auth';
import { useOrganizationContext } from '../features/organizations/context/OrganizationContext';

// Hook to fetch SSO configuration
export const useGetSSOConfig = () => {
    const { organizationId } = useOrganizationContext();

    return useQuery<SSOConfigResponse>({
        queryKey: ['sso-config', organizationId],
        queryFn: () => APIVersion1GetSSOConfig(organizationId!),
        staleTime: 5 * 60 * 1000, // 5 minutes
        cacheTime: 10 * 60 * 1000, // 10 minutes
        enabled: !!organizationId,
    });
};

// Hook to enable SSO configuration (first time setup)
export const useEnableSSO = () => {
    const queryClient = useQueryClient();
    const { organizationId } = useOrganizationContext();

    return useMutation<SSOEnableResponse, Error, SSOEnableRequest>({
        mutationFn: (ssoConfig) => APIVersion1EnableSSO(ssoConfig, organizationId!),
        onSuccess: () => {
            // Invalidate and refetch SSO config after successful update
            queryClient.invalidateQueries({ queryKey: ['sso-config', organizationId] });
            // Also refetch immediately
            queryClient.refetchQueries({ queryKey: ['sso-config', organizationId] });
        },
    });
};

// Hook to update existing SSO configuration
export const useUpdateSSO = () => {
    const queryClient = useQueryClient();
    const { organizationId } = useOrganizationContext();

    return useMutation<SSOEnableResponse, Error, SSOEnableRequest>({
        mutationFn: (ssoConfig) => APIVersion1UpdateSSO(ssoConfig, organizationId!),
        onSuccess: () => {
            // Invalidate and refetch SSO config after successful update
            queryClient.invalidateQueries({ queryKey: ['sso-config', organizationId] });
            // Also refetch immediately
            queryClient.refetchQueries({ queryKey: ['sso-config', organizationId] });
        },
    });
};

// Hook to delete SSO configuration
export const useDeleteSSO = () => {
    const queryClient = useQueryClient();
    const { organizationId } = useOrganizationContext();

    return useMutation<SSOEnableResponse, Error, void>({
        mutationFn: () => APIVersion1DeleteSSO(organizationId!),
        onSuccess: () => {
            // Invalidate and refetch SSO config after successful deletion
            queryClient.invalidateQueries({ queryKey: ['sso-config', organizationId] });
            // Also refetch immediately
            queryClient.refetchQueries({ queryKey: ['sso-config', organizationId] });
        },
    });
}; 