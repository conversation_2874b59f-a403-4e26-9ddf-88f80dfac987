import { MultiStepSheet } from "@/components/common/MultiStepSheet";
import { CreateStationOptionSelectionStep } from "./station-steps/CreateStationOptionSelectionStep";
import { StationDetailsStep } from "./station-steps/StationDetailsStep";
import { AddServiceProviderStep } from "./station-steps/AddServiceProviderStep";
import { AddFromExistingStep } from "./station-steps/AddFromExistingStep";
import { ImportSettingsStep } from "./station-steps/ImportSettingsStep";
import { StationCompletionStep } from "./station-steps/StationCompletionStep";
import { useStationForm } from "../../hooks/useStationForm";
import { useStepNavigation } from "../../hooks/useStepNavigation";
import { useLocationStationSelection } from "../../hooks/useLocationStationSelection";
import { useStationOperations } from "../../hooks/useStationOperations";
import { StationProvider } from "../../context/StationContext";
import type {
	CreateStationRequest,
	CreateProviderStationRequest,
} from "../../types";
import { useForm } from "react-hook-form";
import type { StationFormData } from "../../schemas/stationSchema";
import { useState } from "react";
import type { UploadedFile } from "@/components/common/Uploader/types";
import { Skeleton } from "@/components/ui/skeleton";

interface AddStationSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (
		data:
			| (CreateProviderStationRequest & { imageFile?: File })
			| { name: string; description?: string }
	) => Promise<void>;
	locationId?: string;
	organizationId?: number;
}

// Inner component that uses the station context
function AddStationSheetContent({
	open,
	onOpenChange,
	onSubmit,
	locationId,
	organizationId,
}: AddStationSheetProps) {
	// Use simplified station operations hook - now safely within StationProvider
	const { addStation, addStationOnly } = useStationOperations();
	// Custom hooks
	const {
		formData,
		isSubmitting,
		errors,
		handleMethodToggle,
		handlePreferenceChange,
		validateCurrentStep,
		onFormSubmit,
	} = useStationForm({
		onSubmit: async (data) => {
			await onSubmit?.(data);
			onOpenChange(false);
		},
	});

	// Get form methods from createStationForm
	const form = useForm<StationFormData>({
		defaultValues: {
			stationName: "",
			description: "",
			autoApprove: false,
			serviceVisibility: true,
			serviceAvailability: true,
			availableMethods: ["in-person"],
			stationDuration: 30,
			durationUnit: "minutes",
			applyStationTo: "all-locations",
			selectedStations: [],
		},
		mode: "onChange",
	});

	const { control, watch, setValue, handleSubmit, reset } = form;

	// State to track selected option
	const [selectedOption, setSelectedOption] = useState<
		"new" | "new-with-provider" | "existing" | null
	>(null);

	// State to track selected providers for import
	const [selectedProviders, setSelectedProviders] = useState<any[]>([]);

	// State to track completion info
	const [completionInfo, setCompletionInfo] = useState<{
		providerName?: string;
		stationName?: string;
	}>({});

	// Provider form data for API submission
	const [providerFormData, setProviderFormData] =
		useState<CreateProviderStationRequest>({
			name: "",
			image: "",
			description: "",
			service_provider_first_name: "",
			service_provider_last_name: "",
			service_provider_email: "",
			service_provider_phone: "",
		});
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
	const [isSubmittingProvider, setIsSubmittingProvider] = useState(false);
	const [isShowingSuccess, setIsShowingSuccess] = useState(false);

	// Handle provider form input changes
	const handleProviderInputChange = (
		field: keyof CreateProviderStationRequest,
		value: string
	) => {
		setProviderFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	// Handle image upload
	const handleImageChange = (files: File[]) => {
		if (files.length > 0) {
			const file = files[0];
			setSelectedFile(file);

			const uploadedFile: UploadedFile = {
				id: `file-${Date.now()}`,
				name: file.name,
				size: file.size,
				type: file.type,
				preview: URL.createObjectURL(file),
			};
			setUploadedFiles([uploadedFile]);
			setProviderFormData((prev) => ({ ...prev, image: "" }));
		} else {
			setSelectedFile(null);
			setUploadedFiles([]);
			setProviderFormData((prev) => ({ ...prev, image: "" }));
		}
	};

	// Handle file removal
	const handleFileRemove = (fileId: string) => {
		const fileToRemove = uploadedFiles.find((file) => file.id === fileId);
		if (fileToRemove?.preview) {
			URL.revokeObjectURL(fileToRemove.preview);
		}
		setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId));
		setSelectedFile(null);
		setProviderFormData((prev) => ({ ...prev, image: "" }));
	};

	// Handle file edit
	const handleFileEdit = (fileId: string, newName: string) => {
		setUploadedFiles((prev) =>
			prev.map((file) =>
				file.id === fileId ? { ...file, name: newName } : file
			)
		);
	};

	// Validate provider form
	const isProviderFormValid = () => {
		return (
			providerFormData.service_provider_first_name.trim() &&
			providerFormData.service_provider_last_name.trim() &&
			providerFormData.service_provider_email.trim() &&
			providerFormData.service_provider_phone.trim()
		);
	};

	// Submit station data without provider
	const handleStationOnlySubmit = async () => {
		setIsSubmittingProvider(true);
		try {
			const stationData = {
				name: watch("stationName"),
				description: watch("description") || "",
			};

			// Use the station operations hook or fallback to onSubmit prop
			if (addStationOnly) {
				await addStationOnly(stationData);
			} else {
				await onSubmit?.(stationData);
			}

			setIsShowingSuccess(true);

			// Store completion info
			setCompletionInfo({
				stationName: watch("stationName") || "New Station",
			});
		} catch (error) {
			console.error("Error creating station:", error);
		} finally {
			setIsSubmittingProvider(false);
		}
	};

	// Submit provider data via API
	const handleProviderSubmit = async () => {
		setIsSubmittingProvider(true);
		try {
			const submitData = {
				...providerFormData,
				...(selectedFile && { imageFile: selectedFile }),
			};

			// Use the station operations hook or fallback to onSubmit prop
			if (addStation) {
				await addStation(submitData);
			} else if (onSubmit) {
				await onSubmit(submitData);
			}

			// Wait a moment before showing success
			await new Promise((resolve) => setTimeout(resolve, 1500));
			setIsShowingSuccess(true);

			resetProviderForm();
		} catch (error) {
			console.error("Error submitting provider:", error);
		} finally {
			setIsSubmittingProvider(false);
		}
	};

	// Reset provider form
	const resetProviderForm = () => {
		uploadedFiles.forEach((file) => {
			if (file.preview) {
				URL.revokeObjectURL(file.preview);
			}
		});

		setProviderFormData({
			name: "",
			image: "",
			description: "",
			service_provider_first_name: "",
			service_provider_last_name: "",
			service_provider_email: "",
			service_provider_phone: "",
		});
		setSelectedFile(null);
		setUploadedFiles([]);
		setIsShowingSuccess(false);
	};

	// Dynamic total steps based on selected option
	const getTotalSteps = () => {
		if (selectedOption === "existing") return 4; // Option → Add from Existing → Import Settings → Final
		if (selectedOption === "new") return 3; // Option → Station Details → Final (no provider)
		return 4; // Option → Station Details → Add Provider → Final (with provider)
	};

	const {
		currentStep,
		isLastStep,
		handleNext,
		handlePrevious,
		canProceed,
		goToStep,
	} = useStepNavigation({
		totalSteps: getTotalSteps(),
		initialStep: 1,
		onStepChange: async (step) => {
			if (selectedOption === "new" && step === 3) {
				// For station-only flow, submit on step 3
				await handleStationOnlySubmit();
			} else if (selectedOption === "new-with-provider" && step === 4) {
				// For station with provider flow, submit on step 4
				await handleProviderSubmit();
			}
		},
	});

	const {
		selectedLocationIds,
		selectedStationIds,
		handleLocationSelection,
		handleStationSelection,
		clearSelections,
	} = useLocationStationSelection();

	// Handle next/submit
	const handleFormNext = () => {
		if (isLastStep) {
			if (
				selectedOption === "new" ||
				selectedOption === "new-with-provider"
			) {
				// Data already submitted in previous step, just close
				onOpenChange(false);
			} else {
				// Handle completion for "existing" flow
				onOpenChange(false);
			}
		} else {
			handleNext();
		}
	};

	// Handle cancel
	const handleCancel = () => {
		reset();
		clearSelections();
		resetProviderForm();
		setSelectedOption(null);
		setSelectedProviders([]);
		setCompletionInfo({});
		onOpenChange(false);
		goToStep(1);
	};

	// Reset everything when sheet closes
	const handleSheetChange = (open: boolean) => {
		if (!open) {
			reset();
			clearSelections();
			resetProviderForm();
			setSelectedOption(null);
			setSelectedProviders([]);
			setCompletionInfo({});
			goToStep(1);
		}
		onOpenChange(open);
	};

	// Render step content
	const renderStepContent = () => {
		// Step 1 is always the option selection
		if (currentStep === 1) {
			return (
				<CreateStationOptionSelectionStep
					onAddNewStation={() => {
						setSelectedOption("new");
						handleNext();
					}}
					onAddNewStationWithProvider={() => {
						setSelectedOption("new-with-provider");
						handleNext();
					}}
					onAddFromExisting={() => {
						setSelectedOption("existing");
						handleNext();
					}}
				/>
			);
		}

		// Handle different flows based on selected option
		if (selectedOption === "new") {
			// Station only flow (2 steps: Station Details → Final)
			switch (currentStep) {
				case 2:
					return (
						<StationDetailsStep
							control={control}
							errors={errors}
							watch={watch}
							setValue={setValue}
							onBack={handlePrevious}
							onAddServiceProvider={() => {
								setSelectedOption("new-with-provider");
								handleNext();
							}}
							providerFormData={providerFormData}
							onProviderDataChange={handleProviderInputChange}
							showProviderSection={false} // Hide provider section for station-only flow
						/>
					);
				case 3:
					// Show loading state while submitting, then success screen
					if (isSubmittingProvider) {
						return (
							<div className="flex flex-col items-center justify-center space-y-4 py-12">
								<div className="space-y-2 text-center">
									<Skeleton className="mx-auto h-8 w-8 rounded-full" />
									<Skeleton className="mx-auto h-4 w-32" />
									<Skeleton className="mx-auto h-3 w-48" />
								</div>
							</div>
						);
					}

					return (
						<StationCompletionStep
							onCancel={handleCancel}
							onViewStation={() => {
								// Handle view station navigation
								onOpenChange(false);
							}}
							providerName={
								completionInfo.stationName ||
								watch("stationName") ||
								"New Station"
							}
							isImport={false}
							isStationOnly={true}
						/>
					);
			}
		} else if (selectedOption === "new-with-provider") {
			// Station with provider flow (3 steps: Station Details → Add Provider → Final)
			switch (currentStep) {
				case 2:
					return (
						<StationDetailsStep
							control={control}
							errors={errors}
							watch={watch}
							setValue={setValue}
							onBack={handlePrevious}
							onAddServiceProvider={handleNext}
							providerFormData={providerFormData}
							onProviderDataChange={handleProviderInputChange}
							showProviderSection={true} // Show provider section
						/>
					);
				case 3:
					return (
						<AddServiceProviderStep
							onBack={handlePrevious}
							onSendInvite={async (data) => {
								// Update provider form data with the step data
								setProviderFormData((prev) => ({
									...prev,
									name: watch("stationName") || prev.name,
									service_provider_first_name:
										data.firstName || "",
									service_provider_last_name:
										data.lastName || "",
									service_provider_email: data.email || "",
									service_provider_phone: data.phone || "",
								}));

								// Store completion info
								setCompletionInfo({
									providerName: `${data.firstName} ${data.lastName}`,
									stationName:
										watch("stationName") || "New Station",
								});

								// Move to step 4 immediately, submission happens there
								handleNext();
							}}
							providerFormData={providerFormData}
							onProviderDataChange={handleProviderInputChange}
							selectedFile={selectedFile}
							uploadedFiles={uploadedFiles}
							onImageChange={handleImageChange}
							onFileRemove={handleFileRemove}
							onFileEdit={handleFileEdit}
							isSubmitting={isSubmittingProvider}
						/>
					);
				case 4:
					// Show loading state while submitting, then success screen
					if (isSubmittingProvider) {
						return (
							<div className="flex flex-col items-center justify-center space-y-4 py-12">
								<div className="space-y-2 text-center">
									<Skeleton className="mx-auto h-8 w-8 rounded-full" />
									<Skeleton className="mx-auto h-4 w-32" />
									<Skeleton className="mx-auto h-3 w-48" />
								</div>
							</div>
						);
					}

					return (
						<StationCompletionStep
							onCancel={handleCancel}
							onViewStation={() => {
								// Handle view station navigation
								onOpenChange(false);
							}}
							providerName={
								completionInfo.providerName ||
								watch("stationName") ||
								"New Station"
							}
							isImport={false}
							isStationOnly={false}
						/>
					);
			}
		} else if (selectedOption === "existing") {
			switch (currentStep) {
				case 2:
					return (
						<AddFromExistingStep
							onBack={handlePrevious}
							onImport={(providers) => {
								setSelectedProviders(providers);
								handleNext();
							}}
							locationId={locationId}
						/>
					);
				case 3:
					return (
						<ImportSettingsStep
							onBack={handlePrevious}
							onImport={(settings) => {
								// Handle the imported settings
								handleNext();
							}}
							providerName={
								selectedProviders[0]?.name ||
								"Service Provider Station Name"
							}
							locationName="Location Name"
						/>
					);
				case 4:
					return (
						<StationCompletionStep
							onCancel={handleCancel}
							onViewStation={() => {
								// Handle view station navigation
								onOpenChange(false);
							}}
							providerName={
								selectedProviders[0]?.name || "Imported Station"
							}
							isImport={true}
						/>
					);
			}
		}

		return null;
	};

	return (
		<MultiStepSheet
			open={open}
			onOpenChange={handleSheetChange}
			title="Add New Station"
			currentStep={currentStep}
			totalSteps={getTotalSteps()}
			onNext={handleFormNext}
			onPrevious={handlePrevious}
			onCancel={handleCancel}
			canProceed={canProceed(() => {
				if (selectedOption === "existing" && currentStep === 2) {
					// For "add from existing" step, require at least one provider selected
					return selectedProviders.length > 0;
				}
				return true;
			})}
			isSubmitting={isSubmittingProvider}
			showPreviousButton={
				currentStep > 1 && currentStep !== getTotalSteps()
			}
			nextButtonText={
				selectedOption === "new-with-provider" && currentStep === 3
					? "Send Invite"
					: selectedOption === "new" && currentStep === 2
						? "Create Station"
						: selectedOption === "existing"
							? "Import"
							: currentStep === getTotalSteps()
								? "View Station"
								: "Next"
			}
		>
			{renderStepContent()}
		</MultiStepSheet>
	);
}

// Main export component that conditionally wraps with StationProvider
export function AddStationSheet(props: AddStationSheetProps) {
	const { locationId, organizationId } = props;

	// If we have both locationId and organizationId, wrap with StationProvider
	if (locationId && organizationId) {
		return (
			<StationProvider
				locationId={locationId}
				organizationId={organizationId}
			>
				<AddStationSheetContent {...props} />
			</StationProvider>
		);
	}

	// If we don't have the required props, use the component without context
	// This will handle the case gracefully by using onSubmit prop instead
	return <AddStationSheetContentWithoutContext {...props} />;
}

// Fallback component for when StationProvider context is not available
function AddStationSheetContentWithoutContext({
	open,
	onOpenChange,
	onSubmit,
	locationId,
	organizationId,
}: AddStationSheetProps) {
	// Custom hooks (same as the main component but without useStationOperations)
	const {
		formData,
		isSubmitting,
		errors,
		handleMethodToggle,
		handlePreferenceChange,
		validateCurrentStep,
		onFormSubmit,
	} = useStationForm({
		onSubmit: async (data) => {
			await onSubmit?.(data);
			onOpenChange(false);
		},
	});

	// Get form methods from createStationForm
	const form = useForm<StationFormData>({
		defaultValues: {
			stationName: "",
			description: "",
			autoApprove: false,
			serviceVisibility: true,
			serviceAvailability: true,
			availableMethods: ["in-person"],
			stationDuration: 30,
			durationUnit: "minutes",
			applyStationTo: "all-locations",
			selectedStations: [],
		},
		mode: "onChange",
	});

	const { control, watch, setValue, handleSubmit, reset } = form;

	// State to track selected option
	const [selectedOption, setSelectedOption] = useState<
		"new" | "new-with-provider" | "existing" | null
	>(null);

	// State to track selected providers for import
	const [selectedProviders, setSelectedProviders] = useState<any[]>([]);

	// State to track completion info
	const [completionInfo, setCompletionInfo] = useState<{
		providerName?: string;
		stationName?: string;
	}>({});

	// Provider form data for API submission
	const [providerFormData, setProviderFormData] =
		useState<CreateProviderStationRequest>({
			name: "",
			image: "",
			description: "",
			service_provider_first_name: "",
			service_provider_last_name: "",
			service_provider_email: "",
			service_provider_phone: "",
		});
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
	const [isSubmittingProvider, setIsSubmittingProvider] = useState(false);
	const [isShowingSuccess, setIsShowingSuccess] = useState(false);

	// Handle provider form input changes
	const handleProviderInputChange = (
		field: keyof CreateProviderStationRequest,
		value: string
	) => {
		setProviderFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	// Handle image upload
	const handleImageChange = (files: File[]) => {
		if (files.length > 0) {
			const file = files[0];
			setSelectedFile(file);

			const uploadedFile: UploadedFile = {
				id: `file-${Date.now()}`,
				name: file.name,
				size: file.size,
				type: file.type,
				preview: URL.createObjectURL(file),
			};
			setUploadedFiles([uploadedFile]);
			setProviderFormData((prev) => ({ ...prev, image: "" }));
		} else {
			setSelectedFile(null);
			setUploadedFiles([]);
			setProviderFormData((prev) => ({ ...prev, image: "" }));
		}
	};

	// Handle file removal
	const handleFileRemove = (fileId: string) => {
		const fileToRemove = uploadedFiles.find((file) => file.id === fileId);
		if (fileToRemove?.preview) {
			URL.revokeObjectURL(fileToRemove.preview);
		}
		setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId));
		setSelectedFile(null);
		setProviderFormData((prev) => ({ ...prev, image: "" }));
	};

	// Handle file edit
	const handleFileEdit = (fileId: string, newName: string) => {
		setUploadedFiles((prev) =>
			prev.map((file) =>
				file.id === fileId ? { ...file, name: newName } : file
			)
		);
	};

	// Validate provider form
	const isProviderFormValid = () => {
		return (
			providerFormData.service_provider_first_name.trim() &&
			providerFormData.service_provider_last_name.trim() &&
			providerFormData.service_provider_email.trim() &&
			providerFormData.service_provider_phone.trim()
		);
	};

	// Submit station data without provider - fallback to onSubmit prop
	const handleStationOnlySubmit = async () => {
		setIsSubmittingProvider(true);
		try {
			const stationData = {
				name: watch("stationName"),
				description: watch("description") || "",
			};

			await onSubmit?.(stationData);
			setIsShowingSuccess(true);

			// Store completion info
			setCompletionInfo({
				stationName: watch("stationName") || "New Station",
			});
		} catch (error) {
			console.error("Error creating station:", error);
		} finally {
			setIsSubmittingProvider(false);
		}
	};

	// Submit provider data via API - fallback to onSubmit prop
	const handleProviderSubmit = async () => {
		setIsSubmittingProvider(true);
		try {
			const submitData = {
				...providerFormData,
				...(selectedFile && { imageFile: selectedFile }),
			};

			await onSubmit?.(submitData);

			// Wait a moment before showing success
			await new Promise((resolve) => setTimeout(resolve, 1500));
			setIsShowingSuccess(true);

			resetProviderForm();
		} catch (error) {
			console.error("Error submitting provider:", error);
		} finally {
			setIsSubmittingProvider(false);
		}
	};

	// Reset provider form
	const resetProviderForm = () => {
		uploadedFiles.forEach((file) => {
			if (file.preview) {
				URL.revokeObjectURL(file.preview);
			}
		});

		setProviderFormData({
			name: "",
			image: "",
			description: "",
			service_provider_first_name: "",
			service_provider_last_name: "",
			service_provider_email: "",
			service_provider_phone: "",
		});
		setSelectedFile(null);
		setUploadedFiles([]);
		setIsShowingSuccess(false);
	};

	// Dynamic total steps based on selected option
	const getTotalSteps = () => {
		if (selectedOption === "existing") return 4; // Option → Add from Existing → Import Settings → Final
		if (selectedOption === "new") return 3; // Option → Station Details → Final (no provider)
		return 4; // Option → Station Details → Add Provider → Final (with provider)
	};

	const {
		currentStep,
		isLastStep,
		handleNext,
		handlePrevious,
		canProceed,
		goToStep,
	} = useStepNavigation({
		totalSteps: getTotalSteps(),
		initialStep: 1,
		onStepChange: async (step) => {
			if (selectedOption === "new" && step === 3) {
				// For station-only flow, submit on step 3
				await handleStationOnlySubmit();
			} else if (selectedOption === "new-with-provider" && step === 4) {
				// For station with provider flow, submit on step 4
				await handleProviderSubmit();
			}
		},
	});

	const {
		selectedLocationIds,
		selectedStationIds,
		handleLocationSelection,
		handleStationSelection,
		clearSelections,
	} = useLocationStationSelection();

	// Handle next/submit
	const handleFormNext = () => {
		if (isLastStep) {
			if (
				selectedOption === "new" ||
				selectedOption === "new-with-provider"
			) {
				// Data already submitted in previous step, just close
				onOpenChange(false);
			} else {
				// Handle completion for "existing" flow
				onOpenChange(false);
			}
		} else {
			handleNext();
		}
	};

	// Handle cancel
	const handleCancel = () => {
		reset();
		clearSelections();
		resetProviderForm();
		setSelectedOption(null);
		setSelectedProviders([]);
		setCompletionInfo({});
		onOpenChange(false);
		goToStep(1);
	};

	// Reset everything when sheet closes
	const handleSheetChange = (open: boolean) => {
		if (!open) {
			reset();
			clearSelections();
			resetProviderForm();
			setSelectedOption(null);
			setSelectedProviders([]);
			setCompletionInfo({});
			goToStep(1);
		}
		onOpenChange(open);
	};

	// Render step content (same logic as main component)
	const renderStepContent = () => {
		// Step 1 is always the option selection
		if (currentStep === 1) {
			return (
				<CreateStationOptionSelectionStep
					onAddNewStation={() => {
						setSelectedOption("new");
						handleNext();
					}}
					onAddNewStationWithProvider={() => {
						setSelectedOption("new-with-provider");
						handleNext();
					}}
					onAddFromExisting={() => {
						setSelectedOption("existing");
						handleNext();
					}}
				/>
			);
		}

		// Handle different flows based on selected option
		if (selectedOption === "new") {
			// Station only flow (2 steps: Station Details → Final)
			switch (currentStep) {
				case 2:
					return (
						<StationDetailsStep
							control={control}
							errors={errors}
							watch={watch}
							setValue={setValue}
							onBack={handlePrevious}
							onAddServiceProvider={() => {
								setSelectedOption("new-with-provider");
								handleNext();
							}}
							providerFormData={providerFormData}
							onProviderDataChange={handleProviderInputChange}
							showProviderSection={false} // Hide provider section for station-only flow
						/>
					);
				case 3:
					// Show loading state while submitting, then success screen
					if (isSubmittingProvider) {
						return (
							<div className="flex flex-col items-center justify-center space-y-4 py-12">
								<div className="space-y-2 text-center">
									<Skeleton className="mx-auto h-8 w-8 rounded-full" />
									<Skeleton className="mx-auto h-4 w-32" />
									<Skeleton className="mx-auto h-3 w-48" />
								</div>
							</div>
						);
					}

					return (
						<StationCompletionStep
							onCancel={handleCancel}
							onViewStation={() => {
								// Handle view station navigation
								onOpenChange(false);
							}}
							providerName={
								completionInfo.stationName ||
								watch("stationName") ||
								"New Station"
							}
							isImport={false}
							isStationOnly={true}
						/>
					);
			}
		} else if (selectedOption === "new-with-provider") {
			// Station with provider flow (3 steps: Station Details → Add Provider → Final)
			switch (currentStep) {
				case 2:
					return (
						<StationDetailsStep
							control={control}
							errors={errors}
							watch={watch}
							setValue={setValue}
							onBack={handlePrevious}
							onAddServiceProvider={handleNext}
							providerFormData={providerFormData}
							onProviderDataChange={handleProviderInputChange}
							showProviderSection={true} // Show provider section
						/>
					);
				case 3:
					return (
						<AddServiceProviderStep
							onBack={handlePrevious}
							onSendInvite={async (data) => {
								// Update provider form data with the step data
								setProviderFormData((prev) => ({
									...prev,
									name: watch("stationName") || prev.name,
									service_provider_first_name:
										data.firstName || "",
									service_provider_last_name:
										data.lastName || "",
									service_provider_email: data.email || "",
									service_provider_phone: data.phone || "",
								}));

								// Store completion info
								setCompletionInfo({
									providerName: `${data.firstName} ${data.lastName}`,
									stationName:
										watch("stationName") || "New Station",
								});

								// Move to step 4 immediately, submission happens there
								handleNext();
							}}
							providerFormData={providerFormData}
							onProviderDataChange={handleProviderInputChange}
							selectedFile={selectedFile}
							uploadedFiles={uploadedFiles}
							onImageChange={handleImageChange}
							onFileRemove={handleFileRemove}
							onFileEdit={handleFileEdit}
							isSubmitting={isSubmittingProvider}
						/>
					);
				case 4:
					// Show loading state while submitting, then success screen
					if (isSubmittingProvider) {
						return (
							<div className="flex flex-col items-center justify-center space-y-4 py-12">
								<div className="space-y-2 text-center">
									<Skeleton className="mx-auto h-8 w-8 rounded-full" />
									<Skeleton className="mx-auto h-4 w-32" />
									<Skeleton className="mx-auto h-3 w-48" />
								</div>
							</div>
						);
					}

					return (
						<StationCompletionStep
							onCancel={handleCancel}
							onViewStation={() => {
								// Handle view station navigation
								onOpenChange(false);
							}}
							providerName={
								completionInfo.providerName ||
								watch("stationName") ||
								"New Station"
							}
							isImport={false}
							isStationOnly={false}
						/>
					);
			}
		} else if (selectedOption === "existing") {
			switch (currentStep) {
				case 2:
					return (
						<AddFromExistingStep
							onBack={handlePrevious}
							onImport={(providers) => {
								setSelectedProviders(providers);
								handleNext();
							}}
							locationId={locationId}
						/>
					);
				case 3:
					return (
						<ImportSettingsStep
							onBack={handlePrevious}
							onImport={(settings) => {
								// Handle the imported settings
								handleNext();
							}}
							providerName={
								selectedProviders[0]?.name ||
								"Service Provider Station Name"
							}
							locationName="Location Name"
						/>
					);
				case 4:
					return (
						<StationCompletionStep
							onCancel={handleCancel}
							onViewStation={() => {
								// Handle view station navigation
								onOpenChange(false);
							}}
							providerName={
								selectedProviders[0]?.name || "Imported Station"
							}
							isImport={true}
						/>
					);
			}
		}

		return null;
	};

	return (
		<MultiStepSheet
			open={open}
			onOpenChange={handleSheetChange}
			title="Add New Station"
			currentStep={currentStep}
			totalSteps={getTotalSteps()}
			onNext={handleFormNext}
			onPrevious={handlePrevious}
			onCancel={handleCancel}
			canProceed={canProceed(() => {
				if (selectedOption === "existing" && currentStep === 2) {
					// For "add from existing" step, require at least one provider selected
					return selectedProviders.length > 0;
				}
				return true;
			})}
			isSubmitting={isSubmittingProvider}
			showPreviousButton={
				currentStep > 1 && currentStep !== getTotalSteps()
			}
			nextButtonText={
				selectedOption === "new-with-provider" && currentStep === 3
					? "Send Invite"
					: selectedOption === "new" && currentStep === 2
						? "Create Station"
						: selectedOption === "existing"
							? "Import"
							: currentStep === getTotalSteps()
								? "View Station"
								: "Next"
			}
		>
			{renderStepContent()}
		</MultiStepSheet>
	);
}
