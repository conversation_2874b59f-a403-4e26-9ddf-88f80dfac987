import { useEffect, useState } from "react";
import { Search, Filter, Plus, MapPin, Settings2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";
import {
	AddLocationSheet,
	EditStationInformationSheet,
	ServiceFilterSheet,
} from "../locations/components/sheets";
import { useLocations } from "../locations/hooks/useLocations";
import { useStationContext } from "../locations/hooks";
import { useDeleteActions } from "@/hooks/useDeleteActions";
import { LocationProviderCard } from "./components/LocationProviderCard";
import type {
	LocationsFilters,
	LocationsResponse,
	Location,
	CreateStationRequest,
	CreateProviderStationRequest,
} from "../locations/types";
import { SendBookingLinkSheet } from "@/features/schedule";
import { StationInformationSheet } from "../locations/components/sheets/station-information";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { AddStationSheet } from "../locations/components/sheets";
import { useNavigate } from "react-router";

interface LocationProvidersTabProps {
	className?: string;
	onAddStation?: () => Promise<void>;
}

export function LocationProvidersTab({
	className,
	onAddStation,
}: LocationProvidersTabProps) {
	const navigate = useNavigate();
	const { deleteStation } = useDeleteActions();
	const {
		stations,
		isLoading: isLoadingStations,
		error: stationsError,
		refetchStations,
		createStation,
		locationId,
		organizationId,
	} = useStationContext();

	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});
	const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showAddLocationForm, setShowAddLocationForm] = useState(false);
	const [showLocationDetails, setShowLocationDetails] = useState(false);
	const [
		showEditStationInformationSheet,
		setShowEditStationInformationSheet,
	] = useState(false);
	const [selectedLocation, setSelectedLocation] = useState<Location | null>(
		null
	);
	const [showSendBookingLinkSheet, setShowSendBookingLinkSheet] =
		useState(false);
	const [activeTab, setActiveTab] = useState("list");
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const { data: locationsData2, isLoading } = useLocations({
		page: currentPage,
		search: searchTerm,
		limit: 10,
	});

	// Transform stations data to match the expected format
	const locationsData: LocationsResponse =
		stations && stations.length > 0
			? {
				data: stations.map(
					(station) =>
						({
							id: station.id.toString(),
							name: station.name,
							address:
								station.locations[0]?.name ||
								"Unknown Address",
							city: "Unknown City", // Default value since station data doesn't have city
							state: "Unknown State", // Default value since station data doesn't have state
							country: "Unknown Country", // Default value since station data doesn't have country
							phone:
								station.service_providers[0]?.email ||
								"N/A",
							email:
								station.service_providers[0]?.email || "",
							description: station.description || "",
							isActive: true,
							timezone: "America/New_York", // Default timezone
							coordinates: {
								latitude: 0,
								longitude: 0,
							},
							operatingHours: [],
							services: [],
							capacity: 100,
							amenities: [],
							organizationId:
								organizationId?.toString() || "1",
							createdAt: new Date().toISOString(),
							updatedAt: new Date().toISOString(),
							stations: [],
							service_count: station.service_providers.length,
							// Additional station-specific data for display
							image: station.image,
							service_provider: station.service_providers,
						}) as Location & {
							image?: string;
							service_provider?: any;
						}
				),
				pagination: {
					page: 1,
					limit: 10,
					total: stations.length,
					totalPages: 1,
				},
			}
			: {
				data: [],
				pagination: {
					page: 1,
					limit: 10,
					total: 0,
					totalPages: 0,
				},
			};

	// Debounced search filter
	useEffect(() => {
		const timer = setTimeout(() => {
			setFilters((prev) => ({
				...prev,
				search: searchTerm || undefined,
				page: 1,
			}));
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// Handle refetch when provider is created
	const handleAddLocation = async (
		data: (CreateProviderStationRequest & { imageFile?: File }) | { name: string; description?: string }
	) => {
		console.log("Adding new location:", data);
		try {
			// Use the context's createStation method
			if ('service_provider_first_name' in data) {
				await createStation(data as CreateProviderStationRequest & { imageFile?: File });
			} else {
				// Handle simple station creation
				console.log("Simple station data:", data);
			}
			setShowAddLocationForm(false);
		} catch (error) {
			console.error("Error adding provider:", error);
		}
	};

	const handleFilterChange = (newFilters: Partial<LocationsFilters>) => {
		setFilters((prev) => ({ ...prev, ...newFilters, page: 1 }));
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked && locationsData?.data) {
			setSelectedLocations(
				locationsData.data.map((location) => location.id)
			);
		} else {
			setSelectedLocations([]);
		}
	};

	const handleLocationSelection = (locationId: string, selected: boolean) => {
		if (selected) {
			setSelectedLocations((prev) => [...prev, locationId]);
		} else {
			setSelectedLocations((prev) =>
				prev.filter((id) => id !== locationId)
			);
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleViewLocation = (location: Location) => {
		navigate(
			`/dashboard/workplace/providers/provider-details/${location.id}`
		);
		// setSelectedLocation(location);
		// setShowLocationDetails(true);
	};

	const handleEditStation = (location: Location) => {
		setSelectedLocation(location);
		setShowEditStationInformationSheet(true);
	};

	const handleDeleteStation = (locationProvider: any) => {
		// Convert the locationProvider back to station format for deletion
		const stationData = {
			id: parseInt(locationProvider.id),
			name: locationProvider.name,
		};

		deleteStation(stationData as any, { refetch: refetchStations });
	};

	// Show loading state
	if (isLoadingStations) {
		return (
			<div className={className}>
				<div className="flex items-center justify-center py-8">
					<span className="text-sm text-gray-500">
						Loading providers...
					</span>
				</div>
			</div>
		);
	}

	// Show error state
	if (stationsError) {
		return (
			<div className={className}>
				<div className="flex items-center justify-center py-8">
					<div className="text-center">
						<MapPin className="mx-auto h-12 w-12 text-gray-400" />
						<h3 className="mt-2 text-sm font-medium text-gray-900">
							Error loading providers
						</h3>
						<p className="mt-1 text-sm text-gray-500">
							{stationsError.message}
						</p>
						<Button
							className="mt-4"
							onClick={() => refetchStations()}
							variant="outline"
						>
							Try Again
						</Button>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-end py-1">
				{/* <Tabs value={activeTab} onValueChange={setActiveTab}>
					<TabsList className="bg-zinc-100">
						<TabsTrigger
							value="grid"
							className="cursor-pointer text-sm font-medium"
						>
							Grid View
						</TabsTrigger>
						<TabsTrigger
							value="list"
							className="cursor-pointer text-sm font-medium"
						>
							List View
						</TabsTrigger>
						<TabsTrigger
							value="tabs"
							className="cursor-pointer text-sm font-medium"
						>
							Tabs Text
						</TabsTrigger>
					</TabsList>
				</Tabs> */}
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search locations..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className="cursor-pointer"
						size="icon"
						onClick={() => setShowFilterSheet(true)}
					>
						<Settings2 className="h-4 w-4" />
					</Button>
					<Button
						variant="default"
						className="cursor-pointer"
						onClick={() =>
							onAddStation?.() || setShowAddLocationForm(true)
						}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add Provider
					</Button>
				</div>
			</div>
			<div className="border-foregound-muted mt-5 box-border overflow-hidden rounded-lg border">
				<div className="flex items-center justify-between border-b border-zinc-200 py-4 pl-4">
					<Checkbox
						label=""
						checked={
							selectedLocations.length ===
							locationsData?.data?.length &&
							locationsData?.data?.length > 0
						}
						className="cursor-pointer"
						onCheckedChange={handleSelectAll}
					/>
				</div>

				{/* Locations Grid */}
				{locationsData && (
					<>
						{locationsData?.data?.length === 0 ? (
							<div className="py-12 text-center">
								<MapPin className="mx-auto h-12 w-12 text-gray-400" />
								<h3 className="mt-2 text-sm font-medium text-gray-900">
									No locations found
								</h3>
								<p className="mt-1 text-sm text-gray-500">
									Get started by creating your first location.
								</p>
								<Button
									className="mt-4"
									onClick={() =>
										onAddStation?.() ||
										setShowAddLocationForm(true)
									}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add Provider
								</Button>
							</div>
						) : (
							<div className="flex flex-col gap-0.5">
								{locationsData?.data?.map(
									(locationProvider: any) => (
										<LocationProviderCard
											key={locationProvider.id}
											locationProvider={locationProvider}
											isSelected={selectedLocations.includes(
												locationProvider.id
											)}
											onSelectionChange={(selected) =>
												handleLocationSelection(
													locationProvider.id,
													selected
												)
											}
											onEdit={() =>
												handleEditStation(
													locationProvider
												)
											}
											onView={() =>
												handleViewLocation(
													locationProvider
												)
											}
											onDelete={() =>
												handleDeleteStation(
													locationProvider
												)
											}
										/>
									)
								)}
							</div>
						)}

						{/* Pagination */}
						{locationsData?.pagination?.totalPages > 1 && (
							<div className="mt-8 flex items-center justify-center gap-2">
								<Button
									variant="outline"
									disabled={
										locationsData?.pagination?.page === 1
									}
									onClick={() =>
										handlePageChange(
											(locationsData?.pagination?.page ||
												1) - 1
										)
									}
								>
									Previous
								</Button>

								<span className="text-sm text-gray-600">
									Page {locationsData?.pagination?.page} of{" "}
									{locationsData?.pagination?.totalPages}
								</span>

								<Button
									variant="outline"
									disabled={
										locationsData?.pagination?.page ===
										locationsData?.pagination?.totalPages
									}
									onClick={() =>
										handlePageChange(
											(locationsData?.pagination?.page ||
												1) + 1
										)
									}
								>
									Next
								</Button>
							</div>
						)}
					</>
				)}
			</div>

			{/* Add Location Sheet */}
			<AddStationSheet
				open={showAddLocationForm}
				onOpenChange={setShowAddLocationForm}
				onSubmit={handleAddLocation}
			/>

			{/* Station Information Sheet */}
			<StationInformationSheet
				open={showLocationDetails}
				onClose={() => setShowLocationDetails(false)}
				onSendBookingLink={() => setShowSendBookingLinkSheet(true)}
			/>

			{/* Send Booking Link Sheet */}
			<SendBookingLinkSheet
				open={showSendBookingLinkSheet}
				onOpenChange={setShowSendBookingLinkSheet}
			/>

			{/* Edit Station Information Sheet */}
			<EditStationInformationSheet
				open={showEditStationInformationSheet}
				onClose={() => {
					setShowEditStationInformationSheet(false);
					setSelectedLocation(null);
				}}
				stationId={selectedLocation?.id || ""}
				stationData={
					selectedLocation
						? {
							id: selectedLocation.id,
							stationName: selectedLocation.name,
							description: selectedLocation.description || "",
							logoUrl: (selectedLocation as any)?.image || "",
							categories: [],
							providers:
								(
									selectedLocation as any
								)?.service_provider?.map(
									(provider: any) => ({
										id: provider.id?.toString() || "",
										name:
											provider.name ||
											provider.email ||
											"Unknown",
										email: provider.email || "",
										status: provider.status || "active",
										avatar: provider.avatar,
									})
								) || [],
						}
						: undefined
				}
				onSave={async (data) => {
					console.log("Saving station data:", data);
					// TODO: Implement station update API call
					// await updateStation(selectedLocation?.id, data);
					await refetchStations();
					setShowEditStationInformationSheet(false);
					setSelectedLocation(null);
				}}
				onProviderEdit={(providerId) => {
					console.log("Edit provider:", providerId);
					// TODO: Implement provider edit functionality
				}}
				onProviderDelete={(providerId) => {
					console.log("Provider deleted:", providerId);
					// Refresh station data after provider deletion
					refetchStations();
				}}
				onAddProvider={() => {
					console.log("Add new provider");
					// TODO: Implement add provider functionality
				}}
			/>

			{/* Filter Sheet */}
			<ServiceFilterSheet
				open={showFilterSheet}
				onOpenChange={setShowFilterSheet}
			/>
		</div>
	);
}
