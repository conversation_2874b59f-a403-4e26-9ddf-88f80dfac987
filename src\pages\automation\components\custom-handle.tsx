import type { CSSProperties } from 'react'
import { Handle, type HandleProps } from 'reactflow'
import { useEditor } from '../provider/editor'

type Props = HandleProps & { style?: CSSProperties }

const CustomHandle = (props: Props) => {
	const { state } = useEditor()

	return (
		<Handle
			{...props}
			isValidConnection={(e) => {
				const sourcesFromHandleInState = state.editor.edges.filter(
					(edge) => edge.source === e.source
				).length
				const sourceNode = state.editor.elements.find(
					(node) => node.id === e.source
				)
				//target
				const targetFromHandleInState = state.editor.edges.filter(
					(edge) => edge.target === e.target
				).length

				// Prevent any node from having more than one incoming connection
				if (targetFromHandleInState === 1) return false

				// Allow conditions to have multiple outgoing connections
				if (sourceNode?.type === 'Condition') return true

				// Allow triggers to have multiple outgoing connections
				if (sourceNode?.type === 'Trigger') return true

				// Allow connections if source doesn't have any outgoing connections yet
				if (sourcesFromHandleInState < 1) return true

				return false
			}}
			className="!-bottom-2 !h-4 !w-4 dark:bg-neutral-800"
		/>
	)
}

export default CustomHandle
