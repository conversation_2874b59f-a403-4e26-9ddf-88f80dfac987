import { dummyAnalyticsData, dummyFilterData } from './data';

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export class DummyAnalyticsService {
  // Main analytics data
  static async getAnalyticsData(params?: any) {
    await delay(800); // Simulate network delay
    
    // You can add logic here to filter data based on params
    // For now, return the full dummy data
    return {
      data: {
        data: dummyAnalyticsData
      }
    };
  }

  // Logo/blob data (if needed)
  static async getAnalyticsLogoBlob() {
    await delay(300);
    return {
      data: {
        logo_url: "https://via.placeholder.com/150x50?text=Logo",
        blob_data: null
      }
    };
  }

  // Status filter options
  static async getAnalyticsStatusForFiltering() {
    await delay(200);
    return {
      data: dummyFilterData.statuses
    };
  }

  // Services data
  static async getServices() {
    await delay(300);
    return {
      data: dummyFilterData.services
    };
  }

  // Locations data
  static async getAnalyticsQuery() {
    await delay(400);
    return {
      data: dummyFilterData.locations
    };
  }
} 