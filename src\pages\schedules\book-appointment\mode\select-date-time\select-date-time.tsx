import { Label } from "@/components/ui/label";
import { RefactorMultiSelect } from "@/pages/schedules/components/custom-select";

import { useLocationServices } from "@/features/locations/hooks/useServices";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { getFormattedHeader } from "@/pages/schedules/utils";
import { FaAngleLeft, FaAngleRight } from "react-icons/fa6";
import { FiUser } from "react-icons/fi";
import { GoCheckCircleFill } from "react-icons/go";
import { IoCalendar } from "react-icons/io5";
import { MdOutlineVideocam } from "react-icons/md";
import { PiSpeakerHighThin } from "react-icons/pi";
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
} from "@/components/ui/pagination"
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { ToggleButton } from "@/components/common/ToggleButton";

export default function SelectDateTime({
    back,
    next,
    locationId
}: { back: () => void; next: () => void; locationId?: string }) {
    const { organizationId } = useOrganizationContext();
    const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
    const [selectedServices, setSelectedServices] = useState<string[]>([]);
    const [selectedProviders, setSelectedProviders] = useState<string | string[]>("");

    // Fetch services for the current location
    const { data: servicesResponse, isLoading: isLoadingServices } = useLocationServices({
        locationId,
        organizationId: organizationId || undefined,
        enabled: !!locationId && !!organizationId
    });

    // Get full services data and create dropdown options
    const servicesData = servicesResponse?.data || [];
    const services = servicesData.map(service => service.name);

    // Find selected service object
    const selectedServiceName = selectedServices[0] || "";
    const selectedService = servicesData.find(service => service.name === selectedServiceName);

    // Get available appointment methods from selected service
    const availableMethodNames = selectedService?.appointment_methods?.map(method => method.name.toLowerCase().replace(" ", "-")) || [];

    // Get stations for the current location from the selected service
    const currentLocationSelection = selectedService?.location_selections?.find(
        selection => selection.location_id.toString() === locationId
    );
    const locationStations = currentLocationSelection?.stations || [];
    const stationNames = locationStations.map(station => station.name);

    // Use stations as providers (no fallback to hardcoded providers)
    const availableProviders = stationNames;
    // Reset selected types when service changes and default to in-person if available
    useEffect(() => {
        if (selectedService) {
            const hasInPerson = availableMethodNames.includes("in-person");
            setSelectedTypes(hasInPerson ? ["in-person"] : []);
        } else {
            setSelectedTypes([]);
        }
    }, [selectedServiceName, availableMethodNames.join(',')]);

    const toggleSelection = (type: string) => {
        // Only allow single selection for appointment methods
        setSelectedTypes([type]);
    };

    // Check if appointment method is available for selected service
    const isMethodAvailable = (methodId: string) => {
        if (!selectedService) return false; // Disable all if no service selected
        return availableMethodNames.includes(methodId);
    };

    const meetingTypes = [
        { id: "in-person", label: "In-Person", icon: FiUser },
        { id: "audio", label: "Audio", icon: PiSpeakerHighThin },
        { id: "video", label: "Video", icon: MdOutlineVideocam },
    ];

    return (
        <div className="mx-5 mt-5">

            <div className="space-y-6">
                <div className="flex flex-col gap-2">
                    <Label htmlFor="services" className="text-[#18181B] text-sm font-normal">Select Service</Label>

                    <RefactorMultiSelect
                        value={selectedServices}
                        setValue={(value) => setSelectedServices(Array.isArray(value) ? [value[0]].filter(Boolean) : value ? [value] : [])}
                        placeholder={isLoadingServices ? "Loading services..." : "Select a service"}
                        label="Services"
                        options={services}
                    />
                </div>
                <div>
                    <div className="flex flex-col gap-2">
                        <Label htmlFor="providers" className="text-[#18181B] text-sm font-normal">Select Provider</Label>

                        <RefactorMultiSelect
                            value={selectedProviders}
                            setValue={setSelectedProviders}
                            placeholder={!selectedServiceName ? "Select a service first" : availableProviders.length === 0 ? "No providers available" : "Select providers"}
                            label="Providers"
                            options={availableProviders}
                        />
                    </div>
                    <p className="text-xs text-[#71717A]">
                        Select a provider you want to book. You can select multiple providers together view all available time slots.
                    </p>
                </div>
                <div className="flex flex-col gap-3">
                    <Label htmlFor="appointmentType" className="text-[#18181B] text-sm font-normal">Appointment Method</Label>
                    <div className="flex flex-wrap gap-2">
                        {meetingTypes.map((type) => (
                            <ToggleButton
                                key={type.id}
                                label={type.label}
                                icon={type.icon as any}
                                isSelected={selectedTypes.includes(type.id)}
                                onClick={() => toggleSelection(type.id)}
                                disabled={!isMethodAvailable(type.id)}
                                className="py-5"
                            />
                        ))}
                    </div>
                </div>
            </div>

            <div className="mt-12 mb-5">
                <div className="max-w-[25rem] mx-auto">
                    <p className="text-center w-full mb-3">Select Date and Preferred Time Slot</p>
                    <div className="flex items-center justify-between">

                        <FaAngleLeft />
                        <div className="flex-1 flex items-center justify-center gap-x-1 text-center tracking-wider">
                            <h1 className="text-xl font-medium">
                                {getFormattedHeader(new Date(), "day")}
                            </h1>
                            <span
                                className="bg-[#F4F4F5] ml-1 p-2 rounded-md"
                            >
                                <IoCalendar />
                            </span>
                        </div>
                        <FaAngleRight />

                    </div>
                </div>

                <div className="relative rounded-lg bg-white overflow-hidden mt-6">
                    <div className="max-w-fit grid grid-flow-col place-content-center overflow-x-scroll overflow-y-hidden scrollbar-hide gap-x-3">
                        {Array.from({ length: 10 }).map((_, index) => (
                            <div key={index} className="w-[24rem]">
                                <div className="border border-[#E5E5E7] text-center py-3 rounded-lg">
                                    <h1 className="text-base">Monarch Corps</h1>

                                    <div className="grid grid-cols-2 gap-x-4 gap-y-3.5 mt-4 max-w-[22rem] mx-auto">

                                        {Array.from({ length: 10 }).map((_, index) => (
                                            <button
                                                key={index}
                                                tabIndex={0}
                                                className="text-nowrap cursor-pointer py-3 px-2.5 bg-[#0058930A] text-[#005893] border border-[#0000000A] rounded-lg text-sm font-normal"
                                            >
                                                <span>09:15 AM - 9:30 AM</span>
                                            </button>
                                        ))}

                                    </div>

                                    <div className="mt-6">
                                        <Pagination>
                                            <PaginationContent>
                                                <PaginationItem>
                                                    <FaAngleLeft />
                                                </PaginationItem>
                                                <PaginationItem>
                                                    <PaginationLink href="#">1</PaginationLink>
                                                </PaginationItem>
                                                <PaginationItem className="bg-white border rounded-lg">
                                                    <PaginationLink href="#">2</PaginationLink>
                                                </PaginationItem>
                                                <PaginationItem>
                                                    <PaginationEllipsis />
                                                </PaginationItem>
                                                <PaginationItem>
                                                    <PaginationLink href="#">3</PaginationLink>
                                                </PaginationItem>
                                                <PaginationItem>
                                                    <FaAngleRight />
                                                </PaginationItem>
                                            </PaginationContent>
                                        </Pagination>
                                    </div>

                                </div>
                            </div>
                        ))}
                    </div>
                </div>

            </div>

            <div className="flex items-center justify-end gap-x-3">
                <Button
                    type="button"
                    variant="outline"
                    onClick={back}
                    className="bg-[#F4F4F5] cursor-pointer"
                >
                    Back
                </Button>
                <Button
                    type="button"
                    className="cursor-pointer"
                    onClick={next}
                >
                    Next
                </Button>
            </div>

        </div>
    )
}