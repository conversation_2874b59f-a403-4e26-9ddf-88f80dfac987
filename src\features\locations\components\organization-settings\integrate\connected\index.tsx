import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/tabs"
import migranium from "../../images/migranium.png"
import acurro from "../../images/acurro.png"
import oscarPro from "../../images/oscar.png"
import vector from "../../images/vector.png"
import AppointmentTypes from "./appointment-types"
import { useState } from "react"
import { Button } from "@/components/ui/Button/Button"
import clsx from "clsx"
import { cn } from "@/lib/utils"
import AppointmentService from "./appointment-service"
import AppointmentStatus from "./appointment-status"
import Providers from "./providers"
import Flags from "./flags"

export default function ConnectedEHR({ selectedIntegration }: { selectedIntegration: "accuro-emr" | "oscar-pro" }) {
    const tabsTrigger = ["Appointment Types", "Appointment Service", "Appointment Status", "Providers", "Flags"]
    const [activeTab, setActiveTab] = useState("Appointment Types")
    return (
        <div>
            <div className="flex items-center justify-center flex-col text-center">
                <div className="flex items-center gap-x-7 mt-7">
                    <div className="border border-[#D4D4D8] p-2 rounded-lg">
                        <img
                            src={migranium}
                            alt="Migranium"
                            className="size-7"
                        />
                    </div>
                    <img
                        src={vector}
                        alt="Vector"
                        className="size-7"
                    />
                    <div className="rounded-lg bg-white shadow-[0px_4px_20px_0px_#9BAAB42E] p-2 scale-90">
                        <img
                            src={selectedIntegration === "oscar-pro" ? oscarPro : acurro}
                            alt="Oscar Pro"
                            className="size-9"
                        />
                    </div>
                </div>
                <h1 className="text-[#27272A] font-medium text-lg mt-3">
                    You are <span className="text-[#34A853] font-medium text-xl">connected!</span>
                    <p className="text-[#71717A] font-light text-sm mt-1">Connected since: DD MM YYYY</p>
                    <p className="text-[#71717A] font-light text-sm mt-1">Business UUID : #### ## ###</p>
                </h1>

            </div>
            <div className="mt-8">
                <h1 className="text-[#27272A] font-medium text-lg mt-3">Match with Migranium</h1>
                <p className="text-[#71717A] font-light text-sm mt-1">
                    Prior to our onboarding meeting, you will provide us with some information. If not, no worries, we will guide you through it during our onbaording call, just make sure to book request some additional time with us!
                </p>
            </div>

            <Tabs defaultValue="Appointment Types" className="w-full mt-8" onValueChange={setActiveTab}>
                <TabsList className="bg-[#F4F4F5] w-full">
                    {tabsTrigger.map((tab) => (
                        <TabsTrigger
                            key={tab}
                            value={tab}
                            // className={clsx("cursor-pointer border border-[#F4F4F5] text-xs py-1.5", activeTab === tab && "!bg-[#128576] text-white border-[#128576]")}
                            className={cn(
                                "cursor-pointer border border-[#F4F4F5] text-sm py-1.5",
                                activeTab !== tab && "text-[#A1A1AA]"
                            )}
                            onClick={() => setActiveTab(tab)}
                        >
                            {tab}
                        </TabsTrigger>
                    ))}
                </TabsList>

                <TabsContent key={activeTab} value={activeTab} className="w-full mt-4">
                    {activeTab === "Appointment Types" && <AppointmentTypes />}
                    {activeTab === "Appointment Service" && <AppointmentService />}
                    {activeTab === "Appointment Status" && <AppointmentStatus />}
                    {activeTab === "Providers" && <Providers />}
                    {activeTab === "Flags" && <Flags />}
                </TabsContent>
            </Tabs>

            <div className="bg-[#F4F4F5] py-5 px-6 rounded-xl mt-6 flex items-start justify-between">
                <div className="flex-1">
                    <h1 className="text-[#DC2626] font-normal text-base">Disconnect</h1>
                    <p className="text-[#71717A] font-light mt-2 text-sm">Disconnecting this EMR will limit certain functions. You may disconnect here or contact
                        {" "}
                        <span className="text-[#005893]">support.</span>
                    </p>
                </div>
                <div>
                    <Button className="w-[10rem]" variant="outline">
                        Disconnect
                    </Button>
                </div>

            </div>

        </div>

    )
}