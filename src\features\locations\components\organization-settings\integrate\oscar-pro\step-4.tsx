import { Input } from "@/components/ui/input"
import addLinksI from "../../images/add-links-id.png"
import { Button } from "@/components/ui/Button/Button"

export default function IntegrateOscarProStep4({
    setConnected
}: { setConnected: () => void }) {
    return (
        <div>
            <h1 className="text-[#09090B] font-medium text-lg mb-3">Step 4</h1>
            <div className="bg-[#F4F4F5] py-5 px-6 rounded-xl">
                <h1 className="text-[#27272A] font-medium text-base">
                    Fill in the information to complete adding a new client
                </h1>
                <p className="text-[#71717A] font-light mt-2">
                    Once added, under the ‘Manage Clients’ section of your EMR system (screenshot attached below), you’ll find Client Keys, Client Secret, and a URI.
                </p>
                <p className="text-[#71717A] font-light mt-2">
                    Copy the information for Migranium from each into the input fields below to merge both systems.
                </p>
                <img
                    src={addLinksI}
                    alt="Add Links"
                    className="w-full max-h-[20rem] object-contain mt-5"
                />
                <div className="flex flex-col gap-x-4 gap-y-3 mt-6">
                    <div className="flex flex-col gap-1">
                        <label htmlFor="client-id" className="text-[#27272A] font-normal text-sm">Client ID / Key</label>
                        <Input
                            type="text"
                            className="bg-white py-5"
                        />
                    </div>
                    <div className="flex flex-col gap-1">
                        <label htmlFor="client-secret" className="text-[#27272A] font-normal text-sm">
                            Client Secret
                        </label>
                        <Input
                            type="text"
                            className="bg-white py-5"
                        />
                    </div>
                    <div className="flex flex-col gap-1">
                        <label htmlFor="emr-base-url" className="text-[#27272A] font-normal text-sm">
                            EMR Base URL
                        </label>
                        <Input
                            type="url"
                            className="bg-white py-5"
                        />
                    </div>
                </div>
                <div className="flex items-center justify-end mt-6">
                    <Button className="w-[8rem]" onClick={setConnected}>
                        Add EMR
                    </Button>
                </div>
            </div>

        </div>
    )
}