import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface OccurrenceSelectorProps {
  occurrence: 'Daily' | 'Weekly' | 'Monthly' | 'Yearly';
  onOccurrenceChange: (value: 'Daily' | 'Weekly' | 'Monthly' | 'Yearly') => void;
  selectedItems: string[];
  onItemToggle: (item: string) => void;
}

export const OccurrenceSelector: React.FC<OccurrenceSelectorProps> = ({
  occurrence,
  onOccurrenceChange,
  selectedItems,
  onItemToggle
}) => {
  const occurrenceOptions = {
    Daily: ['Mon', 'Tues', 'Wed', 'Thurs', 'Fri', 'Sat', 'Sun'],
    Weekly: [
      '1st Week of the Month',
      '2nd Week of the Month',
      '3rd Week of the Month',
      '4th Week of the Month',
      'Every Week',
      'Every Other Week'
    ],
    Monthly: [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'All'
    ],
    Yearly: ['Every Year', 'Every Other Year']
  };

  const renderGrid = () => {
    const options = occurrenceOptions[occurrence];

    switch (occurrence) {
      case 'Daily':
        return (
          <div className="grid grid-cols-7 gap-2">
            {options.map((day) => (
              <Button
                key={day}
                variant={selectedItems.includes(day) ? "default" : "outline"}
                size="sm"
                className="text-center"
                onClick={() => onItemToggle(day)}
              >
                {day}
              </Button>
            ))}
          </div>
        );

      case 'Weekly':
        return (
          <div className="grid grid-cols-2 gap-2">
            {options.map((week) => (
              <Button
                key={week}
                variant={selectedItems.includes(week) ? "default" : "outline"}
                size="sm"
                className="text-center text-xs"
                onClick={() => onItemToggle(week)}
              >
                {week}
              </Button>
            ))}
          </div>
        );

      case 'Monthly':
        return (
          <div className="grid grid-cols-4 gap-2">
            {options.map((month) => (
              <Button
                key={month}
                variant={selectedItems.includes(month) ? "default" : "outline"}
                size="sm"
                className="text-center"
                onClick={() => onItemToggle(month)}
              >
                {month}
              </Button>
            ))}
          </div>
        );

      case 'Yearly':
        return (
          <div className="grid grid-cols-2 gap-2">
            {options.map((year) => (
              <Button
                key={year}
                variant={selectedItems.includes(year) ? "default" : "outline"}
                size="sm"
                className="text-center"
                onClick={() => onItemToggle(year)}
              >
                {year}
              </Button>
            ))}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex gap-x-12 items-baseline">
      <div>
        <label className="block text-sm font-medium mb-2">Select Occurrence</label>

      </div>
      <div className='flex flex-col gap-y-2  '>
        <div className='max-w-[312px] w-full'>
<Select value={occurrence} onValueChange={onOccurrenceChange as any}>
          <SelectTrigger className="w-full">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Daily">Daily</SelectItem>
            <SelectItem value="Weekly">Weekly</SelectItem>
            <SelectItem value="Monthly">Monthly</SelectItem>
            <SelectItem value="Yearly">Yearly</SelectItem>
          </SelectContent>
        </Select>
        </div>
        
        <div>
          {renderGrid()}
        </div>
      </div>


    </div>
  );
};