import { Star } from "lucide-react";

const StarRating = ({ rating = 0, totalStars = 5 }) => {
	const getStarType = (position: any) => {
		const difference = rating - position;

		if (difference >= 0) return "full";
		if (difference > -1 && difference < 0) return "half";
		return "empty";
	};

	return (
		<div className="flex gap-1">
			{[...Array(totalStars)].map((_, index) => {
				const starType = getStarType(index + 1);

				return (
					<div key={index} className="relative">
						{/* Base empty star */}
						<Star
							className="h-[13px] w-[13px]"
							color="#E9B949"
							fill="transparent"
						/>

						{/* Full or half fill */}
						{(starType === "full" || starType === "half") && (
							<div
								className={`absolute left-0 top-0 ${starType === "half" ? "w-1/2 overflow-hidden" : "w-full"}`}
							>
								<Star
									className="h-[13px] w-[13px]"
									color="#E9B949"
									fill="#E9B949"
								/>
							</div>
						)}
					</div>
				);
			})}
		</div>
	);
};

export default StarRating;
