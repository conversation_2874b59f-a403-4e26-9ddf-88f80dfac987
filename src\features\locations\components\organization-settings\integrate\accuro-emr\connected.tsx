import { ChevronDown, X } from "lucide-react"
import migranium from "../../images/migranium.png"
import acurro from "../../images/acurro.png"
import vector from "../../images/vector.png"
import { Button } from "@/components/ui/Button/Button"
import { useRef, useState } from "react"
import { Input } from "@/components/ui/input"

function SlideDown({ show, children }: { show: boolean, children: React.ReactNode }) {
    const ref = useRef<HTMLDivElement>(null);

    return (
        <div
            ref={ref}
            style={{
                maxHeight: show ? ref.current?.scrollHeight : 0,
                overflow: "hidden",
                transition: "max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
            }}
            aria-hidden={!show}
        >
            <div className={show ? "opacity-100 transition-opacity duration-300" : "opacity-0 transition-opacity duration-200"}>
                {children}
            </div>
        </div>
    );
}

export default function IntegrateAccuroEHRConnected({
    setDisconnected
}: { setDisconnected: () => void }) {
    return (
        <div>
            <div className="flex items-center justify-center flex-col text-center">
                <div className="flex items-center gap-x-7 mt-7">
                    <div className="border border-[#D4D4D8] p-2 rounded-lg">
                        <img
                            src={migranium}
                            alt="Migranium"
                            className="size-7"
                        />
                    </div>
                    <img
                        src={vector}
                        alt="Vector"
                        className="size-7"
                    />
                    <div className="rounded-lg bg-white shadow-[0px_4px_20px_0px_#9BAAB42E] p-2 scale-90">
                        <img
                            src={acurro}
                            alt="Accuro"
                            className="size-9"
                        />
                    </div>
                </div>
                <h1 className="text-[#27272A] font-medium text-lg mt-3">
                    You are <span className="text-[#34A853] font-medium text-xl">connected!</span>
                    <p className="text-[#71717A] font-light text-sm mt-1">Business UUID : #### ## ###</p>
                    <p className="text-[#71717A] font-light text-sm mt-1">Connected since: DD MM YYYY</p>
                </h1>
            </div>

            <div className="bg-[#F4F4F5] py-5 px-6 rounded-xl mt-6 flex items-start justify-between">
                <div className="flex-1">
                    <h1 className="text-[#DC2626] font-normal text-base">Disconnect</h1>
                    <p className="text-[#71717A] font-light mt-2 text-sm">Disconnecting this EMR will limit certain functions. You may disconnect here or contact
                        {" "}
                        <span className="text-[#005893]">support.</span>
                    </p>
                </div>
                <div>
                    <Button className="w-[10rem]" variant="outline" onClick={setDisconnected}>
                        Disconnect
                    </Button>
                </div>

            </div>
        </div>
    )
}