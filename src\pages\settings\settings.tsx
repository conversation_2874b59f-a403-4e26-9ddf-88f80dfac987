import React, { useEffect } from "react";
import { useUIStore } from "@/stores/uiStore";
import { SettingsTabs } from "./settingsTabs";

const Settings: React.FC = () => {
	const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);

	const setCurrentPageTitle = useUIStore(
		(state) => state.setCurrentPageTitle
	);

	useEffect(() => {
		setCurrentPageTitle("Settings");

		return () => setCurrentPageTitle(""); // optional cleanup
	}, [setCurrentPageTitle]);

	useEffect(() => {
		const breadcrumbs = [
			{
				label: "Settings",
				href: "/settings",
			},
		];

		setBreadcrumbs(breadcrumbs);

		// Cleanup breadcrumbs when component unmounts
		return () => {
			setBreadcrumbs([]);
		};
	}, [setBreadcrumbs]);

	return <SettingsTabs className="" />;
};

export default Settings;
