import React, { forwardRef, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Info } from "lucide-react";
import InfoTooltip from "./InfoTooltip";
import ProgressRing from "./ProgressRing";
import MultiProgressRing from "./MultiProgressRing";
import { Button } from "@/components/ui/button";

export interface AnalyticMetric {
	title: string;
	value: string;
	progress: number;
	colors: {
		dark: string;
		dim: string;
	};
	ratios?: Array<{ value: string | number; color: string }>;
	legend: Array<{ label: string; color: string }>;
}

interface MetricCardProps {
	metric: AnalyticMetric;
	showInfo?: boolean;
}

const MetricCard = forwardRef<HTMLDivElement, MetricCardProps>(
	({ metric, showInfo = false }, ref) => {
		return (
			<Card
				className="relative h-[130px] min-w-[250px] flex-1 overflow-visible rounded-lg border-0 bg-[#F8F9FB]"
				ref={ref}
			>
				<CardContent className="p-4">
					{showInfo && (
						<InfoTooltip
							trigger={
								<Button
									className="absolute right-3 top-2 h-[20px] w-[24px] p-0"
									variant="secondary"
								>
									<i className="mgc_information_line text-main-1" />
								</Button>
							}
						>
							Utilization Rate is the percentage of available time
							booked for appointments, calculated by dividing the
							time booked by the total available time and
							multiplying by 100.
						</InfoTooltip>
					)}
					<div className="flex items-start gap-3">
						{/* Make ProgressRing responsive */}
						<div className="flex-shrink-0">
							{/* {metric.title === "Total Visits" &&
							metric.ratios ? ( */}
							<MultiProgressRing
								ratios={metric.ratios}
								legend={metric.legend}
							/>
						</div>

						{/* Content container with better overflow handling */}
						<div className="flex min-w-0 flex-1 flex-col pt-1.5">
							<div className="flex items-center gap-2">
								{/* Value with truncation */}
								<span className="mb-0.5 truncate text-2xl font-semibold leading-8 text-gray-900 sm:text-[30px]">
									{metric.value || 0}
								</span>
							</div>

							{/* Title with truncation */}
							<span className="text-xs font-normal leading-tight text-gray-600">
								{metric.title}
							</span>

							{/* Legend with responsive layout */}
							<div className="mt-4 flex flex-wrap gap-x-4 gap-y-2">
								{metric.legend.map((item, i) => (
									<div
										key={i}
										className="flex flex-shrink-0 items-center gap-1.5"
									>
										<div
											className="h-3 w-3 flex-shrink-0 rounded"
											style={{
												backgroundColor: item.color,
											}}
										></div>
										<span className="max-w-[100px] truncate text-xs font-normal text-gray-900">
											{item.label}
										</span>
									</div>
								))}
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		);
	}
);

MetricCard.displayName = "Metric Card";

export default MetricCard;
