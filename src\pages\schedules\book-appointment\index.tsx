import {
    Sheet,
    SheetContent,
    SheetDes<PERSON>,
    Sheet<PERSON>eader,
    SheetTitle,
} from "@/components/ui/sheet"
import { useEffect, useState } from "react";
import { AddPatientSheet, type PatientFormData } from "@/components/dashboard/patient/AddPatientSheet";
import SearchPatient from "./search-patient";
import { Button } from "@/components/ui/button";
import FirstAvailable from "./mode/first-available/first-available";
import SelectDateTime from "./mode/select-date-time/select-date-time";
import AutoAssign from "./mode/auto-assign/auto-assign";
import { IoIosArrowBack } from "react-icons/io";
import UniversityForm from "./form";
import AppointmentScheduled from "./appointment-scheduled";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import type { ClientsTypes } from "../types";

type Props = {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    locationId?: string;
}

export default function BookAppointment({
    open,
    onOpenChange,
    locationId,
}: Props) {
    const [selectedPatient, setSelectedPatient] = useState<number | null>(null);
    const [selectedPatientData, setSelectedPatientData] = useState<ClientsTypes.CreatePatientResponse['data'] | null>(null);
    const [addNewPatientOpen, setAddNewPatientOpen] = useState<boolean>(false);
    const [mode, setMode] = useState<"first-available" | "date-time" | "auto-assign" | "form" | "scheduled" | null>(null);

    const handlePatientCreated = (data: PatientFormData) => {
        // This is called with form data - we'll use the API response data instead
        // This callback is kept for compatibility but the real logic is in handlePatientCreatedFromAPI
    };

    const handlePatientCreatedFromAPI = (apiResponse: any) => {
        // Map the API response to the format expected by the appointment flow
        const patientData: ClientsTypes.CreatePatientResponse['data'] = {
            id: apiResponse.data.id, // Use the real ID from the API
            external_id: '',
            first_name: apiResponse.data.first_name,
            last_name: apiResponse.data.last_name,
            full_name: `${apiResponse.data.first_name} ${apiResponse.data.last_name}`,
            email: apiResponse.data.email,
            phone_number: apiResponse.data.phone_number,
            profile_picture_url: null, // API doesn't return this in the current response structure
            is_active: true,
            last_visit: null,
            emr_sync_status: null,
            attribute_values: []
        };

        // Close the add patient drawer and store the patient data
        setAddNewPatientOpen(false);
        setSelectedPatient(patientData.id);
        setSelectedPatientData(patientData);

        // Small delay to ensure the add patient drawer closes before reopening appointment drawer
        setTimeout(() => {
            onOpenChange(true); // Reopen the appointment drawer
            setMode("first-available"); // Set mode to continue with appointment flow
        }, 100);
    };

    const handleAddNewPatientClick = () => {
        // Close the appointment drawer and open the add patient drawer
        onOpenChange(false);
        setAddNewPatientOpen(true);
    };

    const handlePatientSelected = (patient: ClientsTypes.GetPatientsResponse['data'][0]) => {
        // Store the selected patient data for consistent UI display
        setSelectedPatientData({
            id: patient.id,
            external_id: patient.external_id,
            first_name: patient.first_name,
            last_name: patient.last_name,
            full_name: patient.full_name,
            email: patient.email,
            phone_number: patient.phone_number,
            profile_picture_url: patient.profile_picture_url,
            is_active: patient.is_active,
            last_visit: patient.last_visit,
            emr_sync_status: patient.emr_sync_status,
            attribute_values: []
        });
    };

    const tabs = [
        { value: "first-available", label: "First Available Option" },
        { value: "date-time", label: "Select Date & Time" },
        { value: "auto-assign", label: "Auto Assign" },
    ];

    return (
        <>
            <Sheet open={open} onOpenChange={() => {
                onOpenChange(!open)
                setSelectedPatient(null)
                setSelectedPatientData(null)
                setMode(null)
            }}>
                <SheetContent className="z-[1003] py-5 px-2 sm:max-w-[780px] overflow-scroll scrollbar-hide">

                    <SheetHeader className="border-b border-[#E4E4E7]">
                        <SheetTitle className="text-2xl mb-1">
                            Schedule an Appointment
                        </SheetTitle>
                        <SheetDescription>
                            {selectedPatient === null ? "Select Patient, Provider and Service to Book an Appointment" : "Add the customer's details to schedule an appointment."}
                        </SheetDescription>
                    </SheetHeader>

                    {mode === null && (
                        <div className="flex items-center justify-between mx-5">
                            <h1 className="font-medium text-xl">Select Patient</h1>
                            <Button
                                type="button"
                                className="cursor-pointer py-5 font-normal"
                                onClick={handleAddNewPatientClick}
                            >
                                Add New Patient
                            </Button>
                        </div>
                    )}

                    {(selectedPatient !== null && mode !== null) ? ( // if a patient is selected and a mode is set(which will always be if the patient button is double clicked), render this
                        <div className="px-3">
                            {mode !== "scheduled" && (
                                <div className="flex items-start gap-x-2 mb-6">
                                    <Button
                                        variant="outline"
                                        className="cursor-pointer size-8"
                                        onClick={() => {
                                            setSelectedPatient(null);
                                            setSelectedPatientData(null);
                                            setMode(null);
                                        }}
                                    >
                                        <IoIosArrowBack />
                                    </Button>

                                    <div
                                        className={"w-full border-[1px] py-3.5 px-4 rounded-[10px] flex items-start justify-between transition-all duration-300"}
                                        tabIndex={0}
                                    >
                                        <div className="flex items-center justify-center gap-x-3">
                                            <div className="size-12 bg-[#E4E4E7] text-[#A1A1AA] rounded-full grid place-content-center">
                                                {selectedPatientData ? (
                                                    `${selectedPatientData.first_name?.[0] || ''}${selectedPatientData.last_name?.[0] || ''}`
                                                ) : (
                                                    'AB'
                                                )}
                                            </div>
                                            <div>
                                                <h1 className="text-[#27272A] text-base">
                                                    {selectedPatientData ? `${selectedPatientData.first_name} ${selectedPatientData.last_name}` : 'Thomas Edison'}
                                                </h1>
                                                <p className="text-[#71717A] text-sm font-light">
                                                    {selectedPatientData?.email || '<EMAIL>'}
                                                </p>
                                            </div>
                                        </div>
                                        <h1 className="text-[#27272A] font-medium text-xl">
                                            #{selectedPatient}
                                        </h1>
                                    </div>
                                </div>
                            )}
                            {mode === "form" ? ( // if users has gotten to the university form step render this
                                <UniversityForm
                                    back={() => { setSelectedPatient(null); setSelectedPatientData(null); setMode(null); }}
                                    next={() => { setMode("scheduled") }}
                                />
                            ) : mode === "scheduled" ? ( // if the appointment is successfully created render this
                                <AppointmentScheduled

                                />
                            ) : ( // else render other tabs on the user interface
                                <Tabs
                                    items={tabs}
                                    useRouting={true}
                                    searchParamKey="book-appointment-tab"
                                    defaultTab="first-available">
                                    <TabsContent value="first-available">
                                        <FirstAvailable
                                            back={() => { setSelectedPatient(null); setSelectedPatientData(null); setMode(null); }}
                                            next={() => setMode("form")}
                                            locationId={locationId}
                                        />
                                    </TabsContent>
                                    <TabsContent value="date-time">
                                        <SelectDateTime
                                            back={() => { setSelectedPatient(null); setSelectedPatientData(null); setMode(null) }}
                                            next={() => setMode("form")}
                                            locationId={locationId}
                                        />
                                    </TabsContent>
                                    <TabsContent value="auto-assign">
                                        <AutoAssign
                                            back={() => { setSelectedPatient(null); setSelectedPatientData(null); setMode(null); }}
                                            next={() => setMode("form")}
                                            locationId={locationId}
                                        />
                                    </TabsContent>
                                </Tabs>
                            )}
                        </div>
                    ) // else render the search patient ui
                        : <SearchPatient
                            selectedPatient={selectedPatient}
                            setSelectedPatient={(e) => {
                                setSelectedPatient?.(e)
                            }}
                            onDoubleClick={() => setMode("first-available")}
                            onPatientSelected={handlePatientSelected}
                        />
                    }

                </SheetContent>
            </Sheet>

            {/* Separate AddPatientSheet - no longer nested */}
            <AddPatientSheet
                open={addNewPatientOpen}
                onOpenChange={(open) => {
                    setAddNewPatientOpen(open);
                    // If closing without creating a patient, reopen the appointment drawer
                    if (!open && !selectedPatient) {
                        onOpenChange(true);
                    }
                }}
                onSubmit={handlePatientCreated}
                onPatientCreated={handlePatientCreatedFromAPI}
            />
        </>
    )
}