// Complete dummy data matching the API response structure
export const dummyAnalyticsData = {
  meta_data: {
    locations: [
      "Covenant University Ota",
      "North York City Center Medical Clinic",
      "Downtown Medical Center",
      "Westside Health Clinic"
    ],
    stations: [
      "Station 1",
      "Station 2", 
      "Station 3",
      "Station 4"
    ],
    services: [
      "General Consultation",
      "Specialist Consultation", 
      "Laboratory Tests",
      "Imaging Services",
      "Pharmacy Services"
    ],
    categories: [
      "DOB",
      "DOB2",
      "Emergency",
      "Routine",
      "Follow-up"
    ],
    status: [
      "cancelled",
      "walk_in", 
      "scheduled",
      "no_show"
    ],
    hours: [
      "09:00 AM",
      "10:00 AM",
      "11:00 AM", 
      "12:00 PM",
      "01:00 PM",
      "02:00 PM",
      "03:00 PM",
      "04:00 PM"
    ],
    days: [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday", 
      "Thursday",
      "Friday",
      "Saturday"
    ]
  },
  data: {
    summary: {
      total_visits: {
        total: 1247,
        returning: 892,
        new: 355
      },
      average_time_spent: {
        total_average: 45,
        service_average: 25,
        waiting_average: 20
      },
      cancellation_rate: {
        percentage: 12.5,
        by_admin: 8.2,
        by_user: 4.3
      },
      operational_status: 87.3,
      average_rating: {
        average: 4.2,
        review_count: 156
      }
    },
	statics: {
		location_visit_summary: {
        total: 1247,
			data: [
				{
            name: "Covenant University Ota",
            no_show: 45,
            cancelled: 23,
            scheduled: 156,
            walk_in: 34
				},
				{
					name: "North York City Center Medical Clinic",
            no_show: 32,
            cancelled: 18,
            scheduled: 234,
            walk_in: 28
          },
          {
            name: "Downtown Medical Center",
            no_show: 28,
            cancelled: 15,
            scheduled: 189,
            walk_in: 22
          },
          {
            name: "Westside Health Clinic",
            no_show: 38,
            cancelled: 20,
            scheduled: 167,
            walk_in: 31
          },
          {
            name: "Eastside Medical Center",
            no_show: 25,
            cancelled: 12,
            scheduled: 145,
            walk_in: 18
          },
          {
            name: "Central Health Clinic",
            no_show: 30,
            cancelled: 16,
            scheduled: 178,
            walk_in: 25
          },
          {
            name: "Southside Medical Center",
            no_show: 35,
            cancelled: 19,
            scheduled: 192,
            walk_in: 28
          },
          {
            name: "Northside Health Clinic",
            no_show: 40,
            cancelled: 22,
            scheduled: 203,
            walk_in: 32
          }
        ]
		},
		service_visit_summary: {
        total: 1247,
			data: [
				{
            name: "General Consultation",
            no_show: 25,
            cancelled: 12,
            scheduled: 234,
            walk_in: 18
          },
          {
            name: "Specialist Consultation",
            no_show: 18,
            cancelled: 8,
            scheduled: 189,
            walk_in: 15
          },
          {
            name: "Laboratory Tests",
            no_show: 12,
            cancelled: 6,
            scheduled: 156,
            walk_in: 12
          },
          {
            name: "Imaging Services",
            no_show: 8,
            cancelled: 4,
            scheduled: 98,
            walk_in: 8
          },
          {
            name: "Pharmacy Services",
            no_show: 15,
            cancelled: 7,
            scheduled: 123,
            walk_in: 10
          }
        ]
		},
		category_visit_summary: {
        total: 1247,
			data: [
				{
					name: "DOB",
            no_show: 20,
            cancelled: 10,
            scheduled: 189,
            walk_in: 15
          },
          {
            name: "DOB2",
            no_show: 15,
            cancelled: 8,
            scheduled: 156,
            walk_in: 12
          },
          {
            name: "Emergency",
            no_show: 8,
            cancelled: 4,
            scheduled: 98,
            walk_in: 8
          },
          {
            name: "Routine",
            no_show: 25,
            cancelled: 12,
            scheduled: 234,
            walk_in: 18
          },
          {
            name: "Follow-up",
            no_show: 18,
            cancelled: 9,
            scheduled: 167,
            walk_in: 14
          }
        ]
		},
		location_time_spent_summary: {
        total_average: 45,
			data: [
				{
            id: 1,
            name: "Covenant University Ota",
            total: 48,
            serving: 28,
            waiting: 20
          },
          {
            id: 2,
					name: "North York City Center Medical Clinic",
            total: 42,
            serving: 25,
            waiting: 17
          },
          {
            id: 3,
            name: "Downtown Medical Center",
            total: 51,
            serving: 30,
            waiting: 21
          },
          {
            id: 4,
            name: "Westside Health Clinic",
            total: 39,
            serving: 22,
            waiting: 17
          }
        ]
		},
		service_time_spent_summary: {
        total_average: 45,
			data: [
          {
            id: 1,
            name: "General Consultation",
            total: 35,
            serving: 20,
            waiting: 15
          },
				{
					id: 2,
            name: "Specialist Consultation",
            total: 60,
            serving: 40,
            waiting: 20
				},
				{
					id: 3,
            name: "Laboratory Tests",
            total: 25,
            serving: 15,
            waiting: 10
				},
				{
					id: 4,
            name: "Imaging Services",
            total: 75,
            serving: 45,
            waiting: 30
				},
				{
					id: 5,
            name: "Pharmacy Services",
            total: 20,
            serving: 10,
            waiting: 10
          }
        ]
		},
		category_time_spent_summary: {
        total_average: 45,
			data: [
				{
            id: 1,
					name: "DOB",
            total: 40,
            serving: 25,
            waiting: 15
          },
          {
            id: 2,
            name: "DOB2",
            total: 35,
            serving: 20,
            waiting: 15
          },
          {
            id: 3,
            name: "Emergency",
            total: 55,
            serving: 35,
            waiting: 20
          },
          {
            id: 4,
            name: "Routine",
            total: 30,
            serving: 18,
            waiting: 12
          },
          {
            id: 5,
            name: "Follow-up",
            total: 45,
            serving: 28,
            waiting: 17
          }
        ]
		},
		service_usage_by_client_category: {
        total: 1247,
			data: [
				{
            name: "General Consultation",
            total: 289,
					categories: {
              DOB: 89,
              DOB2: 67,
              Emergency: 45,
              Routine: 56,
              "Follow-up": 32
            }
          },
          {
            name: "Specialist Consultation",
            total: 230,
					categories: {
              DOB: 67,
              DOB2: 45,
              Emergency: 34,
              Routine: 45,
              "Follow-up": 39
            }
          },
          {
            name: "Laboratory Tests",
            total: 186,
					categories: {
              DOB: 56,
              DOB2: 34,
              Emergency: 23,
              Routine: 34,
              "Follow-up": 39
            }
          },
          {
            name: "Imaging Services",
            total: 118,
					categories: {
              DOB: 34,
              DOB2: 23,
              Emergency: 12,
              Routine: 23,
              "Follow-up": 26
            }
          },
          {
            name: "Pharmacy Services",
            total: 150,
					categories: {
              DOB: 45,
              DOB2: 23,
              Emergency: 12,
              Routine: 34,
              "Follow-up": 36
            }
          }
        ]
		},
		station_usage_by_client_category: {
        total: 1247,
			data: [
				{
					name: "Station 1",
            total: 312,
					categories: {
              DOB: 89,
              DOB2: 67,
              Emergency: 45,
              Routine: 56,
              "Follow-up": 55
            }
				},
				{
					name: "Station 2",
            total: 298,
					categories: {
              DOB: 78,
              DOB2: 56,
              Emergency: 34,
              Routine: 67,
              "Follow-up": 63
            }
          },
          {
            name: "Station 3",
            total: 289,
					categories: {
              DOB: 67,
              DOB2: 78,
              Emergency: 23,
              Routine: 56,
              "Follow-up": 65
            }
          },
          {
            name: "Station 4",
            total: 348,
					categories: {
              DOB: 89,
              DOB2: 67,
              Emergency: 45,
              Routine: 78,
              "Follow-up": 69
            }
          }
        ]
		},
		location_visits_by_service_type: {
        total: 1247,
			data: [
				{
            name: "Covenant University Ota",
            total: 258,
					services: {
              "General Consultation": 67,
              "Specialist Consultation": 45,
              "Laboratory Tests": 34,
              "Imaging Services": 23,
              "Pharmacy Services": 89
            }
          },
          {
            name: "North York City Center Medical Clinic",
            total: 312,
					services: {
              "General Consultation": 78,
              "Specialist Consultation": 67,
              "Laboratory Tests": 45,
              "Imaging Services": 34,
              "Pharmacy Services": 88
            }
          },
          {
            name: "Downtown Medical Center",
            total: 254,
					services: {
              "General Consultation": 67,
              "Specialist Consultation": 56,
              "Laboratory Tests": 34,
              "Imaging Services": 23,
              "Pharmacy Services": 74
            }
          },
          {
            name: "Westside Health Clinic",
            total: 223,
					services: {
              "General Consultation": 56,
              "Specialist Consultation": 45,
              "Laboratory Tests": 23,
              "Imaging Services": 12,
              "Pharmacy Services": 87
            }
          }
        ]
      },
      station_visits_by_service_type: {
        total: 1247,
        data: [
          {
            name: "Station 1",
            total: 312,
					services: {
              "General Consultation": 78,
              "Specialist Consultation": 67,
              "Laboratory Tests": 45,
              "Imaging Services": 34,
              "Pharmacy Services": 88
            }
          },
          {
            name: "Station 2",
            total: 298,
					services: {
              "General Consultation": 67,
              "Specialist Consultation": 56,
              "Laboratory Tests": 34,
              "Imaging Services": 23,
              "Pharmacy Services": 118
            }
          },
          {
            name: "Station 3",
            total: 289,
					services: {
              "General Consultation": 67,
              "Specialist Consultation": 56,
              "Laboratory Tests": 34,
              "Imaging Services": 23,
              "Pharmacy Services": 109
            }
          },
          {
            name: "Station 4",
            total: 348,
					services: {
              "General Consultation": 77,
              "Specialist Consultation": 55,
              "Laboratory Tests": 32,
              "Imaging Services": 20,
              "Pharmacy Services": 164
            }
          }
        ]
      }
    },
    trending: {
      weekly_visit_trend_by_location: {
        average: 178,
        data: [
          { name: "Sunday", scheduled: 45, walk_in: 23, cancelled: 12, no_show: 8, total: 145 },
          { name: "Monday", scheduled: 78, walk_in: 34, cancelled: 15, no_show: 10, total: 234 },
          { name: "Tuesday", scheduled: 67, walk_in: 29, cancelled: 13, no_show: 9, total: 198 },
          { name: "Wednesday", scheduled: 72, walk_in: 31, cancelled: 14, no_show: 11, total: 212 },
          { name: "Thursday", scheduled: 63, walk_in: 28, cancelled: 12, no_show: 8, total: 189 },
          { name: "Friday", scheduled: 56, walk_in: 25, cancelled: 11, no_show: 7, total: 167 },
          { name: "Saturday", scheduled: 34, walk_in: 18, cancelled: 8, no_show: 6, total: 102 }
        ]
      },
      weekly_visit_trend_by_service: {
        most_used_service_average: 58,
        most_used_service: "General Consultation",
        data: [
          { name: "Sunday", "General Consultation": 15, "Specialist Consultation": 12, "Laboratory Tests": 8, "Imaging Services": 5, "Pharmacy Services": 5, total: 45 },
          { name: "Monday", "General Consultation": 25, "Specialist Consultation": 18, "Laboratory Tests": 12, "Imaging Services": 8, "Pharmacy Services": 15, total: 78 },
          { name: "Tuesday", "General Consultation": 22, "Specialist Consultation": 15, "Laboratory Tests": 10, "Imaging Services": 7, "Pharmacy Services": 13, total: 67 },
          { name: "Wednesday", "General Consultation": 24, "Specialist Consultation": 16, "Laboratory Tests": 11, "Imaging Services": 8, "Pharmacy Services": 13, total: 72 },
          { name: "Thursday", "General Consultation": 21, "Specialist Consultation": 14, "Laboratory Tests": 9, "Imaging Services": 6, "Pharmacy Services": 13, total: 63 },
          { name: "Friday", "General Consultation": 19, "Specialist Consultation": 13, "Laboratory Tests": 8, "Imaging Services": 5, "Pharmacy Services": 11, total: 56 },
          { name: "Saturday", "General Consultation": 12, "Specialist Consultation": 8, "Laboratory Tests": 5, "Imaging Services": 3, "Pharmacy Services": 6, total: 34 }
        ]
      },
      weekly_visit_trend_by_category: {
        most_used_category_average: 52,
        most_used_category: "DOB",
        data: [
          { name: "Sunday", DOB: 12, DOB2: 8, Emergency: 6, Routine: 8, "Follow-up": 4, total: 38 },
          { name: "Monday", DOB: 22, DOB2: 15, Emergency: 10, Routine: 12, "Follow-up": 8, total: 67 },
          { name: "Tuesday", DOB: 18, DOB2: 12, Emergency: 8, Routine: 10, "Follow-up": 8, total: 56 },
          { name: "Wednesday", DOB: 20, DOB2: 13, Emergency: 9, Routine: 11, "Follow-up": 8, total: 61 },
          { name: "Thursday", DOB: 18, DOB2: 12, Emergency: 8, Routine: 10, "Follow-up": 6, total: 54 },
          { name: "Friday", DOB: 16, DOB2: 11, Emergency: 7, Routine: 9, "Follow-up": 5, total: 48 },
          { name: "Saturday", DOB: 10, DOB2: 7, Emergency: 4, Routine: 5, "Follow-up": 3, total: 29 }
        ]
      },
      hourly_visit_trend_by_location: {
        peak_hour: "10:00 AM",
        peak_hour_average: 45,
        data: [
          { name: "09:00 AM", scheduled: 8, walk_in: 4, cancelled: 2, no_show: 1, total: 23 },
          { name: "10:00 AM", scheduled: 15, walk_in: 8, cancelled: 3, no_show: 2, total: 45 },
          { name: "11:00 AM", scheduled: 13, walk_in: 7, cancelled: 2, no_show: 1, total: 38 },
          { name: "12:00 PM", scheduled: 12, walk_in: 6, cancelled: 2, no_show: 1, total: 34 },
          { name: "01:00 PM", scheduled: 10, walk_in: 5, cancelled: 2, no_show: 1, total: 29 },
          { name: "02:00 PM", scheduled: 11, walk_in: 6, cancelled: 2, no_show: 1, total: 32 },
          { name: "03:00 PM", scheduled: 10, walk_in: 5, cancelled: 2, no_show: 1, total: 28 },
          { name: "04:00 PM", scheduled: 9, walk_in: 4, cancelled: 2, no_show: 1, total: 25 }
        ]
      },
      hourly_visit_trend_by_service: {
        peak_hour: "10:00 AM",
        most_used_service_in_peak_hour: "General Consultation",
        data: [
          { name: "09:00 AM", "General Consultation": 3, "Specialist Consultation": 2, "Laboratory Tests": 1, "Imaging Services": 1, "Pharmacy Services": 1, total: 8 },
          { name: "10:00 AM", "General Consultation": 5, "Specialist Consultation": 3, "Laboratory Tests": 2, "Imaging Services": 2, "Pharmacy Services": 3, total: 15 },
          { name: "11:00 AM", "General Consultation": 4, "Specialist Consultation": 3, "Laboratory Tests": 2, "Imaging Services": 1, "Pharmacy Services": 2, total: 12 },
          { name: "12:00 PM", "General Consultation": 4, "Specialist Consultation": 2, "Laboratory Tests": 2, "Imaging Services": 1, "Pharmacy Services": 2, total: 11 },
          { name: "01:00 PM", "General Consultation": 3, "Specialist Consultation": 2, "Laboratory Tests": 1, "Imaging Services": 1, "Pharmacy Services": 2, total: 9 },
          { name: "02:00 PM", "General Consultation": 3, "Specialist Consultation": 2, "Laboratory Tests": 2, "Imaging Services": 1, "Pharmacy Services": 2, total: 10 },
          { name: "03:00 PM", "General Consultation": 3, "Specialist Consultation": 2, "Laboratory Tests": 1, "Imaging Services": 1, "Pharmacy Services": 1, total: 8 },
          { name: "04:00 PM", "General Consultation": 2, "Specialist Consultation": 2, "Laboratory Tests": 1, "Imaging Services": 1, "Pharmacy Services": 1, total: 7 }
        ]
      },
      hourly_visit_trend_by_category: {
        peak_hour: "10:00 AM",
        most_used_category_in_peak_hour: "DOB",
        data: [
          { name: "09:00 AM", DOB: 2, DOB2: 2, Emergency: 1, Routine: 1, "Follow-up": 1, total: 7 },
          { name: "10:00 AM", DOB: 4, DOB2: 3, Emergency: 2, Routine: 2, "Follow-up": 2, total: 13 },
          { name: "11:00 AM", DOB: 3, DOB2: 3, Emergency: 2, Routine: 2, "Follow-up": 1, total: 11 },
          { name: "12:00 PM", DOB: 3, DOB2: 2, Emergency: 2, Routine: 2, "Follow-up": 1, total: 10 },
          { name: "01:00 PM", DOB: 2, DOB2: 2, Emergency: 1, Routine: 2, "Follow-up": 1, total: 8 },
          { name: "02:00 PM", DOB: 3, DOB2: 2, Emergency: 1, Routine: 2, "Follow-up": 1, total: 9 },
          { name: "03:00 PM", DOB: 2, DOB2: 2, Emergency: 1, Routine: 1, "Follow-up": 1, total: 7 },
          { name: "04:00 PM", DOB: 2, DOB2: 1, Emergency: 1, Routine: 1, "Follow-up": 1, total: 6 }
        ]
      }
    },
    rating: {
      rating_by_station: {
        summary: {
          average: 4.2,
          count: 156,
          participant_percent: 78.5,
          distribution: {
            "1": 5,
            "2": 8,
            "3": 15,
            "4": 45,
            "5": 83
          }
        },
        by_station: [
          {
            name: "Station 1",
            average: 4.3,
            count: 42,
            participant_percent: 82.1,
            distribution: { "1": 1, "2": 2, "3": 4, "4": 12, "5": 23 }
          },
          {
            name: "Station 2",
            average: 4.1,
            count: 38,
            participant_percent: 76.2,
            distribution: { "1": 2, "2": 3, "3": 5, "4": 11, "5": 17 }
          },
          {
            name: "Station 3",
            average: 4.4,
            count: 41,
            participant_percent: 80.5,
            distribution: { "1": 1, "2": 2, "3": 3, "4": 12, "5": 23 }
          },
          {
            name: "Station 4",
            average: 4.0,
            count: 35,
            participant_percent: 75.0,
            distribution: { "1": 1, "2": 1, "3": 3, "4": 10, "5": 20 }
          }
        ]
      },
      rating_by_location: {
        summary: {
          average: 4.2,
          count: 156,
          participant_percent: 78.5,
          distribution: {
            "1": 5,
            "2": 8,
            "3": 15,
            "4": 45,
            "5": 83
          }
        },
        by_location: {
          "Covenant University Ota": {
            average: 4.3,
            count: 42,
            participant_percent: 82.1,
            distribution: { "1": 1, "2": 2, "3": 4, "4": 12, "5": 23 }
          },
          "North York City Center Medical Clinic": {
            average: 4.1,
            count: 38,
            participant_percent: 76.2,
            distribution: { "1": 2, "2": 3, "3": 5, "4": 11, "5": 17 }
          },
          "Downtown Medical Center": {
            average: 4.4,
            count: 41,
            participant_percent: 80.5,
            distribution: { "1": 1, "2": 2, "3": 3, "4": 12, "5": 23 }
          },
          "Westside Health Clinic": {
            average: 4.0,
            count: 35,
            participant_percent: 75.0,
            distribution: { "1": 1, "2": 1, "3": 3, "4": 10, "5": 20 }
          }
        }
      },
      rating_by_service: {
        summary: {
          average: 4.2,
          count: 156,
          participant_percent: 78.5,
          distribution: {
            "1": 5,
            "2": 8,
            "3": 15,
            "4": 45,
            "5": 83
          }
        },
        by_service: [
          {
            name: "General Consultation",
            average: 4.3,
            count: 45,
            participant_percent: 82.1,
            distribution: { "1": 1, "2": 2, "3": 4, "4": 12, "5": 26 }
          },
          {
            name: "Specialist Consultation",
            average: 4.1,
            count: 38,
            participant_percent: 76.2,
            distribution: { "1": 2, "2": 3, "3": 5, "4": 11, "5": 17 }
          },
          {
            name: "Laboratory Tests",
            average: 4.4,
            count: 41,
            participant_percent: 80.5,
            distribution: { "1": 1, "2": 2, "3": 3, "4": 12, "5": 23 }
          },
          {
            name: "Imaging Services",
            average: 4.0,
            count: 35,
            participant_percent: 75.0,
            distribution: { "1": 1, "2": 1, "3": 3, "4": 10, "5": 20 }
          },
          {
            name: "Pharmacy Services",
            average: 4.2,
            count: 37,
            participant_percent: 77.5,
            distribution: { "1": 1, "2": 2, "3": 3, "4": 11, "5": 20 }
          }
        ]
      },
      rating_by_category: {
        summary: {
          average: 4.2,
          count: 156,
          participant_percent: 78.5,
          distribution: {
            "1": 5,
            "2": 8,
            "3": 15,
            "4": 45,
            "5": 83
          }
        },
        by_category: [
          {
            name: "DOB",
            average: 4.3,
            count: 45,
            participant_percent: 82.1,
            distribution: { "1": 1, "2": 2, "3": 4, "4": 12, "5": 26 }
          },
          {
            name: "DOB2",
            average: 4.1,
            count: 38,
            participant_percent: 76.2,
            distribution: { "1": 2, "2": 3, "3": 5, "4": 11, "5": 17 }
          },
          {
            name: "Emergency",
            average: 4.4,
            count: 41,
            participant_percent: 80.5,
            distribution: { "1": 1, "2": 2, "3": 3, "4": 12, "5": 23 }
          },
          {
            name: "Routine",
            average: 4.0,
            count: 35,
            participant_percent: 75.0,
            distribution: { "1": 1, "2": 1, "3": 3, "4": 10, "5": 20 }
          },
          {
            name: "Follow-up",
            average: 4.2,
            count: 37,
            participant_percent: 77.5,
            distribution: { "1": 1, "2": 2, "3": 3, "4": 11, "5": 20 }
          }
        ]
      }
    }
  }
};

// Keep the old statistics export for backward compatibility
export const statistics = {
  statics: dummyAnalyticsData.data.statics
};

// Dummy data for filter options
export const dummyFilterData = {
  services: [
    { id: 1, name: "General Consultation" },
    { id: 2, name: "Specialist Consultation" },
    { id: 3, name: "Laboratory Tests" },
    { id: 4, name: "Imaging Services" },
    { id: 5, name: "Pharmacy Services" }
  ],
  statuses: [
    { value: "cancelled", label: "Cancelled" },
    { value: "walk_in", label: "Walk In" },
    { value: "scheduled", label: "Scheduled" },
    { value: "no_show", label: "No Show" }
  ],
  locations: [
    { id: 1, name: "Covenant University Ota", stations: [
      { id: 1, name: "Station 1" },
      { id: 2, name: "Station 2" }
    ]},
    { id: 2, name: "North York City Center Medical Clinic", stations: [
      { id: 3, name: "Station 3" },
      { id: 4, name: "Station 4" }
    ]},
    { id: 3, name: "Downtown Medical Center", stations: [
      { id: 5, name: "Station 5" },
      { id: 6, name: "Station 6" }
    ]},
    { id: 4, name: "Westside Health Clinic", stations: [
      { id: 7, name: "Station 7" },
      { id: 8, name: "Station 8" }
    ]}
  ]
};
