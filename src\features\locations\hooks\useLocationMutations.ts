import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { locationsApi } from "../api";
import { queryKeys } from "@/lib/query/keys";
import { useOrganizationContext } from "@/features/organizations/context";
import type { CreateLocationRequest, UpdateLocationRequest } from "../types";
import { mapServerErrorsToForm } from "@/lib/utils/form-errors";
import type { FieldValues, UseFormSetError } from "react-hook-form";

interface MutationOptions<T extends FieldValues> {
	onSuccess?: (data: any) => void;
	onError?: (error: any, setError?: UseFormSetError<T>) => void;
	setError?: UseFormSetError<T>;
}

export const useCreateLocation = <T extends FieldValues>(
	options?: MutationOptions<T>
) => {
	const queryClient = useQueryClient();
	const { organizationId } = useOrganizationContext();

	return useMutation({
		mutationFn: (data: CreateLocationRequest) =>
			locationsApi.createLocation(data, organizationId!),
		onSuccess: (newLocation) => {
			// Invalidate and refetch locations list for current organization
			queryClient.invalidateQueries({
				queryKey: [...queryKeys.locations.lists(), organizationId],
			});

			// Add the new location to existing cache if it exists
			queryClient.setQueryData(
				[...queryKeys.locations.detail(newLocation.id), organizationId],
				newLocation
			);

			toast.success("Location created successfully");
			options?.onSuccess?.(newLocation);
		},
		onError: (error: any) => {
			// Try to handle form validation errors first
			if (options?.setError && mapServerErrorsToForm(error, options.setError)) {
				// Form errors were handled, don't show toast
				return;
			}

			// Call custom error handler if provided
			if (options?.onError) {
				options.onError(error, options.setError);
				return;
			}

			// Fallback to generic toast
			toast.error(error?.message || "Failed to create location");
		},
	});
};

export const useUpdateLocation = <T extends FieldValues>(
	options?: MutationOptions<T>
) => {
	const queryClient = useQueryClient();
	const { organizationId } = useOrganizationContext();

	return useMutation({
		mutationFn: (data: UpdateLocationRequest) =>
			locationsApi.updateLocation(data, organizationId!),
		onSuccess: (updatedLocation) => {
			// Update specific location cache
			queryClient.setQueryData(
				[...queryKeys.locations.detail(updatedLocation.id), organizationId],
				updatedLocation
			);

			// Invalidate locations list to reflect changes
			queryClient.invalidateQueries({
				queryKey: [...queryKeys.locations.lists(), organizationId],
			});

			toast.success("Location updated successfully");
			options?.onSuccess?.(updatedLocation);
		},
		onError: (error: any) => {
			// Try to handle form validation errors first
			if (options?.setError && mapServerErrorsToForm(error, options.setError)) {
				// Form errors were handled, don't show toast
				return;
			}

			// Call custom error handler if provided
			if (options?.onError) {
				options.onError(error, options.setError);
				return;
			}

			// Fallback to generic toast
			toast.error(error?.message || "Failed to update location");
		},
	});
};

export const useDeleteLocation = () => {
	const queryClient = useQueryClient();
	const { organizationId } = useOrganizationContext();

	return useMutation({
		mutationFn: (id: string) => locationsApi.deleteLocation(id, organizationId!),
		onSuccess: (_, deletedId) => {
			// Remove from cache
			queryClient.removeQueries({
				queryKey: [...queryKeys.locations.detail(deletedId), organizationId],
			});

			// Invalidate locations list for current organization
			queryClient.invalidateQueries({
				queryKey: [...queryKeys.locations.lists(), organizationId],
			});

			toast.success("Location deleted successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to delete location");
		},
	});
};

export const useToggleLocationStatus = () => {
	const queryClient = useQueryClient();
	const { organizationId } = useOrganizationContext();

	return useMutation({
		mutationFn: ({ id, isActive }: { id: string; isActive: boolean }) =>
			locationsApi.toggleLocationStatus(id, isActive, organizationId!),
		onSuccess: (updatedLocation) => {
			// Update specific location cache
			queryClient.setQueryData(
				[...queryKeys.locations.detail(updatedLocation.id), organizationId],
				updatedLocation
			);

			// Invalidate locations list for current organization
			queryClient.invalidateQueries({
				queryKey: [...queryKeys.locations.lists(), organizationId],
			});

			toast.success(
				`Location ${updatedLocation.isActive ? "activated" : "deactivated"} successfully`
			);
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update location status");
		},
	});
};
