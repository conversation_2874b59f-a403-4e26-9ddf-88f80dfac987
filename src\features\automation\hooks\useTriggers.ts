import { useQuery } from "@tanstack/react-query"
import { triggerApi, type TriggerTypesData } from "../api/trigger"
import { useOrganizationContext } from "@/features/organizations/context"

export function useGetTriggers({
    business_id,
    active,
    is_system,
    enabled
}: {
    business_id: string,
    active?: boolean,
    is_system?: boolean,
    enabled: boolean,
}) {
    const organizationId = useOrganizationContext()?.organizationId
    return useQuery<{
        success: boolean,
        data: TriggerTypesData[],
    }
    >({
        queryKey: ["triggers", organizationId, business_id, active, is_system],
        queryFn: () => {
            if (!business_id || !organizationId) {
                throw new Error("Business ID and Organization ID are required")
            }
            return triggerApi.getTriggerTypes(organizationId, business_id, active, is_system)
        },
        enabled
    })
}
