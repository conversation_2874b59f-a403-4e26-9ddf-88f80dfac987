import { Card, CardContent } from "@/components/ui/card";
import { FormTypes, FormResponseTypes } from "../types";
import {
	AgreeDisagreeFieldRenderer,
	AttachmentFieldRenderer,
	Checkbox<PERSON>ield<PERSON><PERSON>er,
	DateField<PERSON><PERSON>er,
	DateRange<PERSON>ield<PERSON><PERSON>er,
	DropdownField<PERSON><PERSON>er,
	InfoTextFieldRenderer,
	InfoImageFieldRenderer,
	SatisfactionRatingFieldRenderer,
	ScaleFieldRenderer,
	RadioFieldRenderer,
	TextFieldRenderer,
} from "./renderers";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { getVariantColor } from "../utils/formHelpers";

interface FormRendererProps {
	formData: FormTypes.FormDataType;
	responses?: FormResponseTypes.FormResponsesData;
	mode: "preview" | "view" | "edit";
	onResponseChange?: (fieldId: string, value: any) => void;
	onSubmit?: (responses: FormResponseTypes.FormResponse[]) => void;
}

// Map field types to renderer components
const fieldRendererMap: Record<string, React.FC<any>> = {
	text: TextFieldRenderer,
	longtext: TextFieldRenderer,
	numeric: TextFieldRenderer,
	date: DateFieldRenderer,
	date_range: DateRangeFieldRenderer,
	dropdown: DropdownFieldRenderer,
	radio: RadioFieldRenderer,
	checkbox: CheckboxFieldRenderer,
	attachment: AttachmentFieldRenderer,
	info_image: InfoImageFieldRenderer,
	info_text: InfoTextFieldRenderer,
	scale_1_10: ScaleFieldRenderer,
	satisfaction_scale: SatisfactionRatingFieldRenderer,
	agree_disagree: AgreeDisagreeFieldRenderer,
	yes_no: RadioFieldRenderer, // fallback to radio
	rating: SatisfactionRatingFieldRenderer, // fallback
};

// Helper to get the value for a field from responses
function getFieldValue(
	fieldId: string,
	responses: FormResponseTypes.FormResponsesData = []
) {
	// Flatten all sections and their fields from all response data
	const allFields = responses?.flatMap((responseData) => responseData.fields);

	// Find the field with matching field_id
	const field = allFields.find((field) => field.field_id === fieldId);

	// Return the raw_value if found, otherwise undefined
	return field?.raw_value ?? undefined;
}

// function getFieldValue(
// 	fieldId: string,
// 	responses: FormResponseTypes.FormResponsesData = []
// ): FormResponseTypes.FieldValue {
// 	for (const response of responses) {
// 		if (response.fields) {
// 			const match = response.fields.find((r) => r.field_id === fieldId);
// 			if (match) return match.raw_value;
// 		}
// 	}
// 	return null;
// }

export const FormRenderer: React.FC<FormRendererProps> = ({
	formData,
	responses = [],
	mode,
	onResponseChange,
	onSubmit,
}) => {
	return (
		<Card className="border-t-primary border-t-4 py-0">
			<CardContent className="space-y-6 p-6">
				<div className="flex items-center justify-between">
					<h2 className="text-3xl font-light text-slate-900">
						{formData.name}
					</h2>
					<Badge
						variant="outline"
						className={cn(
							formData?.type &&
								getVariantColor(
									formData?.type || "",
									"border-none text-xs"
								),
							"border-none text-xs"
						)}
					>
						{formData?.type || ""}
					</Badge>
				</div>
				<div className="space-y-4">
					{formData.sections.map((section) => (
						<div key={section.id} className="space-y-4">
							<h2 className="text-lg font-medium">
								{section.title}
							</h2>
							<div className="space-y-4">
								{section.fields.map((field) => {
									const Renderer =
										fieldRendererMap[field.type] ||
										(() => (
											<div>
												Unsupported field type:{" "}
												{field.type}
											</div>
										));
									const value = getFieldValue(
										field.id,
										responses
									);
									return (
										<div
											key={field.id}
											className="mb-8 space-y-2.5"
										>
											<h3 className="text-sm font-medium text-[#323539]">
												{field.title}
											</h3>
											<Renderer
												field={field}
												value={value}
												mode={mode}
												onChange={
													onResponseChange
														? (val: any) =>
																onResponseChange(
																	field.id,
																	val
																)
														: undefined
												}
											/>
										</div>
									);
								})}
							</div>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
};
