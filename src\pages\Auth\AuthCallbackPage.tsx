import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router';
import { useAuthStore } from '@/stores/authStore';
import useUserStore from '@/stores/useUserStore';
import { setCookie } from '@/lib/utils/cookies';
import useCustomToast from '@/components/CustomToast';
import { ROUTES } from '@/lib/utils/constants';
import { getProfile } from '@/lib/api/auth';
import Loader from '@/components/Loader';
import type { AuthUserData } from '@/types/signin';
import type { ProfileData } from '@/types/api/auth';


const convertProfileToAuthUser = (profileData: ProfileData, token: string): AuthUserData => {
	return {
		id: profileData.id,
		name: profileData.name,
		email: profileData.email,
		is_email_verified: profileData.is_email_verified,
		is_active: true, // Default to true for authenticated users
		company_id: '', // Default empty string
		token: token,
		expires_in: 3600, // Default 1 hour
		two_factor_enable: profileData.two_factor_enable,
		two_factor_skip: false, // Default to false
		remember_token: undefined, // Optional field
		twoFactor: false, // Default to false
	};
};

export const AuthCallbackPage: React.FC = () => {
	const [searchParams] = useSearchParams();
	const navigate = useNavigate();
	const customToast = useCustomToast();
	const { setAuth } = useAuthStore();
	const setUser = useUserStore((s: any) => s.setUser);
	const [isProcessing, setIsProcessing] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const handleCallback = async () => {
			try {
				// Get token from URL parameters
				const token = searchParams.get('token');
				
				if (!token) {
					setError('No authentication token provided');
					setIsProcessing(false);
					return;
				}

				// Set the token in auth store first so getProfile can access it
				setAuth(undefined, token, "");
				
				// Set the token in cookies for persistence
				setCookie("ac-token", token, 7);
				
				const profileResponse = await getProfile();
				
				if (profileResponse.success) {
					const userData = profileResponse.data;
					
					// Convert profile data to auth user data
					const authUserData = convertProfileToAuthUser(userData, token);
					
					// Update authentication state with full user data
					setAuth(authUserData, token, "");
					setUser(userData);
					
					// Show success message
					customToast("Authentication successful! Welcome back.", {
						id: "auth-callback-success",
						type: "success",
					});
					
					// Redirect to dashboard
					navigate(ROUTES.HOME);
				} else {
					setError(profileResponse.message || 'Authentication failed');
					setIsProcessing(false);
				}
			} catch (error: any) {
				console.error('Auth callback error:', error);
				const errorMessage = error.response?.data?.message || 'Authentication failed. Please try again.';
				setError(errorMessage);
				setIsProcessing(false);
				
				customToast(errorMessage, {
					id: "auth-callback-error",
					type: "error",
				});
			}
		};

		handleCallback();
	// }, [searchParams, navigate, customToast, setAuth, setUser]);
}, [searchParams,navigate]);

	if (error) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
				<div className="max-w-md w-full space-y-8">
					<div className="text-center">
						<h3 className="text-xl font-semibold text-gray-900 mb-4">
							Authentication Error
						</h3>
						<p className="text-gray-600 mb-6">
							{error}
						</p>
						<button
							onClick={() => navigate(ROUTES.AUTH.SIGNIN)}
							className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
						>
							Go to Sign In
						</button>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
			<div className="max-w-md w-full space-y-8">
				<div className="text-center">
					<Loader size={48} />
					<h3 className="text-xl font-semibold text-gray-900 mt-4">
						Authenticating...
					</h3>
					<p className="text-gray-600 mt-2">
						Please wait while we verify your authentication.
					</p>
				</div>
			</div>
		</div>
	);
}; 