import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, MapPin, User, Users, Calendar, Settings } from 'lucide-react';
import OrganizationIcon from '@/assets/images/planner/organization_icon.png';
import LocationIcon from '@/assets/images/planner/location_icon.png';
import StationIcon from '@/assets/images/planner/provider_icon.png'; // Assuming you have an SVG icon for organization

interface PlannerDashboardProps {
  onSelectLevel: (level: 'organization' | 'location' | 'provider') => void;
}

export const PlannerDashboard: React.FC<PlannerDashboardProps> = ({ onSelectLevel }) => {
  return (
    <div className="min-h-screen  p-6">
      <div className="max-w-7xl flex flex-col gap-y-20 mx-auto">
        {/* Header */}
        <div className="">
          <h1 className=" font-semibold text-gray-900 mb-2">Welcome to Planner</h1>
          <p className="text-[#6D748D]">
            Here, you can set schedule preferences, restrictions, and time off for your Organization, Locations, and Providers.
          </p>
        </div>

        {/* Three Main Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 min-h-[400px]">
          {/* Organization Card */}
          <Card
            className="cursor-pointer hover:shadow-lg transition-shadow duration-200  border-gray-200"
            onClick={() => onSelectLevel('organization')}
          >
            <CardHeader className="text-center ">
              <div className="flex justify-center ">
                <div className="relative">
                  <div className="flex justify-center mt-4">
                    <img src={OrganizationIcon} alt="Organization Icon" className=" text-gray-400 mb-2" />
                  </div>
                </div>
              </div>
              <CardTitle className="text-xl font-semibold">Organization Wide Preferences</CardTitle>
            </CardHeader>
            <CardContent className="">
              <CardDescription className="text-sm text-gray-600 leading-relaxed">
                Set availability preferences, time off, maximum number of services available and more across the organization.
              </CardDescription>
            </CardContent>
          </Card>

          {/* Location Card */}
          <Card
            className="cursor-pointer hover:shadow-lg transition-shadow duration-200 border-gray-200"
            onClick={() => onSelectLevel('location')}
          >
            <CardHeader className="text-center ">
              <div className="flex justify-center mb-4">
                <div className="relative">
                  <img src={LocationIcon} alt="Location Icon" className=" text-gray-400 mb-2" />
                </div>
              </div>
              <CardTitle className="text-xl font-semibold">Location Wide Preferences</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <CardDescription className="text-sm text-[#6D748D] leading-relaxed">
                Set availability preferences, time off, maximum number of services available, category preferences at a location.
              </CardDescription>
            </CardContent>
          </Card>

          {/* Provider Card */}
          <Card
            className="cursor-pointer hover:shadow-lg transition-shadow duration-200 border-gray-200"
            onClick={() => onSelectLevel('provider')}
          >
            <CardHeader className="text-center">
              <div className="flex justify-center">
                <div className="relative">
                  <div className="flex justify-center mt-4">
                    <img src={StationIcon} alt="Station Icon" className=" text-gray-400 mb-2" />
                  </div>
                </div>
              </div>
              <CardTitle className="text-xl font-semibold">Provider Preferences</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <CardDescription className="text-sm text-[#6D748D] leading-relaxed">
                Set availability preferences, time off, maximum number of services available, category preferences for Providers.
              </CardDescription>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};