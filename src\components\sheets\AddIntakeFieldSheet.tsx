import React, { useState } from "react";
import { X, Plus } from "lucide-react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { IntakeFieldTypeSelector } from "@/components/common/IntakeFieldTypeSelector";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import {
	useCreateCustomIntake,
	useCustomIntakes,
} from "@/hooks/useCustomIntakes";
import { type CreateIntakeFieldData } from "@/lib/api/customIntakesApi";
import { cn } from "@/lib/utils";

interface AddIntakeFieldSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

// Zod validation schema
const addIntakeFieldSchema = z
	.object({
		activeTab: z.enum(["existing", "new"]),
		selectedExistingField: z.string().optional(),
		fieldTitle: z.string().optional(),
		hasDescription: z.boolean(),
		description: z.string().optional(),
		fieldType: z.string().min(1, "Field type is required"),
		fieldRequirement: z.enum(["required", "optional", "only-new"]),
		applyTo: z
			.array(z.string())
			.min(1, "Must apply to at least one option"),
		allowMultiple: z.boolean(),
		longText: z.boolean(),
		numericUnitTitle: z.string().optional(),
		infoTextValue: z.string().optional(),
		approvedFormats: z.array(z.string()),
		image: z.string().optional(),
		order: z.number().min(1, "Order must be at least 1"),
		options: z.array(
			z.object({
				label: z.string(),
				order: z.number(),
			})
		),
	})
	.refine(
		(data) => {
			// Custom validation: either selectedExistingField or fieldTitle must be provided
			if (data.activeTab === "existing") {
				return (
					data.selectedExistingField &&
					data.selectedExistingField.length > 0
				);
			} else {
				return data.fieldTitle && data.fieldTitle.length > 0;
			}
		},
		{
			message: "Field name is required",
			path: ["fieldTitle"], // This will show the error on fieldTitle
		}
	);

export function AddIntakeFieldSheet({
	open,
	onOpenChange,
}: AddIntakeFieldSheetProps) {
	const { organizationId } = useOrganizationContext();
	const createIntakeMutation = useCreateCustomIntake();

	// Fetch existing custom intakes to populate existing fields dropdown
	const { data: existingIntakesResponse } = useCustomIntakes({
		organizationId: organizationId?.toString() || "",
		limit: 100, // Get a large number to show all existing fields
		offset: 0,
	});

	// React Hook Form with Zod validation
	const form = useForm({
		resolver: zodResolver(addIntakeFieldSchema),
		defaultValues: {
			activeTab: "existing" as const,
			selectedExistingField: "",
			fieldTitle: "",
			hasDescription: false,
			description: "",
			fieldType: "",
			fieldRequirement: "required" as const,
			applyTo: ["waitlist"],
			allowMultiple: false,
			longText: false,
			numericUnitTitle: "",
			infoTextValue: "",
			approvedFormats: [],
			image: "",
			order: 1,
			options: [],
		},
	});

	const {
		control,
		handleSubmit,
		formState: { errors },
		watch,
		reset,
		setValue,
		getValues,
	} = form;

	// Watch form values for conditional rendering
	const activeTab = watch("activeTab");
	const fieldType = watch("fieldType");
	const selectedExistingField = watch("selectedExistingField");
	const fieldTitle = watch("fieldTitle");
	const hasDescription = watch("hasDescription");
	const description = watch("description");
	const applyTo = watch("applyTo");

	// Local state for IntakeFieldTypeSelector
	const [localFieldTitle, setLocalFieldTitle] = useState(fieldTitle || "");
	const [localDescription, setLocalDescription] = useState(description || "");
	const [localFieldType, setLocalFieldType] = useState(fieldType || "text");
	const [localRequired, setLocalRequired] = useState(true);
	const [localOptions, setLocalOptions] = useState<
		Array<{ id: string; value: string; label: string; order: number }>
	>([]);

	// Update form values when local state changes
	const handleFieldTitleChange = (title: string) => {
		setLocalFieldTitle(title);
		setValue("fieldTitle", title);
	};

	const handleDescriptionChange = (desc: string) => {
		setLocalDescription(desc);
		setValue("description", desc);
		setValue("hasDescription", desc.length > 0);
	};

	const handleFieldTypeChange = (type: string) => {
		setLocalFieldType(type);
		setValue("fieldType", type);

		// Clear options when changing field type to non-option type
		if (!["dropdown", "radio", "checkbox"].includes(type)) {
			setLocalOptions([]);
			setValue("options", []);
		} else if (localOptions.length === 0) {
			// Add default option if switching to option-based field type
			const defaultOption = {
				id: crypto.randomUUID(),
				value: "Option 1",
				label: "Option 1",
				order: 0,
			};
			setLocalOptions([defaultOption]);
			setValue("options", [
				{ label: defaultOption.label, order: defaultOption.order },
			]);
		}
	};

	const handleRequiredChange = (required: boolean) => {
		setLocalRequired(required);
	};

	const handleOptionsChange = (
		options: Array<{
			id: string;
			value: string;
			label: string;
			order: number;
		}>
	) => {
		setLocalOptions(options);
		// Convert to the format expected by the form
		const formOptions = options.map((option) => ({
			label: option.label,
			order: option.order,
		}));
		setValue("options", formOptions);
	};

	// Get existing fields from API response
	const existingFields =
		existingIntakesResponse?.data?.map((field) => field.name) || [];

	// Handle existing field selection
	const handleExistingFieldSelection = (fieldName: string) => {
		setValue("selectedExistingField", fieldName);
		setLocalFieldTitle(fieldName);
		setValue("fieldTitle", fieldName);
	};

	const fieldTypes = [
		{ value: "text", label: "Text" },
		{ value: "rating", label: "Rating" },
		{ value: "dropdown", label: "Dropdown" },
		{ value: "checkbox", label: "Checkbox" },
		{ value: "radio", label: "Radio" },
		{ value: "scale", label: "Scale" },
		{ value: "boolean", label: "Boolean" },
	];

	const onSubmit = async (formData: any) => {
		if (!organizationId) return;

		const fieldName =
			formData.activeTab === "existing"
				? formData.selectedExistingField
				: formData.fieldTitle;

		const data: CreateIntakeFieldData = {
			name: fieldName,
			subtitle: formData.hasDescription
				? formData.description
				: undefined,
			type: formData.fieldType,
			apply_to:
				formData.applyTo.includes("waitlist") &&
				formData.applyTo.includes("schedule")
					? "all"
					: formData.applyTo.includes("waitlist")
						? "waitlist"
						: formData.applyTo.includes("schedule")
							? "schedule"
							: "all",
			field_requirement:
				formData.fieldRequirement === "required"
					? "yes"
					: formData.fieldRequirement === "optional"
						? "optional"
						: "no",
			is_visible: true,
			order: formData.order,
			// Optional fields - only include if they have values
			...(formData.allowMultiple && {
				allow_multiple: formData.allowMultiple,
			}),
			...(formData.longText && { long_text: formData.longText }),
			...(formData.numericUnitTitle && {
				numeric_unit_title: formData.numericUnitTitle,
			}),
			...(formData.infoTextValue && {
				info_text_value: formData.infoTextValue,
			}),
			...(formData.approvedFormats.length > 0 && {
				approved_formats: formData.approvedFormats,
			}),
			...(formData.image && { image: formData.image }),
			...(formData.options.length > 0 && {
				options: formData.options.map((opt: any, index: number) => ({
					label: opt.label,
					order: opt.order || index,
				})),
			}),
		};

		try {
			await createIntakeMutation.mutateAsync({
				data,
				organizationId: organizationId.toString(),
			});
			onOpenChange(false);
			// Reset form
			reset();
		} catch (error) {
			console.error("Failed to create intake field:", error);
		}
	};

	const handleCancel = () => {
		onOpenChange(false);
		reset();
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-6 py-6 sm:w-[540px] sm:max-w-[525px] [&>button]:hidden">
				<SheetHeader className="p-0 pb-6">
					<div className="flex items-center justify-between">
						<SheetTitle className="text-lg font-semibold">
							Add Intake Field
						</SheetTitle>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>
				</SheetHeader>

				<form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
					{/* Adding Type Selection */}
					<div className="space-y-3">
						<Label className="text-sm font-medium">
							I'm Adding a
						</Label>
						<Controller
							name="activeTab"
							control={control}
							render={({ field }) => (
								<RadioGroup
									value={field.value}
									onValueChange={field.onChange}
									className="flex flex-row space-x-6"
								>
									<div className="flex items-center space-x-2">
										<RadioGroupItem
											value="existing"
											id="existing-radio"
										/>
										<Label
											htmlFor="existing-radio"
											className="flex items-center gap-2 text-sm"
										>
											Existing Field
										</Label>
									</div>
									<div className="flex items-center space-x-2">
										<RadioGroupItem
											value="new"
											id="new-radio"
										/>
										<Label
											htmlFor="new-radio"
											className="flex items-center gap-2 text-sm"
										>
											New Field
										</Label>
									</div>
								</RadioGroup>
							)}
						/>
					</div>

					{/* Select Existing Fields */}
					<div className="space-y-3">
						<Label className="text-sm font-medium">
							Select Existing Fields
						</Label>
						<Controller
							name="selectedExistingField"
							control={control}
							render={({ field }) => (
								<Select
									value={field.value}
									onValueChange={(value) => {
										if (typeof value === "string") {
											field.onChange(value);
											handleExistingFieldSelection(value);
										}
									}}
									disabled={activeTab !== "existing"}
								>
									<SelectTrigger
										className={cn(
											"w-full",
											activeTab !== "existing"
												? "opacity-50"
												: "",
											errors.selectedExistingField &&
												activeTab === "existing"
												? "border-red-500"
												: ""
										)}
									>
										<SelectValue placeholder="What is your gender?" />
									</SelectTrigger>
									<SelectContent>
										{existingFields.length > 0 ? (
											existingFields.map((field) => (
												<SelectItem
													key={field}
													value={field}
												>
													{field}
												</SelectItem>
											))
										) : (
											<SelectItem
												value="no-fields-available"
												disabled
											>
												No existing fields available
											</SelectItem>
										)}
									</SelectContent>
								</Select>
							)}
						/>
						{errors.selectedExistingField &&
							activeTab === "existing" && (
								<p className="text-sm text-red-500">
									{errors.selectedExistingField.message}
								</p>
							)}
					</div>
					<div className="p-3 shadow-md">
						<IntakeFieldTypeSelector
							fieldTitle={localFieldTitle}
							fieldDescription={localDescription}
							fieldType={localFieldType}
							required={localRequired}
							options={localOptions}
							onFieldTitleChange={handleFieldTitleChange}
							onFieldDescriptionChange={handleDescriptionChange}
							onFieldTypeChange={handleFieldTypeChange}
							onRequiredChange={handleRequiredChange}
							onOptionsChange={handleOptionsChange}
							disabled={activeTab !== "new"}
						/>
					</div>

					{/* Field Requirements */}
					<div className="space-y-3">
						<Label className="text-sm font-medium">
							This Field Will Be
						</Label>
						<Controller
							name="fieldRequirement"
							control={control}
							render={({ field }) => (
								<RadioGroup
									value={field.value}
									onValueChange={field.onChange}
									className="flex flex-row space-x-6"
								>
									<div className="flex items-center space-x-2">
										<RadioGroupItem
											value="required"
											id="required"
										/>
										<Label
											htmlFor="required"
											className="text-sm"
										>
											Required
										</Label>
									</div>
									<div className="flex items-center space-x-2">
										<RadioGroupItem
											value="optional"
											id="optional"
										/>
										<Label
											htmlFor="optional"
											className="text-sm"
										>
											Optional
										</Label>
									</div>
									<div className="flex items-center space-x-2">
										<RadioGroupItem
											value="only-new"
											id="only-new"
										/>
										<Label
											htmlFor="only-new"
											className="text-sm"
										>
											Only for new visitors
										</Label>
									</div>
								</RadioGroup>
							)}
						/>
					</div>

					{/* Apply To */}
					<div className="space-y-3">
						<Label className="text-sm font-medium">
							This Field Will Apply To
						</Label>
						<Controller
							name="applyTo"
							control={control}
							render={({ field }) => (
								<div className="flex flex-row space-x-6">
									<div className="flex items-center space-x-2">
										<Checkbox
											id="waitlist"
											checked={field.value.includes(
												"waitlist"
											)}
											onCheckedChange={(checked) => {
												if (checked) {
													field.onChange([
														...field.value,
														"waitlist",
													]);
												} else {
													field.onChange(
														field.value.filter(
															(item: string) =>
																item !==
																"waitlist"
														)
													);
												}
											}}
										/>
										<Label
											htmlFor="waitlist"
											className="text-sm"
										>
											Waitlist
										</Label>
									</div>
									<div className="flex items-center space-x-2">
										<Checkbox
											id="schedule"
											checked={field.value.includes(
												"schedule"
											)}
											onCheckedChange={(checked) => {
												if (checked) {
													field.onChange([
														...field.value,
														"schedule",
													]);
												} else {
													field.onChange(
														field.value.filter(
															(item: string) =>
																item !==
																"schedule"
														)
													);
												}
											}}
										/>
										<Label
											htmlFor="schedule"
											className="text-sm"
										>
											Schedule
										</Label>
									</div>
								</div>
							)}
						/>
						{errors.applyTo && (
							<p className="text-sm text-red-500">
								{errors.applyTo.message}
							</p>
						)}
					</div>

					<SheetFooter className="mt-6 flex flex-row justify-between border-t pt-6">
						<Button
							type="button"
							variant="ghost"
							onClick={handleCancel}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							className="bg-primary hover:bg-primary/90"
						>
							{createIntakeMutation.isPending
								? "Adding..."
								: "Add Field"}
						</Button>
					</SheetFooter>
				</form>
			</SheetContent>
		</Sheet>
	);
}
