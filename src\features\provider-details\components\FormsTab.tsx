import { useEffect, useState, type FC } from "react";
import { Search, Plus, MapPin, Settings2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";
import { ServiceFilterSheet } from "@/features/locations/components/sheets";
import { useLocations } from "@/features/locations/hooks";
import type {
	LocationsFilters,
	LocationsResponse,
} from "@/features/locations/types";
import { FormsCard } from "./forms/FormsCard";
import { SendBookingLinkSheet } from "@/features/schedule";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";

interface FormsTabProps {
	className?: string;
}

export const FormsTab: FC<FormsTabProps> = ({ className }) => {
	const [filters, setFilters] = useState<LocationsFilters>({
		page: 1,
		limit: 12,
		sortBy: "name",
		sortOrder: "asc",
	});
	const [selectedServices, setSelectedServices] = useState<string[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [currentPage, setCurrentPage] = useState(1);
	const [showAddServiceForm, setShowAddServiceForm] = useState(false);
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const [showLocationDetails, setShowLocationDetails] = useState(false);
	const [selectedLocation, setSelectedLocation] = useState<Location | null>(
		null
	);
	const [showSendBookingLinkSheet, setShowSendBookingLinkSheet] =
		useState(false);

	const { organizationId } = useOrganizationContext();
	const { data: locationsData2, isLoading } = useLocations({
		page: currentPage,
		search: searchTerm,
		limit: 10,
	}, organizationId || undefined);

	// Debounced search filter
	useEffect(() => {
		const timer = setTimeout(() => {
			setFilters((prev) => ({
				...prev,
				search: searchTerm || undefined,
				page: 1,
			}));
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// Use real API data instead of mock data
	const locationsData: LocationsResponse = {
		data: locationsData2 || [],
		pagination: {
			page: currentPage,
			limit: 10,
			total: locationsData2?.length || 0,
			totalPages: Math.ceil((locationsData2?.length || 0) / 10),
		},
	};

	const handleFilterChange = (newFilters: Partial<LocationsFilters>) => {
		setFilters((prev) => ({ ...prev, ...newFilters, page: 1 }));
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked && locationsData?.data) {
			setSelectedServices(
				locationsData.data.map((location) => location.id)
			);
		} else {
			setSelectedServices([]);
		}
	};

	const handleLocationSelection = (locationId: string, selected: boolean) => {
		if (selected) {
			setSelectedServices((prev) => [...prev, locationId]);
		} else {
			setSelectedServices((prev) =>
				prev.filter((id) => id !== locationId)
			);
		}
	};

	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	const handleAddService = async (data: any) => {
		console.log("Adding new service:", data);
		setShowAddServiceForm(false);
	};

	const handleViewLocation = (location: Location) => {
		setSelectedLocation(location);
		setShowLocationDetails(true);
	};

	const handleApplyFilters = (filterData: any) => {
		console.log("Applying filters:", filterData);
	};

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-3 pl-4">
				<h1 className="text-2xl font-bold">Forms</h1>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
					<Button
						variant="outline"
						className="cursor-pointer"
						size="icon"
						onClick={() => setShowFilterSheet(true)}
					>
						<Settings2 className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						className="bg-primary hover:bg-primary/90 cursor-pointer text-white hover:text-white"
						onClick={() => setShowAddServiceForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Create a Form
					</Button>
				</div>
			</div>

			{/* Table */}
			<div className="flex w-full flex-col overflow-hidden rounded-lg border border-zinc-200">
				<div className="text-muted flex h-12 items-center justify-between border-b py-1 pl-4">
					<div className="flex items-center pr-4">
						<Checkbox
							label=""
							checked={
								selectedServices.length ===
								locationsData?.data?.length &&
								locationsData?.data?.length > 0
							}
							className="cursor-pointer"
							onCheckedChange={handleSelectAll}
						/>
					</div>
					<div className="flex flex-2 items-center px-3">
						<div className="flex items-center gap-3 text-[#71717A]">
							<p>Form Name</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3 text-[#71717A]">
							<p>Status</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3 text-[#71717A]">
							<p>Service Name</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3 text-[#71717A]">
							<p>Submissions</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3 text-[#71717A]">
							<p>Type</p>
						</div>
					</div>
					<div className="flex flex-1 items-center px-3">
						<div className="flex items-center gap-3 text-[#71717A]">
							<p></p>
						</div>
					</div>
				</div>

				{/* Locations Grid */}
				{locationsData && (
					<>
						{locationsData?.data?.length === 0 ? (
							<div className="py-12 text-center">
								<MapPin className="mx-auto h-12 w-12 text-gray-400" />
								<h3 className="mt-2 text-sm font-medium text-gray-900">
									No service found
								</h3>
								<p className="mt-1 text-sm text-gray-500">
									Get started by creating your first service.
								</p>
								<Button
									className="mt-4"
									onClick={() => setShowAddServiceForm(true)}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add a Service
								</Button>
							</div>
						) : (
							<div className="flex flex-col gap-0.5">
								{locationsData?.data?.map((location: any) => (
									<FormsCard
										key={location.id}
										location={location}
										isSelected={selectedServices.includes(
											location.id
										)}
										onSelectionChange={(selected) =>
											handleLocationSelection(
												location.id,
												selected
											)
										}
										onEdit={() =>
											console.log(
												"Edit location:",
												location.id
											)
										}
										onView={() =>
											handleViewLocation(location)
										}
										onSendBookingLink={() =>
											setShowSendBookingLinkSheet(true)
										}
									/>
								))}
							</div>
						)}

						{/* Pagination */}
						{locationsData?.pagination?.totalPages > 1 && (
							<div className="mt-8 flex items-center justify-center gap-2">
								<Button
									variant="outline"
									disabled={
										locationsData?.pagination?.page === 1
									}
									onClick={() =>
										handlePageChange(
											locationsData?.pagination?.page - 1
										)
									}
								>
									Previous
								</Button>

								<span className="text-sm text-gray-600">
									Page {locationsData.pagination.page} of{" "}
									{locationsData?.pagination?.totalPages}
								</span>

								<Button
									variant="outline"
									disabled={
										locationsData?.pagination?.page ===
										locationsData?.pagination?.totalPages
									}
									onClick={() =>
										handlePageChange(
											locationsData?.pagination?.page + 1
										)
									}
								>
									Next
								</Button>
							</div>
						)}
					</>
				)}
			</div>

			{/* Filter Sheet */}
			<ServiceFilterSheet
				open={showFilterSheet}
				onOpenChange={setShowFilterSheet}
				onApplyFilters={handleApplyFilters}
			/>

			{/* Send Booking Link Sheet */}
			<SendBookingLinkSheet
				open={showSendBookingLinkSheet}
				onOpenChange={setShowSendBookingLinkSheet}
			/>
		</div>
	);
};
