import type { Location, OperatingHours } from "../types";

export const formatAddress = (location: Pick<Location, 'address' | 'city' | 'state' | 'country'>): string => {
	return [
		location.address,
		location.city,
		location.state,
		location.country
	]
		.filter(Boolean)
		.join(', ') || 'No address provided';
};

export const formatOperatingHours = (hours: OperatingHours[]): string => {
	const dayNames = [
		"Sunday",
		"Monday",
		"Tuesday",
		"Wednesday",
		"Thursday",
		"Friday",
		"Saturday",
	];

	return hours
		.map((hour) => {
			const dayName = dayNames[hour.dayOfWeek];
			if (hour.isClosed) {
				return `${dayName}: Closed`;
			}
			return `${dayName}: ${hour.openTime} - ${hour.closeTime}`;
		})
		.join(", ");
};

export const formatPhoneNumber = (phone: string): string => {
	// Basic phone number formatting (US format)
	const cleaned = phone.replace(/\D/g, "");
	if (cleaned.length === 10) {
		return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
	}
	return phone;
};

export const getLocationStatus = (
	location: Location
): "active" | "inactive" => {
	return location.isActive ? "active" : "inactive";
};

export const getLocationStatusColor = (isActive: boolean): string => {
	return isActive ? "text-green-600" : "text-red-600";
};

export const isLocationOpen = (
	location: Location,
	currentTime?: Date
): boolean => {
	if (!location.operatingHours || !location.isActive) return false;

	const now = currentTime || new Date();
	const currentDay = now.getDay();
	const currentHour = now.getHours();
	const currentMinute = now.getMinutes();
	const currentTimeString = `${currentHour.toString().padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`;

	const todayHours = location.operatingHours.find(
		(h) => h.dayOfWeek === currentDay
	);

	if (!todayHours || todayHours.isClosed) return false;

	return (
		currentTimeString >= todayHours.openTime &&
		currentTimeString <= todayHours.closeTime
	);
};

export const calculateDistance = (
	from: { latitude: number; longitude: number },
	to: { latitude: number; longitude: number }
): number => {
	const R = 3959; // Earth's radius in miles
	const dLat = toRadians(to.latitude - from.latitude);
	const dLon = toRadians(to.longitude - from.longitude);

	const a =
		Math.sin(dLat / 2) * Math.sin(dLat / 2) +
		Math.cos(toRadians(from.latitude)) *
		Math.cos(toRadians(to.latitude)) *
		Math.sin(dLon / 2) *
		Math.sin(dLon / 2);

	const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
	return R * c;
};

const toRadians = (degrees: number): number => {
	return degrees * (Math.PI / 180);
};
