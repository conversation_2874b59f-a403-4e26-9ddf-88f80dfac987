import { useState } from "react";
import { X } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>ooter,
	<PERSON><PERSON><PERSON>eader,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { InputText } from "@/components/common/InputText";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Uploader } from "@/components/common/Uploader";
import { getAllCountries } from "@/components/common/InputPhone/utils";
import type { CreateLocationRequest } from "../../types";
import type { UploadedFile } from "@/components/common/Uploader/types";
import { uploadImage } from "@/lib/api/upload";
import { extractValidationErrors } from "@/lib/utils/errorHandling";

// Zod validation schema
const locationSchema = z.object({
	name: z.string().min(1, "Location name is required"),
	address: z.string().min(1, "Address is required"),
	country: z.string().min(1, "Country is required"),
	state: z.string().min(1, "State is optional"),
	city: z.string().min(1, "City is required"),
	phone_number: z.string().optional(),
	image: z.string().optional(),
	description: z.string().optional(),
	timezone: z.string().optional(),
});

type LocationFormData = z.infer<typeof locationSchema>;

interface AddLocationSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit?: (
		data: CreateLocationRequest & { images?: File[] }
	) => Promise<void>;
}

export function AddLocationSheet({
	open,
	onOpenChange,
	onSubmit,
}: AddLocationSheetProps) {
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [isUploading, setIsUploading] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
	const [actualFiles, setActualFiles] = useState<File[]>([]);

	const {
		register,
		handleSubmit,
		setValue,
		watch,
		reset,
		formState: { errors },
		trigger,
		setError,
	} = useForm<LocationFormData>({
		resolver: zodResolver(locationSchema),
		defaultValues: {
			name: "",
			address: "",
			country: "",
			state: "",
			city: "",
			phone_number: "",
			image: "",
			description: "",
			timezone: "America/New_York",
		},
	});

	const watchedValues = watch();
	const countries = getAllCountries();

	const handleInputChange = (
		field: keyof LocationFormData,
		value: string
	) => {
		setValue(field, value);
		// Trigger validation for the specific field to clear errors immediately
		trigger(field);
	};

	const handleImageChange = (files: File[]) => {
		if (files.length > 0) {
			setSelectedFile(files[0]);
			setValue("image", ""); // Clear image URL until upload
		} else {
			setSelectedFile(null);
			setValue("image", "");
		}
	};

	const handleFileRemove = (fileId: string) => {
		const fileIndex = uploadedFiles.findIndex((file) => file.id === fileId);
		setUploadedFiles((prev) => prev.filter((file) => file.id !== fileId));
		setActualFiles((prev) =>
			prev.filter((_, index) => index !== fileIndex)
		);
	};

	const handleFileEdit = (fileId: string, newName: string) => {
		setUploadedFiles((prev) =>
			prev.map((file) =>
				file.id === fileId ? { ...file, name: newName } : file
			)
		);
	};

	const onFormSubmit = async (data: LocationFormData) => {
		setIsSubmitting(true);
		try {
			let imageUrl = data.image;
			if (selectedFile) {
				setIsUploading(true);
				console.log(selectedFile);
				imageUrl = await uploadImage(selectedFile);
				setIsUploading(false);
			}

			const submitData = {
				...data,
				image: imageUrl,
			};
			await onSubmit?.(submitData);
			// Reset form
			reset();
			setSelectedFile(null);
			onOpenChange(false);
		} catch (error: any) {
			setIsUploading(false);
			console.error("Error submitting location:", error);

			// Extract validation errors from server response
			const validationErrors = extractValidationErrors(error);

			// Set server validation errors on form fields
			Object.entries(validationErrors).forEach(([field, message]) => {
				if (field in data) {
					setError(field as keyof LocationFormData, {
						type: "server",
						message: message,
					});
				}
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleCancel = () => {
		reset();
		onOpenChange(false);
	};

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-8 py-9 sm:max-w-[832px] [&>button]:hidden">
				<form onSubmit={handleSubmit(onFormSubmit)}>
					<SheetHeader className="p-0">
						<div className="flex items-center justify-between">
							<SheetTitle className="text-lg font-semibold">
								Add New Location
							</SheetTitle>
							<Button
								type="button"
								variant="ghost"
								size="icon"
								onClick={() => onOpenChange(false)}
								className="h-6 w-6 rounded-sm"
							>
								<X className="h-4 w-4" />
								<span className="sr-only">Close</span>
							</Button>
						</div>
					</SheetHeader>

					<div className="flex-1 space-y-3.5 pb-6">
						{/* Location Name */}
						<div className="space-y-2">
							<label className="text-foreground text-sm font-medium">
								Location name{" "}
								<span className="text-red-500">*</span>
							</label>
							<InputText
								placeholder="Enter location name"
								value={watchedValues.name}
								onChange={(e) =>
									handleInputChange("name", e.target.value)
								}
								className={`w-full ${errors.name ? "border-red-500" : ""
									}`}
								id="location-name"
								variant="default"
							/>
							{errors.name && (
								<p className="mt-1 text-xs text-red-600">
									{errors.name.message}
								</p>
							)}
						</div>
						{/* Address */}
						<div className="space-y-2">
							<label className="text-foreground text-sm font-medium">
								Address <span className="text-red-500">*</span>
							</label>
							<InputText
								placeholder="Enter full address"
								value={watchedValues.address}
								onChange={(e) =>
									handleInputChange("address", e.target.value)
								}
								className={`w-full ${errors.address ? "border-red-500" : ""
									}`}
								id="address"
								variant="default"
							/>
							{errors.address && (
								<p className="mt-1 text-xs text-red-600">
									{errors.address.message}
								</p>
							)}
						</div>
						{/* Country, State, City */}
						<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
							<div className="space-y-2">
								<label className="text-foreground text-sm font-medium">
									Country
								</label>
								<InputText
									placeholder="Enter country"
									value={watchedValues.country}
									onChange={(e) =>
										handleInputChange(
											"country",
											e.target.value
										)
									}
									className={`w-full ${errors.country ? "border-red-500" : ""
										}`}
									id="country"
									variant="default"
								/>
								{errors.country && (
									<p className="mt-1 text-xs text-red-600">
										{errors.country.message}
									</p>
								)}
							</div>
							<div className="space-y-2">
								<label className="text-foreground text-sm font-medium">
									State
								</label>
								<InputText
									placeholder="Enter state"
									value={watchedValues.state}
									onChange={(e) =>
										handleInputChange(
											"state",
											e.target.value
										)
									}
									className={`w-full ${errors.state ? "border-red-500" : ""
										}`}
									id="state"
									variant="default"
								/>
								{errors.state && (
									<p className="mt-1 text-xs text-red-600">
										{errors.state.message}
									</p>
								)}
							</div>
							<div className="space-y-2">
								<label className="text-foreground text-sm font-medium">
									City
								</label>
								<InputText
									placeholder="Enter city"
									value={watchedValues.city}
									onChange={(e) =>
										handleInputChange(
											"city",
											e.target.value
										)
									}
									className={`w-full ${errors.city ? "border-red-500" : ""
										}`}
									id="city"
									variant="default"
								/>
								{errors.city && (
									<p className="mt-1 text-xs text-red-600">
										{errors.city.message}
									</p>
								)}
							</div>
						</div>
						{/* Phone Number */}
						<div className="space-y-2">
							<label className="text-foreground text-sm font-medium">
								Phone Number
							</label>
							<InputText
								type="tel"
								placeholder="Enter phone number"
								value={watchedValues.phone_number}
								onChange={(e) =>
									handleInputChange(
										"phone_number",
										e.target.value
									)
								}
								className={`w-full ${errors.phone_number ? "border-red-500" : ""
									}`}
								id="phone_number"
								variant="default"
							/>
							{errors.phone_number && (
								<p className="mt-1 text-xs text-red-600">
									{errors.phone_number.message}
								</p>
							)}
						</div>
						{/* Image Upload */}
						<div className="space-y-2">
							<Uploader
								files={
									selectedFile
										? [
											{
												id: "1",
												name: selectedFile.name,
												size: selectedFile.size,
												type: selectedFile.type,
											},
										]
										: []
								}
								onFilesChange={handleImageChange}
								onFileRemove={() => handleImageChange([])}
								// onFileEdit={() => {}}
								accept=".svg,.png,.jpg,.jpeg"
								maxFileSize={8 * 1024 * 1024}
								uploadText={
									isUploading
										? "Uploading..."
										: "Click or drag file here to upload image"
								}
								descriptionText="Recommended file type: .svg, .png, .jpg (Max of 8 mb)"
								multiple={false}
								maxFiles={1}
								size="default"
								disabled={isUploading || isSubmitting}
							/>
						</div>
						{/* Description */}
						<div className="space-y-2">
							<label className="text-foreground text-sm font-medium">
								Location description
							</label>
							<Textarea
								placeholder="Enter location description"
								value={watchedValues.description}
								onChange={(e) =>
									handleInputChange(
										"description",
										e.target.value
									)
								}
								className={`min-h-[120px] w-full resize-none ${errors.description ? "border-red-500" : ""
									}`}
							/>
							{errors.description && (
								<p className="mt-1 text-xs text-red-600">
									{errors.description.message}
								</p>
							)}
						</div>
					</div>

					{/* Footer */}
					<SheetFooter className="mt-6 flex-row justify-end gap-3 p-0">
						<Button
							type="button"
							variant="secondary"
							onClick={handleCancel}
							className="cursor-pointer"
						>
							Close
						</Button>
						<Button
							type="submit"
							className="bg-primary hover:bg-primary/90 cursor-pointer"
							disabled={isSubmitting || isUploading}
						>
							{isSubmitting || isUploading ? "Saving..." : "Save"}
						</Button>
					</SheetFooter>
				</form>
			</SheetContent>
		</Sheet>
	);
}
