import { useState, type FC } from "react";
import { DataTable, type Column } from "@/components/common/DataTable";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Calendar } from "lucide-react";
import { Tabs, TabsContent } from "@/components/common/Tabs";
import { AppointmentTableCard } from "./AppointmentTableCard";

// Define appointment status type
export type AppointmentStatus =
	| "scheduled"
	| "in_progress"
	| "upcoming"
	| "completed"
	| "no_show"
	| "cancelled_patient"
	| "cancelled_admin";

// Define appointment interface
export interface Appointment {
	id: string;
	patientName: string;
	appointmentType: string;
	date: string;
	time: string;
	doctor: string;
	status: AppointmentStatus;
}

interface AppointmentsTabProps {
	className?: string;
}

export const AppointmentsTab: FC<AppointmentsTabProps> = ({ className }) => {
	const [activeTab, setActiveTab] = useState("upcoming");
	const [selectedAppointments, setSelectedAppointments] = useState<string[]>(
		[]
	);

	// Mock data for appointments (based on the screenshot)
	const mockAppointments: Appointment[] = [
		{
			id: "1",
			patientName: "<PERSON>",
			appointmentType: "First CT Scan Appointment",
			date: "12 Apr 2025",
			time: "12:30 pm - 4:00 pm",
			doctor: "Dr. <PERSON> <PERSON>",
			status: "scheduled",
		},
		{
			id: "2",
			patientName: "<PERSON> Heatherway",
			appointmentType: "First CT Scan Appointment",
			date: "12 Apr 2025",
			time: "12:30 pm - 4:00 pm",
			doctor: "Dr. <PERSON> <PERSON>",
			status: "in_progress",
		},
		{
			id: "3",
			patientName: "John Heatherway",
			appointmentType: "First CT Scan Appointment",
			date: "12 Apr 2025",
			time: "12:30 pm - 4:00 pm",
			doctor: "Dr. Abraham Johnson",
			status: "upcoming",
		},
		{
			id: "4",
			patientName: "John Heatherway",
			appointmentType: "First CT Scan Appointment",
			date: "12 Apr 2025",
			time: "10:30 pm - 4:00 pm",
			doctor: "Dr. Abraham Johnson",
			status: "upcoming",
		},
	];

	// Filter appointments based on active tab
	const filteredAppointments = mockAppointments.filter((appointment) => {
		if (activeTab === "upcoming") {
			return ["scheduled", "upcoming", "in_progress"].includes(
				appointment.status
			);
		} else {
			return [
				"completed",
				"no_show",
				"cancelled_patient",
				"cancelled_admin",
			].includes(appointment.status);
		}
	});

	// Define columns for the DataTable
	const columns: Column<Appointment>[] = [
		{
			key: "patient",
			label: "Patient",
			width: "flex-2",
		},
		{
			key: "appointment",
			label: "Appointment",
			width: "flex-2",
		},
		{
			key: "dateTime",
			label: "Date & Time",
			width: "flex-2",
		},
		{
			key: "doctor",
			label: "Doctor",
			width: "flex-1",
		},
		{
			key: "status",
			label: "Status",
			width: "flex-1",
		},
		{
			key: "actions",
			label: "Actions",
			width: "flex-1",
		},
	];

	const handleSelectAll = (checked: boolean) => {
		if (checked) {
			setSelectedAppointments(filteredAppointments.map((apt) => apt.id));
		} else {
			setSelectedAppointments([]);
		}
	};

	const handleAppointmentSelection = (
		appointmentId: string,
		selected: boolean
	) => {
		if (selected) {
			setSelectedAppointments((prev) => [...prev, appointmentId]);
		} else {
			setSelectedAppointments((prev) =>
				prev.filter((id) => id !== appointmentId)
			);
		}
	};

	const tabItems = [
		{ value: "upcoming", label: "Upcoming" },
		{ value: "history", label: "History" },
	];

	return (
		<div className={className}>
			<div className="p-6">
				<div className="mb-6 flex items-center justify-between">
					<div>
						<h1 className="text-2xl font-semibold text-gray-900">
							Appointments
						</h1>
						<p className="text-sm text-gray-500">
							Manage and view all appointments
						</p>
					</div>
					<Button className="bg-primary hover:bg-primary/90">
						<Plus className="mr-2 h-4 w-4" />
						Book Appointment
					</Button>
				</div>

				<Tabs
					items={tabItems}
					defaultValue="upcoming"
					value={activeTab}
					onValueChange={setActiveTab}
				>
					<TabsContent value="upcoming">
						<DataTable
							columns={columns}
							data={filteredAppointments}
							selectedItems={selectedAppointments}
							onSelectAll={handleSelectAll}
							onItemSelect={handleAppointmentSelection}
							getItemId={(appointment) => appointment.id}
							showCheckboxes={true}
							renderItem={(appointment, isSelected, onSelect) => (
								<AppointmentTableCard
									key={appointment.id}
									appointment={appointment}
									isSelected={isSelected}
									onSelectionChange={onSelect}
								/>
							)}
							emptyState={{
								icon: (
									<Calendar className="mx-auto h-12 w-12 text-gray-400" />
								),
								title: "No upcoming appointments",
								description:
									"There are no upcoming appointments scheduled.",
								action: {
									label: "Book Appointment",
									onClick: () =>
										console.log("Book appointment clicked"),
								},
							}}
						/>
					</TabsContent>

					<TabsContent value="history">
						<DataTable
							columns={columns}
							data={filteredAppointments}
							selectedItems={selectedAppointments}
							onSelectAll={handleSelectAll}
							onItemSelect={handleAppointmentSelection}
							getItemId={(appointment) => appointment.id}
							showCheckboxes={true}
							renderItem={(appointment, isSelected, onSelect) => (
								<AppointmentTableCard
									key={appointment.id}
									appointment={appointment}
									isSelected={isSelected}
									onSelectionChange={onSelect}
								/>
							)}
							emptyState={{
								icon: (
									<Calendar className="mx-auto h-12 w-12 text-gray-400" />
								),
								title: "No appointment history",
								description:
									"There are no past appointments to display.",
							}}
						/>
					</TabsContent>
				</Tabs>
			</div>
		</div>
	);
};
