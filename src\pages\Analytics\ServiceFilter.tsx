import React from "react";
import { dummyFilterData } from './data';
import { useAnalyticsStore } from './useAnalyticsStore';
import MultiSelect from "@/components/ui-extended/multi-select";

const ServiceFilter: React.FC = () => {
  const { analyticsQuery, setAnalyticsQuery } = useAnalyticsStore();

  return (
    <MultiSelect
      width="100%"
      options={dummyFilterData.services.map((item: any) => ({
        value: item.id.toString(),
        label: item.name,
      }))}
      placeholder={"Services"}
      selected={
        analyticsQuery?.service_ids?.map((item) => ({
          value: item.toString(),
          label: item.toString(),
        })) ?? []
      }
      onSelect={(option) => {
        setAnalyticsQuery({
          service_ids: option.map((item) => parseInt(item.value)),
        });
      }}
      onUnselect={(selectedOption) =>
        setAnalyticsQuery({
          service_ids: analyticsQuery?.service_ids?.filter(
            (s) => s !== parseInt(selectedOption.value)
          ),
        })
      }
      className="h-9"
      textContainerClassName="text-left justify-between"
    />
  );
};

export default ServiceFilter;
