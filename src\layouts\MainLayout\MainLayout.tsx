import React from "react";
import { Outlet } from "react-router";
import { Sidebar } from "../components/Sidebar";
import { Header } from "../components/Header";
import { useUIStore } from "@/stores/uiStore";
import { ModalManager } from "@/components/ui-components/ModalManager";
import { DrawerManager } from "@/components/ui-components/DrawerManager";
import { usePerformanceCleanup } from "@/lib/hooks/usePerformanceCleanup";
import MigraniumLogo from "@/assets/images/logo-blue-footer.svg";

export const MainLayout = () => {
	const { mobileMenuOpen, setSidebarCollapsed, setMobileMenuOpen } =
		useUIStore();

	const [isMobile, setIsMobile] = React.useState(() => {
		if (typeof window !== "undefined") {
			return window.innerWidth < 768;
		}
		return false;
	});

	// Handle window resize with proper cleanup
	React.useEffect(() => {
		const handleResize = () => {
			setIsMobile(window.innerWidth < 768);
		};

		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	const handleSidebarClose = React.useCallback(() => {
		// Close mobile menu on mobile, or collapse sidebar on desktop
		if (isMobile) {
			setMobileMenuOpen(false);
		} else {
			setSidebarCollapsed(true);
		}
	}, [isMobile, setMobileMenuOpen, setSidebarCollapsed]);

	// On mobile, show drawer when mobileMenuOpen is true
	// On desktop, always show but respect collapsed state
	const isSidebarOpen = isMobile ? mobileMenuOpen : true;

	// Initialize auto-cleanup (only once at app level)
	usePerformanceCleanup();

	return (
		<div className="flex h-screen overflow-hidden">
			<Sidebar isOpen={isSidebarOpen} onClose={handleSidebarClose} />

			<div className="flex flex-1 flex-col overflow-hidden bg-white">
				<Header />

				{/* Main content */}
				<main className="flex-1 overflow-y-auto px-6">
					<Outlet />
				</main>
				<div className="sticky bottom-0 flex flex-col items-center bg-white pt-2">
					<footer className="flex w-full items-center justify-center space-x-2 py-2">
						<small className="py-0.5 text-[12px] text-[#6D748D]">
							Powered by
						</small>
						<img
							src={MigraniumLogo}
							alt="Migranium logo"
							className="py-0.5"
						/>
					</footer>
				</div>
			</div>

			{/* Add these at the end of your app */}
			<ModalManager />
			<DrawerManager />
		</div>
	);
};
