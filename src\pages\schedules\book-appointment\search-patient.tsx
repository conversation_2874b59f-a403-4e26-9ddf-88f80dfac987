import { Input } from "@/components/ui/input";
import clsx from "clsx";
import { Check, Search } from "lucide-react";
import { useGetPatients } from "../store/slices/patientsSlice";
import { useState, useEffect, useMemo } from "react";
import type { ClientsTypes } from "../types";
import { useDebounce } from "@/hooks/useDebounce";

type Props = {
    selectedPatient: number | null;
    setSelectedPatient: (selectedPatient: number) => void;
    onDoubleClick: () => void;
    onPatientSelected?: (patient: ClientsTypes.GetPatientsResponse['data'][0]) => void;
}

export default function SearchPatient({
    selectedPatient,
    setSelectedPatient,
    onDoubleClick,
    onPatientSelected,
}: Props) {
    const [searchTerm, setSearchTerm] = useState<string>("");
    const debouncedSearchTerm = useDebounce(searchTerm, 300);

    const { data, isLoading } = useGetPatients({
        search: debouncedSearchTerm || undefined
    });

    // Filter patients locally as well for immediate feedback
    const filteredPatients = useMemo(() => {
        if (!data?.data) return [];

        if (!searchTerm.trim()) return data.data;

        const search = searchTerm.toLowerCase();
        return data.data.filter(patient =>
            patient.first_name?.toLowerCase().includes(search) ||
            patient.last_name?.toLowerCase().includes(search) ||
            patient.email?.toLowerCase().includes(search) ||
            patient.full_name?.toLowerCase().includes(search) ||
            patient.id.toString().includes(search)
        );
    }, [data?.data, searchTerm]);

    const handlePatientClick = (patient: ClientsTypes.GetPatientsResponse['data'][0]) => {
        setSelectedPatient(patient.id);
        onPatientSelected?.(patient);
        onDoubleClick();
    };

    return (
        <div className="mx-10 mt-5">
            <div className="relative h-fit">
                <Search
                    color="#71717A"
                    size={17}
                    className="absolute left-4.5 top-3"
                />
                <Input
                    name="search-patient"
                    id="search-patient"
                    type="text"
                    placeholder="Search Patient"
                    className="py-5 pl-10.5 placeholder:font-light"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                />
            </div>

            <div className="space-y-4 pr-6 mt-5 custom-scrollbar overflow-y-auto max-h-[85vh]">
                {isLoading ? (
                    <div className="flex min-h-[20rem] items-center justify-center">
                        <div className="text-center">
                            <div className="mx-auto mb-4 h-10 w-10 animate-spin rounded-full border-b-2 border-[#005893]"></div>
                            <p className="text-gray-600">Loading patients...</p>
                        </div>
                    </div>
                ) : (
                    filteredPatients?.length && filteredPatients?.length > 0 ? (
                        filteredPatients.map((patient, index) => (
                            <button
                                key={patient.id || index}
                                className={clsx("cursor-pointer w-full border-[1.5px] py-3.5 px-4 rounded-[10px] flex items-start justify-between transition-all duration-300 hover:border-[#005893]")}
                                tabIndex={0}
                                onClick={() => handlePatientClick(patient)}
                            >
                                <div className="flex items-center justify-center gap-x-3">
                                    <div className="w-12 grid place-content-center">
                                    </div>
                                    <div className="size-12 bg-[#E4E4E7] text-[#A1A1AA] rounded-full grid place-content-center">
                                        {patient.first_name?.[0]}{patient.last_name?.[0]}
                                    </div>
                                    <div className="flex flex-col gap-y-1 text-left ml-4">
                                        <h1 className="text-[#27272A] text-base">{patient.first_name} {patient.last_name}</h1>
                                        <p className="text-[#71717A] text-sm font-light">{patient.email}</p>
                                    </div>
                                </div>
                                <h1 className="text-[#27272A] font-medium text-base">#{patient.id}</h1>
                            </button>
                        ))
                    ) : (
                        <div className="flex min-h-[20rem] items-center justify-center">
                            <div className="text-center">
                                <p className="text-gray-600">
                                    {searchTerm.trim() ? "No patients found matching your search." : "No patients available."}
                                </p>
                            </div>
                        </div>
                    )
                )}
            </div>

        </div >
    )
}