import { create } from "zustand";
import { persist } from "zustand/middleware";
import type { FormTypes } from "../../types";

interface FormDraft {
	id: string;
	formData: FormTypes.FormDataType;
	lastModified: Date;
	title?: string;
}

interface FormDraftState {
	drafts: FormDraft[];
	currentDraftId: string | null;
	currentDraft: FormTypes.FormDataType | null;
	isDirty: boolean;

	// Actions
	createDraft: (formData: FormTypes.FormDataType, title?: string) => string;
	updateDraft: (id: string, formData: FormTypes.FormDataType) => void;
	updateCurrentDraft: (formData: FormTypes.FormDataType) => void;
	saveDraft: (formData: FormTypes.FormDataType, title?: string) => string;
	deleteDraft: (id: string) => void;
	loadDraft: (id: string) => FormTypes.FormDataType | null;
	setCurrentDraft: (id: string | null) => void;
	getCurrentDraft: () => FormDraft | null;
	getAllDrafts: () => FormDraft[];
	clearAllDrafts: () => void;
	setIsDirty: (dirty: boolean) => void;
}

export const useFormDraftStore = create<FormDraftState>()(
	persist(
		(set, get) => ({
			drafts: [],
			currentDraftId: null,
			currentDraft: null,
			isDirty: false,

			createDraft: (formData, title) => {
				const id = `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
				const newDraft: FormDraft = {
					id,
					formData,
					lastModified: new Date(),
					title: title || formData.name || "Untitled Form",
				};

				set((state) => ({
					drafts: [...state.drafts, newDraft],
					currentDraftId: id,
				}));

				return id;
			},

			updateDraft: (id, formData) => {
				set((state) => ({
					drafts: state.drafts.map((draft) =>
						draft.id === id
							? {
									...draft,
									formData,
									lastModified: new Date(),
									title: formData.name || draft.title,
								}
							: draft
					),
				}));
			},

			updateCurrentDraft: (formData) => {
				const { currentDraftId } = get();

				set((state) => ({
					currentDraft: formData,
					isDirty: true,
				}));

				if (currentDraftId) {
					get().updateDraft(currentDraftId, formData);
				}
			},

			saveDraft: (formData, title) => {
				const { currentDraftId } = get();

				if (currentDraftId) {
					// Update existing draft
					get().updateDraft(currentDraftId, formData);
					set({ isDirty: false });
					return currentDraftId;
				} else {
					// Create new draft
					const id = get().createDraft(formData, title);
					set({ isDirty: false });
					return id;
				}
			},

			deleteDraft: (id) => {
				set((state) => ({
					drafts: state.drafts.filter((draft) => draft.id !== id),
					currentDraftId:
						state.currentDraftId === id
							? null
							: state.currentDraftId,
				}));
			},

			loadDraft: (id) => {
				const { drafts } = get();
				const draft = drafts.find((d) => d.id === id);
				return draft ? draft.formData : null;
			},

			setCurrentDraft: (id) => {
				set({ currentDraftId: id });

				if (id) {
					const draft = get().loadDraft(id);
					set({
						currentDraft: draft,
						isDirty: false,
					});
				} else {
					set({
						currentDraft: null,
						isDirty: false,
					});
				}
			},

			getCurrentDraft: () => {
				const { drafts, currentDraftId } = get();
				if (!currentDraftId) return null;
				return (
					drafts.find((draft) => draft.id === currentDraftId) || null
				);
			},

			getAllDrafts: () => {
				return get().drafts.sort(
					(a, b) =>
						new Date(b.lastModified).getTime() -
						new Date(a.lastModified).getTime()
				);
			},

			clearAllDrafts: () => {
				set({
					drafts: [],
					currentDraftId: null,
					currentDraft: null,
					isDirty: false,
				});
			},

			setIsDirty: (dirty) => {
				set({ isDirty: dirty });
			},
		}),
		{
			name: "form-drafts-storage",
			// Optional: customize what gets persisted
			partialize: (state) => ({
				drafts: state.drafts,
				currentDraftId: state.currentDraftId,
				// Don't persist currentDraft and isDirty as they're runtime state
			}),
		}
	)
);
