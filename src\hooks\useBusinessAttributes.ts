import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { businessAttributesApi } from "@/lib/api/businessAttributes";
import { queryKeys, mediumLivedQueryOptions } from "@/lib/query";
import type { BusinessAttributesFilters } from "@/types/businessAttributes";
import { useOrganizationContext } from "@/features/organizations/context";
import { toast } from "sonner";
import type { UpdateClientAttributeSettingsRequest } from "@/lib/api/businessAttributes";

export const useBusinessAttributes = (
	filters: BusinessAttributesFilters = {},
	options?: {
		enabled?: boolean;
	}
) => {
	const { organizationId } = useOrganizationContext();

	return useQuery({
		queryKey: queryKeys.businessAttributes.list(filters),
		queryFn: () =>
			businessAttributesApi.getBusinessAttributes(
				filters,
				organizationId || undefined
			),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!organizationId,
	});
};

export const useBusinessAttributesForForm = (options?: {
	enabled?: boolean;
}) => {
	const filters: BusinessAttributesFilters = {
		page: 1,
		per_page: 100,
		show_in_list: true,
	};

	return useBusinessAttributes(filters, options);
};

export const useConditionalAttributes = (
	organizationId: number,
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery({
		queryKey: ["conditionalAttributes", organizationId],
		queryFn: () =>
			businessAttributesApi.getConditionalAttributes(organizationId),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!organizationId,
	});
};

export const useAttributeTypeConfigs = (
	organizationId: number,
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery({
		queryKey: ["attributeTypeConfigs", organizationId],
		queryFn: () =>
			businessAttributesApi.getAttributeTypeConfigs(organizationId),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!organizationId,
	});
};

export const useClientAttributeSettings = (
	organizationId: number,
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery({
		queryKey: ["clientAttributeSettings", organizationId],
		queryFn: () =>
			businessAttributesApi.getClientAttributeSettings(organizationId),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!organizationId,
	});
};

export const useUpdateClientAttributeSettings = (options?: {
	onSuccess?: (data: any) => void;
	onError?: (error: any) => void;
}) => {
	const queryClient = useQueryClient();
	const { organizationId } = useOrganizationContext();

	return useMutation({
		mutationFn: (data: UpdateClientAttributeSettingsRequest) =>
			businessAttributesApi.updateClientAttributeSettings(
				data,
				organizationId || 0
			),
		onSuccess: (data: any) => {
			toast.success(
				data.message || "Patient settings updated successfully"
			);

			// Invalidate related queries
			queryClient.invalidateQueries({
				queryKey: ["clientAttributeSettings", organizationId],
			});
			queryClient.invalidateQueries({
				queryKey: queryKeys.businessAttributes.all,
			});

			options?.onSuccess?.(data);
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update patient settings");
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};
