import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
	customIntakesApi,
	type GetCustomIntakesParams,
	type CreateIntakeFieldData,
	type UpdateIntakeFieldData,
} from "@/lib/api/customIntakesApi";

// Query keys
const CUSTOM_INTAKES_KEYS = {
	all: ["custom-intakes"] as const,
	lists: () => [...CUSTOM_INTAKES_KEYS.all, "list"] as const,
	list: (params: GetCustomIntakesParams) =>
		[...CUSTOM_INTAKES_KEYS.lists(), params] as const,
	details: () => [...CUSTOM_INTAKES_KEYS.all, "detail"] as const,
	detail: (id: number, organizationId: string) =>
		[...CUSTOM_INTAKES_KEYS.details(), id, organizationId] as const,
};

// Get custom intakes hook
export const useCustomIntakes = (
	params: GetCustomIntakesParams,
	enabled = true
) => {
	return useQuery({
		queryKey: CUSTOM_INTAKES_KEYS.list(params),
		queryFn: () => customIntakesApi.getCustomIntakes(params),
		enabled: enabled && !!params.organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
};

// Get single custom intake hook
export const useCustomIntake = (
	id: number,
	organizationId: string,
	enabled = true
) => {
	return useQuery({
		queryKey: CUSTOM_INTAKES_KEYS.detail(id, organizationId),
		queryFn: () => customIntakesApi.getCustomIntake(id, organizationId),
		enabled: enabled && !!id && !!organizationId,
		staleTime: 5 * 60 * 1000,
		refetchOnWindowFocus: false,
	});
};

// Create custom intake mutation
export const useCreateCustomIntake = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			data,
			organizationId,
		}: {
			data: CreateIntakeFieldData;
			organizationId: string;
		}) => customIntakesApi.createCustomIntake(data, organizationId),
		onSuccess: (response, variables) => {
			queryClient.invalidateQueries({
				queryKey: CUSTOM_INTAKES_KEYS.lists(),
			});
			toast.success(
				response.message || "Intake field created successfully"
			);
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to create intake field");
		},
	});
};

// Update custom intake mutation
export const useUpdateCustomIntake = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			data,
			organizationId,
		}: {
			data: UpdateIntakeFieldData;
			organizationId: string;
		}) => customIntakesApi.updateCustomIntake(data, organizationId),
		onSuccess: (response, variables) => {
			queryClient.invalidateQueries({
				queryKey: CUSTOM_INTAKES_KEYS.lists(),
			});
			queryClient.invalidateQueries({
				queryKey: CUSTOM_INTAKES_KEYS.detail(
					variables.data.id,
					variables.organizationId
				),
			});
			toast.success(
				response.message || "Intake field updated successfully"
			);
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update intake field");
		},
	});
};

// Delete custom intake mutation
export const useDeleteCustomIntake = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			id,
			organizationId,
		}: {
			id: number;
			organizationId: string;
		}) => customIntakesApi.deleteCustomIntake(id, organizationId),
		onSuccess: (response) => {
			queryClient.invalidateQueries({
				queryKey: CUSTOM_INTAKES_KEYS.lists(),
			});
			toast.success(
				response.message || "Intake field deleted successfully"
			);
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to delete intake field");
		},
	});
};

// Update field requirement mutation
export const useUpdateFieldRequirement = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			id,
			requirement,
			organizationId,
		}: {
			id: number;
			requirement: "yes" | "no" | "optional";
			organizationId: string;
		}) =>
			customIntakesApi.updateFieldRequirement(
				id,
				requirement,
				organizationId
			),
		onSuccess: (response, variables) => {
			queryClient.invalidateQueries({
				queryKey: CUSTOM_INTAKES_KEYS.lists(),
			});
			queryClient.invalidateQueries({
				queryKey: CUSTOM_INTAKES_KEYS.detail(
					variables.id,
					variables.organizationId
				),
			});
			toast.success("Field requirement updated successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update field requirement");
		},
	});
};

// Update field visibility mutation
export const useUpdateFieldVisibility = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			id,
			isVisible,
			organizationId,
		}: {
			id: number;
			isVisible: boolean;
			organizationId: string;
		}) =>
			customIntakesApi.updateFieldVisibility(
				id,
				isVisible,
				organizationId
			),
		onSuccess: (response, variables) => {
			queryClient.invalidateQueries({
				queryKey: CUSTOM_INTAKES_KEYS.lists(),
			});
			queryClient.invalidateQueries({
				queryKey: CUSTOM_INTAKES_KEYS.detail(
					variables.id,
					variables.organizationId
				),
			});
			toast.success("Field visibility updated successfully");
		},
		onError: (error: any) => {
			toast.error(error?.message || "Failed to update field visibility");
		},
	});
};
