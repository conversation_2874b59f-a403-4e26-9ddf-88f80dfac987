import type { OperatingHour } from "@/types/onboarding";
import type { AuthUserData } from "@/types/signin";
import type { AuthTwoEnabledFactorResponse } from "@/types/signup";
import { create } from "zustand";
import { persist } from "zustand/middleware";

interface AdminStore {
	user: AuthUserData | null;
	selectedOrganisation: string | null;
	onboardingState: number;
	onboardingLocationInfo: onboardingLocationInfoType | null;
	rememberAuth: {
		rememberMe?: boolean;
		rememberToken?: string;
	} | null;
	mfaUser: AuthTwoEnabledFactorResponse | null;
	setOnboardingState: (newState: number) => void;
	setOnboardingLocationInfo: (
		onboardingState: onboardingLocationInfoType | null
	) => void;
	setUser: (user: AuthUserData | null) => void;
	setRememberAuth: (
		rememberAuth: {
			rememberMe?: boolean;
			rememberToken?: string;
		} | null
	) => void;
	setMfaUser: (mfaUser: AuthTwoEnabledFactorResponse | null) => void;
	resetMfaUser: () => void;
	reset: () => void;
}

interface onboardingLocationInfoType {
	id?: number;
	approximate_waiting_time: number | string;
	schedule_block_in_min: number | string;
	time_zone: string;
	time_slots: OperatingHour[];
	
}

const initialState = {
	user: null,
	mfaUser: null,
	onboardingState: 1,
	onboardingLocationInfo: null,
	selectedOrganisation: null,
	rememberAuth: {
		rememberMe: false,
	},
};

const useUserStore = create<AdminStore>()(
	persist(
		(set) => ({
			...initialState,
			setUser: (user: AuthUserData | null) => {
				if (user)
					set((state) => {
						return { user: state.user ? { ...state.user, ...user } : user };
					});
			},
			setOnboardingLocationInfo: (onboardingLocationInfo: onboardingLocationInfoType | null) =>
				set(() => ({ onboardingLocationInfo })),
			setOnboardingState: (onboardingState: number) => {
				set(() => ({
					onboardingState,
				}));
			},

			setRememberAuth: (rememberAuth: { rememberMe?: boolean; rememberToken?: string; } | null) => {
				set((state) => ({
					rememberAuth: state.rememberAuth ? { ...state.rememberAuth, ...rememberAuth } : rememberAuth,
				}));
			},
			setMfaUser: (mfaUser: AuthTwoEnabledFactorResponse | null) => {
				if (mfaUser)
					set((state) => {
						return { mfaUser: state.mfaUser ? { ...state.mfaUser, ...mfaUser } : mfaUser };
					});
			},
			resetMfaUser: () => {
				set(() => {
					return { mfaUser: null };
				});
			},
			reset: () => set(initialState),
		}),
		{
			name: "user-storage",
			// storage: () => localStorage,
		}
	)
);

export default useUserStore;
