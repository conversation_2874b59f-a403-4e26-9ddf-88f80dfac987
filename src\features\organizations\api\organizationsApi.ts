import { apiClient } from "@/lib/api/clients";

export interface BusinessCategory {
	id: number;
	name: string;
}

export interface BusinessCategoriesResponse {
	success: boolean;
	message: string;
	data: BusinessCategory[];
}

export interface Organization {
	id?: number;
	name: string;
	address: string;
	country: string;
	state: string;
	city: string;
	zip_code: string;
	phone_number: string;
	business_category_id: number;
	theme: string;
	logo_url: string;
}

// Operating Hours Types
export interface TimeSlot {
	start_time: string;
	end_time: string;
	is_active: boolean;
}

export interface OrganizationOperatingHour {
	weekday: number; // 0 = Monday, 1 = Tuesday, etc.
	week_day_name: string;
	is_enabled: boolean;
	time_slot: TimeSlot[];
	organization?: {
		name: string;
		id: number;
	};
	location?: string | null; // Present in location/station responses
}

export interface OrganizationOperatingHoursResponse {
	success: boolean;
	message: string;
	data: OrganizationOperatingHour[];
	meta: any;
}

// Additional interface for location/station operating hours responses
export interface LocationOperatingHoursResponse {
	success: boolean;
	message: string;
	data: {
		hours: OrganizationOperatingHour[];
		inheritance_info: {
			has_own_hours: boolean;
			inheriting_from: "organization" | "location";
		};
	};
	meta: any;
}

// Type for the array of operating hours data sent to API
export type UpdateOperatingHoursRequest = {
	weekday: number;
	week_day_name?: string;
	is_enabled: boolean;
	time_slot: Omit<TimeSlot, "is_active">[];
}[];

// Schedule Settings Types
export interface ScheduleSettings {
	id: string;
	schedule_visibility: boolean;
	auto_approve: boolean;
	estimated_wait_time: number;
	timezone: string;
	organization?: {
		name: string;
		id: number;
	};
	location?: string | null; // Present in location/station responses
	method: {
		in_person: boolean;
		video: boolean;
		audio: boolean;
	};
	chat: {
		is_enabled: boolean;
		type: "both_parties" | "provider_only" | "patient_only";
	};
	block: {
		is_enabled: boolean;
		after: {
			is_enabled: boolean;
			date: string | null;
		};
		till: {
			is_enabled: boolean;
			value: number;
			unit: "days" | "weeks" | "months";
		};
	};
}

export interface ScheduleSettingsResponse {
	success: boolean;
	message: string;
	data: ScheduleSettings;
	meta: any;
}

// Location/Station schedule settings response with inheritance
export interface LocationScheduleSettingsResponse {
	success: boolean;
	message: string;
	data: ScheduleSettings & {
		inheritance_info: {
			has_own_settings: boolean;
			inheriting_from: "organization" | "location";
		};
	};
	meta: any;
}

export type UpdateScheduleSettingsRequest = Omit<
	ScheduleSettings,
	"id" | "organization" | "location"
>;

// Schedule Optimizer Types
export interface ScheduleOptimizer {
	is_enabled: boolean;
	created_at: string;
	updated_at: string;
	organization?: {
		name: string;
		id: number;
	};
	location?: string | null; // Present in location/station responses
	trigger: {
		new_cancellation: {
			is_enabled: boolean;
			within_value: number;
			within_unit: "hours" | "days" | "weeks";
		};
		open_time_slot: {
			is_enabled: boolean;
			before_value: number;
			before_unit: "hours" | "days" | "weeks";
		};
	};
	recipients: {
		walk_in: {
			is_enabled: boolean;
			priority_min: number;
			priority_max: number;
			priority: number;
		};
		high_priority: {
			is_enabled: boolean;
			priority_min: number;
			priority_max: number;
			priority: number;
		};
		upcoming_appointments: {
			is_enabled: boolean;
			min_value: number;
			max_value: number;
			unit: "hours" | "days" | "weeks";
			priority: number;
		};
	};
	delivery: {
		email: boolean;
		sms: boolean;
		in_app: boolean;
	};
}

export interface ScheduleOptimizerResponse {
	success: boolean;
	message: string;
	data: ScheduleOptimizer;
	meta: any;
}

// Location/Station schedule optimizer response with inheritance
export interface LocationScheduleOptimizerResponse {
	success: boolean;
	message: string;
	data: ScheduleOptimizer & {
		inheritance_info: {
			has_own_optimizer: boolean;
			inheriting_from: "organization" | "location";
		};
	};
	meta: any;
}

export type UpdateScheduleOptimizerRequest = Omit<
	ScheduleOptimizer,
	"created_at" | "updated_at" | "organization" | "location"
>;

const ORGANIZATIONS_ENDPOINTS = {
	base: "/api/v1/organizations",
	single: "/api/v1/organization",
	byId: (id: string) => `/api/v1/organizations/${id}`,
	businessCategories: "/api/v1/business-categories",
	theme: "/api/v1/theme",
	operatingHours: "/schedule/api/v1/organization/operating-hours",
	scheduleSettings: "/schedule/api/v1/organization/schedule-settings",
	scheduleOptimizer: "/schedule/api/v1/organization/schedule-optimizer",
} as const;

export const organizationsApi = {
	getOrganizations: async (): Promise<Organization[]> => {
		const response = await apiClient.get(ORGANIZATIONS_ENDPOINTS.base, {
			params: {
				include:
					"location_count,station_count,service_count,avg_waittime",
			},
		});
		return response.data.data;
	},
	getBusinessCategories: async (): Promise<BusinessCategory[]> => {
		const response = await apiClient.get(
			ORGANIZATIONS_ENDPOINTS.businessCategories
		);
		return response.data.data;
	},
	getOrganization: async (
		id: string
	): Promise<{ success: boolean; message: string; data: Organization }> => {
		const response = await apiClient.get(ORGANIZATIONS_ENDPOINTS.single, {
			headers: {
				"X-organizationId": id,
			},
		});
		return response.data;
	},
	createOrganization: async (data: Organization): Promise<Organization> => {
		const response = await apiClient.post(
			ORGANIZATIONS_ENDPOINTS.base,
			data
		);
		return response.data;
	},
	updateOrganization: async (
		id: string,
		data: Partial<Organization>
	): Promise<{ success: boolean; message: string; data: Organization }> => {
		// Transform data to match the expected API format
		const requestBody = {
			...data,
			// Ensure logo_url is sent as 'logo' if it exists
			...(data.logo_url && { logo: data.logo_url }),
		};

		// Remove logo_url from the request body since API expects 'logo'
		if (requestBody.logo_url) {
			delete requestBody.logo_url;
		}

		const response = await apiClient.put(
			ORGANIZATIONS_ENDPOINTS.single,
			requestBody,
			{
				headers: {
					"X-organizationId": id,
				},
			}
		);
		return response.data;
	},
	deleteOrganization: async (id: string): Promise<void> => {
		await apiClient.delete(ORGANIZATIONS_ENDPOINTS.byId(id));
	},

	// Theme API method
	updateOrganizationTheme: async (
		orgId: string,
		theme: string
	): Promise<{ success: boolean; message: string }> => {
		const response = await apiClient.put(
			ORGANIZATIONS_ENDPOINTS.theme,
			{ theme },
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data;
	},

	// Operating Hours API methods
	getOrganizationOperatingHours: async (
		orgId: number
	): Promise<OrganizationOperatingHour[]> => {
		const response = await apiClient.get(
			ORGANIZATIONS_ENDPOINTS.operatingHours,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data.data;
	},

	updateOrganizationOperatingHours: async (
		orgId: number,
		data: UpdateOperatingHoursRequest
	): Promise<OrganizationOperatingHour[]> => {
		const response = await apiClient.put(
			ORGANIZATIONS_ENDPOINTS.operatingHours,
			data, // Send the array directly, not wrapped in an object
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data.data;
	},

	// Schedule Settings API methods
	getOrganizationScheduleSettings: async (
		orgId: number
	): Promise<ScheduleSettings> => {
		const response = await apiClient.get(
			ORGANIZATIONS_ENDPOINTS.scheduleSettings,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data.data;
	},

	updateOrganizationScheduleSettings: async (
		orgId: number,
		data: UpdateScheduleSettingsRequest
	): Promise<ScheduleSettings> => {
		const response = await apiClient.patch(
			ORGANIZATIONS_ENDPOINTS.scheduleSettings,
			data,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data.data;
	},

	// Schedule Optimizer API methods
	getOrganizationScheduleOptimizer: async (
		orgId: number
	): Promise<ScheduleOptimizer> => {
		const response = await apiClient.get(
			ORGANIZATIONS_ENDPOINTS.scheduleOptimizer,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data.data;
	},

	updateOrganizationScheduleOptimizer: async (
		orgId: number,
		data: UpdateScheduleOptimizerRequest
	): Promise<ScheduleOptimizer> => {
		const response = await apiClient.patch(
			ORGANIZATIONS_ENDPOINTS.scheduleOptimizer,
			data,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data.data;
	},
};
