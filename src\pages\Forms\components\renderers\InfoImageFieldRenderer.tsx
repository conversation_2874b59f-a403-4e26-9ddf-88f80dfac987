import React from "react";
import type { FormField } from "../../types/Form";

type TextFieldRendererProps = {
	field: FormField;
	value?: any;
	mode?: "preview" | "view" | "edit";
	onChange?: (value: any) => void;
};

export const InfoImageFieldRenderer: React.FC<TextFieldRendererProps> = ({
	field,
}) => {
	return (
		<div className="mx-auto flex max-h-[400px] max-w-[480px] items-center gap-2">
			<img
				src={field?.image || ""}
				alt={field.title}
				className="h-full w-full object-cover"
			/>
		</div>
	);
};
