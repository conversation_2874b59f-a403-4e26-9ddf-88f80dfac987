import { InputPhone } from "@/components/common/InputPhone";
import { InputText } from "@/components/common/InputText";
import { Uploader } from "@/components/common/Uploader";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { IoIosArrowBack } from "react-icons/io";
import { addPatientSchema, type AddPatientSchemaType } from "../schema/add-patient";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { uploadImage } from "@/lib/api/upload";
import { useState } from "react";
import { UploadCard } from "@/components/ui-components/Upload";
import { useAddPatient } from "../store/slices/patientsSlice";
import { Check } from "lucide-react";
import type { ClientsTypes } from "../types";

export default function AddNewPatient({
    children,
    open,
    onOpenChange,
    onPatientCreated
}: {
    open: boolean
    onOpenChange: (open: boolean) => void
    children: React.ReactNode
    onPatientCreated?: (patient: ClientsTypes.CreatePatientResponse['data']) => void
}) {
    const [isUploading, setIsUploading] = useState(false);
    const [file, setFile] = useState<File | null>(null);
    const [profilePictureUrl, setProfilePictureUrl] = useState<string | null>(null);
    const addNewPatientForm = useForm<AddPatientSchemaType>({
        resolver: zodResolver(addPatientSchema),
        defaultValues: {
            first_name: "",
            last_name: "",
            email: "",
            phone_number: "",
            profile_picture_url: "",
        }
    })
    const [isSuccess, setIsSuccess] = useState(false);
    const [createdPatient, setCreatedPatient] = useState<ClientsTypes.CreatePatientResponse['data'] | null>(null);
    const { mutate: addPatient, isPending: isAddingPatient } = useAddPatient()

    const reset = () => {
        addNewPatientForm.reset()
        setFile(null)
        setProfilePictureUrl(null)
        setCreatedPatient(null)
    }

    const onSubmit = (data: AddPatientSchemaType) => {
        addPatient({
            first_name: data.first_name,
            last_name: data.last_name,
            email: data.email,
            phone_number: data.phone_number,
            profile_picture_url: profilePictureUrl || "",
        }, {
            onSuccess: (response) => {
                setCreatedPatient(response.data);
                setIsSuccess(true);
            },
            onError: () => {
                setIsSuccess(false);
            }
        })
    }

    const handleContinueWithPatient = () => {
        if (createdPatient && onPatientCreated) {
            onPatientCreated(createdPatient);
            onOpenChange(false);
            reset();
            setIsSuccess(false);
        }
    };

    const uploadFileToServer = async (file: File) => {
        setIsUploading(true);
        try {
            const url = await uploadImage(file);
            setProfilePictureUrl(url);
            console.log("File uploaded successfully:", url);
            console.log("Profile picture URL set to:", url);
        } catch (error) {
            console.error("Error uploading file:", error);
            // Reset file state on error
            setFile(null);
            setProfilePictureUrl(null);
        } finally {
            setIsUploading(false);
        }
    };

    const handleFileUpload = () => {
        const input = document.createElement("input");
        input.type = "file";
        input.accept = ".svg,.png,.jpg,.jpeg";
        input.onchange = async (e) => {
            const target = e.target as HTMLInputElement;
            if (target.files && target.files[0]) {
                const selectedFile = target.files[0];
                setFile(selectedFile);
                await uploadFileToServer(selectedFile);
            }
        };
        input.click();
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
    };

    const handleDrop = async (e: React.DragEvent) => {
        e.preventDefault();
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            const droppedFile = e.dataTransfer.files[0];
            setFile(droppedFile);
            await uploadFileToServer(droppedFile);
        }
    };

    const handleChangeFile = () => {
        handleFileUpload();
    };

    const handleRemoveFile = () => {
        setFile(null);
        setProfilePictureUrl(null);
    };

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetTrigger asChild>
                {children}
            </SheetTrigger>
            <SheetContent className="z-[1004] py-5 px-2 sm:max-w-[780px] overflow-scroll scrollbar-hide">
                {isSuccess ? (
                    <div className="flex flex-1 flex-col items-center justify-center gap-8">
                        <div className="flex flex-col items-center gap-11">
                            <div className="rounded-full bg-[#005893]/20 p-8">
                                <Check className="h-12 w-12 text-[#005893]" />
                            </div>
                            <div className="flex w-full max-w-72 flex-col items-center gap-3">
                                <h2 className="text-center text-xl font-semibold">
                                    Patient Added Successfully
                                </h2>
                                <p className="text-center text-sm">
                                    {createdPatient?.first_name} {createdPatient?.last_name} has been added
                                    successfully. You can now continue scheduling their appointment.
                                </p>
                            </div>
                        </div>
                        <div className="flex justify-center gap-3">
                            <Button
                                variant="secondary"
                                onClick={() => {
                                    setIsSuccess(false)
                                    reset()
                                }}
                                className="h-9"
                            >
                                Add Another Patient
                            </Button>
                            {onPatientCreated ? (
                                <Button
                                    onClick={handleContinueWithPatient}
                                    className="h-9 bg-[#005893] hover:bg-[#004a7a]"
                                >
                                    Continue Appointment
                                </Button>
                            ) : (
                                <Button
                                    onClick={() => {
                                        onOpenChange(false)
                                        reset()
                                        setIsSuccess(false)
                                    }}
                                    className="h-9 w-20 bg-[#005893] hover:bg-[#004a7a]"
                                >
                                    Done
                                </Button>
                            )}
                        </div>
                    </div>
                ) : (
                    <div className="px-4 mt-3 flex flex-col justify-between min-h-[97%]">
                        <div className="flex-1">
                            <div className="flex items-center gap-x-3">
                                <Button
                                    onClick={() => onOpenChange(false)}
                                    variant="outline"
                                    className="cursor-pointer size-8"
                                >
                                    <IoIosArrowBack />
                                </Button>
                                <h1 className="text-xl font-medium">Add New Patient</h1>
                            </div>
                            <div className="grid grid-cols-2 gap-x-2.5 gap-y-6.5 mt-8">

                                <div className="flex flex-col gap-y-3">
                                    <Label htmlFor="first_name" className="font-normal">First Name</Label>
                                    <InputText
                                        placeholder="Enter your first name"
                                        className="py-4.5"
                                        {...addNewPatientForm.register("first_name")}
                                    />
                                </div>

                                <div className="flex flex-col gap-y-3">
                                    <Label htmlFor="last_name" className="font-normal">Last Name</Label>
                                    <InputText
                                        placeholder="Enter your last name"
                                        className="py-4.5"
                                        {...addNewPatientForm.register("last_name")}
                                    />
                                </div>

                                <div className="flex flex-col gap-y-3">
                                    <Label htmlFor="email" className="font-normal">Email*</Label>
                                    <InputText
                                        placeholder="Enter your email"
                                        className="py-4.5"
                                        type="email"
                                        {...addNewPatientForm.register("email")}
                                    />
                                </div>

                                <div className="flex flex-col gap-y-3">
                                    <Label htmlFor="phone_number" className="font-normal">Phone Number</Label>
                                    <Controller
                                        name="phone_number"
                                        control={addNewPatientForm.control}
                                        render={({ field }) => (
                                            <InputPhone
                                                variant="with-country-dropdown"
                                                defaultCountry="US"
                                                showFlag={true}
                                                format="international"
                                                searchable={true}
                                                showValidation={true}
                                                placeholder="Enter your mobile number"
                                                className="!py-[19px]"
                                                value={field.value}
                                                onChange={field.onChange}
                                            />
                                        )}
                                    />
                                </div>

                                <div className="col-span-full mt-3">

                                    <UploadCard
                                        variant="horizontal-compact"
                                        width="w-full"
                                        title={
                                            isUploading
                                                ? "Uploading file..."
                                                : "Click or drag file here to upload file"
                                        }
                                        description={
                                            isUploading
                                                ? "Please wait while your file is being uploaded"
                                                : profilePictureUrl
                                                    ? "File uploaded successfully! Ready to submit."
                                                    : "Recommended file type: .svg, .png, .jpg (Max of 10 mb)"
                                        }
                                        buttonText={
                                            isUploading
                                                ? "Uploading..."
                                                : "Browse"
                                        }
                                        accept=".svg,.png,.jpg,.jpeg"
                                        onBrowseClick={
                                            isUploading
                                                ? undefined
                                                : handleFileUpload
                                        }
                                        onDragOver={
                                            isUploading
                                                ? undefined
                                                : handleDragOver
                                        }
                                        onDrop={
                                            isUploading ? undefined : handleDrop
                                        }
                                        isUploaded={!!file}
                                        fileName={file?.name}
                                        fileSize={
                                            file
                                                ? `${(file.size / 1024 / 1024).toFixed(2)} MB`
                                                : undefined
                                        }
                                        filePreview={
                                            profilePictureUrl || undefined
                                        }
                                        onRemove={
                                            isUploading
                                                ? undefined
                                                : handleRemoveFile
                                        }
                                        onChange={
                                            isUploading
                                                ? undefined
                                                : handleChangeFile
                                        }
                                    />

                                </div>

                                <div className="col-span-2 flex flex-col gap-y-3">
                                    <Label htmlFor="validation_1" className="font-normal">[Validation Field 1]</Label>
                                    <InputText
                                        placeholder="Validation 1"
                                        name="validation_1"
                                        id="validation_1"
                                        className="py-4.5"
                                    />
                                </div>

                                <div className="col-span-2 flex flex-col gap-y-3">
                                    <Label htmlFor="validation_2" className="font-normal">[Validation Field 2]</Label>
                                    <InputText
                                        placeholder="Validation 2"
                                        name="validation_2"
                                        id="validation_2"
                                        className="py-4.5"
                                    />
                                </div>

                            </div>
                        </div>
                        <div className="flex items-center justify-end gap-x-3 mt-7">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                    onOpenChange(false)
                                    reset()
                                    setIsSuccess(false)
                                }}
                                className="bg-[#F4F4F5] cursor-pointer"
                            >
                                Back
                            </Button>
                            <Button
                                type="button"
                                className="cursor-pointer"
                                onClick={addNewPatientForm.handleSubmit(onSubmit)}
                            >
                                {isAddingPatient ? "Saving..." : "Save Patient"}
                            </Button>
                        </div>
                    </div>
                )}
            </SheetContent>
        </Sheet>
    )
}