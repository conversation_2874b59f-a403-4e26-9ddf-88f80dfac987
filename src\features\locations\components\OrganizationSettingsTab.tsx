import React, { useState } from "react";
import {
	Building2,
	Shield,
	Globe,
	Key,
	Palette,
	Puzzle,
	Search,
} from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { InputText } from "@/components/common/InputText";
import OrganizationInformation from "./organization-settings/OrganizationInformation";
import SecuritySettings from "./organization-settings/SecuritySettings";
import DomainSetup from "./organization-settings/DomainSetup";
import SamlSsoSettings from "./organization-settings/SamlSsoSettings";
import ThemeSettings from "./organization-settings/ThemeSettings";
import IntegrationsPlugins from "./organization-settings/IntegrationsPlugins";

interface SettingsTabItem {
	id: string;
	label: string;
	icon: React.ComponentType<{ className?: string }>;
}

const tabItems: SettingsTabItem[] = [
	{ id: "org-info", label: "Organization Information", icon: Building2 },
	{ id: "security", label: "Security", icon: Shield },
	{ id: "domain", label: "Domain Setup", icon: Globe },
	{ id: "sso", label: "SAML single sign-on (SSO)", icon: Key },
	{ id: "theme", label: "Theme", icon: Palette },
	{ id: "integrations", label: "Integrations & Plugins", icon: Puzzle },
];

const OrganizationSettingsTab: React.FC = () => {
	const [searchTerm, setSearchTerm] = useState("");

	const getTabTitle = (tabId: string) => {
		switch (tabId) {
			case "org-info":
				return "Organization Information";
			case "security":
				return "Security Settings";
			case "domain":
				return "Domain Setup";
			case "sso":
				return "SAML Single Sign-On (SSO)";
			case "theme":
				return "Theme Settings";
			case "integrations":
				return "Integrations & Plugins";
			default:
				return "Organization Information";
		}
	};

	return (
		<>
			<div className="flex items-center justify-between py-5 border-b">
				<h1 className="text-2xl font-bold">Organization Settings</h1>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="min-w-[293px] pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={<Search className="h-4 w-4" />}
							iconPosition="left"
						/>
					</div>
				</div>
			</div>

			<Tabs
				defaultValue="org-info"
				className="mt-4 flex h-full flex-row overflow-auto"
			>
				{/* Vertical Sidebar Navigation */}
				<TabsList className="h-fit w-64 flex-col items-stretch justify-start space-y-1 border-r border-gray-200 bg-transparent p-4">
					<div>
						{tabItems.map((item) => {
							const IconComponent = item.icon;
							return (
								<TabsTrigger
									key={item.id}
									value={item.id}
									className={cn(
										"group flex h-auto w-full cursor-pointer items-center justify-start gap-3 rounded-lg px-3 py-2 text-left text-sm font-medium transition-colors",
										"data-[state=active]:bg-sidebar-accent data-[state=active]:text-gray-900 data-[state=active]:shadow-sm",
										"data-[state=inactive]:hover:bg-sidebar-accent data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-900",
										"border-none bg-transparent"
									)}
								>
									<IconComponent
										className={cn(
											"h-4 w-4 transition-colors",
											"group-data-[state=active]:text-[#3B5566]",
											"group-data-[state=inactive]:text-[#8CA3B2]",
											"group-data-[state=inactive]:group-hover:text-[#3B5566]"
										)}
									/>
									{item.label}
								</TabsTrigger>
							);
						})}
					</div>
				</TabsList>

				{/* Main Content Area */}
				<div className="flex flex-1 flex-col">
					<TabsContent value="org-info" className="m-0 flex-1">
						<div className="px-10.5">
						<OrganizationInformation />
						</div>
					</TabsContent>

					<TabsContent value="security" className="m-0 flex-1">
						<div className="px-10.5">
							<SecuritySettings />
						</div>
					</TabsContent>

					<TabsContent value="domain" className="m-0 flex-1">
						<div className="px-10.5">
							<DomainSetup />
						</div>
					</TabsContent>

					<TabsContent value="sso" className="m-0 flex-1">
						<div className="px-10.5">
							<SamlSsoSettings />
						</div>
					</TabsContent>

					<TabsContent value="theme" className="m-0 flex-1">
						<div className="px-10.5">
							<ThemeSettings />
						</div>
					</TabsContent>

					<TabsContent value="integrations" className="m-0 flex-1">
						<div className="px-10.5">
							<IntegrationsPlugins />
						</div>
					</TabsContent>
				</div>
			</Tabs>
		</>
	);
};

export default OrganizationSettingsTab;
