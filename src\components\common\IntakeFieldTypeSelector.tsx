import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Minus, Plus, X } from "lucide-react";

const customFieldTypeOptions = [
	{ value: "text", label: "Text" },
	{ value: "longtext", label: "Long Text" },
	{ value: "numeric", label: "Number" },
	{ value: "date", label: "Date" },
	{ value: "date_range", label: "Date Range" },
	{ value: "dropdown", label: "Dropdown" },
	{ value: "radio", label: "Radio" },
	{ value: "checkbox", label: "Checkbox" },
	{ value: "attachment", label: "Attachment" },
	{ value: "info_image", label: "Informational Image" },
	{ value: "info_text", label: "Informational Text" },
	{ value: "scale_1_10", label: "Scale (1-10)" },
	{ value: "satisfaction_scale", label: "Scale (Satisfied to Dissatisfied)" },
	{ value: "agree_disagree", label: "Scale (Agree to Disagree)" },
	{ value: "yes_no", label: "Yes / No" },
	{ value: "rating", label: "Rating (0-5 stars)" },
];

interface IntakeFieldTypeSelectorProps {
	fieldTitle: string;
	onFieldTitleChange: (title: string) => void;
	fieldDescription?: string;
	onFieldDescriptionChange: (description: string) => void;
	fieldType: string;
	onFieldTypeChange: (type: string) => void;
	required: boolean;
	onRequiredChange: (required: boolean) => void;
	options?: Array<{
		id: string;
		value: string;
		label: string;
		order: number;
	}>;
	onOptionsChange?: (
		options: Array<{
			id: string;
			value: string;
			label: string;
			order: number;
		}>
	) => void;
	disabled?: boolean;
	showRequired?: boolean;
	showDescription?: boolean;
	className?: string;
}

export const IntakeFieldTypeSelector = ({
	fieldTitle,
	onFieldTitleChange,
	fieldDescription = "",
	onFieldDescriptionChange,
	fieldType,
	onFieldTypeChange,
	required,
	onRequiredChange,
	options = [],
	onOptionsChange,
	disabled = false,
	showRequired = true,
	showDescription = true,
	className = "",
}: IntakeFieldTypeSelectorProps) => {
	const [showDescriptionField, setShowDescriptionField] = useState(false);

	// Check if field type requires options
	const requiresOptions = ["dropdown", "radio", "checkbox"].includes(
		fieldType
	);

	// Handle adding new option
	const handleAddOption = () => {
		if (!onOptionsChange) return;
		const newOption = {
			id: crypto.randomUUID(),
			value: `Option ${options.length + 1}`,
			label: `Option ${options.length + 1}`,
			order: options.length,
		};
		onOptionsChange([...options, newOption]);
	};

	// Handle removing option
	const handleRemoveOption = (optionId: string) => {
		if (!onOptionsChange) return;
		const filteredOptions = options.filter(
			(option) => option.id !== optionId
		);
		// Update order after removing
		const reorderedOptions = filteredOptions.map((option, index) => ({
			...option,
			order: index,
		}));
		onOptionsChange(reorderedOptions);
	};

	// Handle updating option
	const handleUpdateOption = (optionId: string, value: string) => {
		if (!onOptionsChange) return;
		onOptionsChange(
			options.map((option) =>
				option.id === optionId
					? { ...option, value, label: value }
					: option
			)
		);
	};

	return (
		<div className={`space-y-4 ${className}`}>
			<div className="flex flex-col gap-6">
				{/* Field Title */}
				<div className="flex-1">
					<Label className="text-xs font-medium">
						{fieldType === "info_image"
							? "Enter Informational Text"
							: "Field Title"}{" "}
						<span className="text-red-500">*</span>
					</Label>
					<Input
						value={fieldTitle}
						onChange={(e) => onFieldTitleChange(e.target.value)}
						placeholder="Field Title"
						className="mt-2 text-xs placeholder:text-xs"
						disabled={disabled}
					/>
				</div>

				{/* Description Toggle */}
				{showDescription && !showDescriptionField && (
					<button
						type="button"
						className="-mt-2 ml-2.5 flex cursor-pointer items-center gap-3 text-xs font-normal text-[#060D25]"
						onClick={() =>
							setShowDescriptionField(!showDescriptionField)
						}
					>
						<svg
							width="14"
							height="14"
							viewBox="0 0 14 14"
							fill="none"
							xmlns="http://www.w3.org/2000/svg"
						>
							<path
								d="M4.6665 7.00033H9.33317M6.99984 4.66699V9.33366M12.8332 7.00033C12.8332 10.222 10.2215 12.8337 6.99984 12.8337C3.77818 12.8337 1.1665 10.222 1.1665 7.00033C1.1665 3.77866 3.77818 1.16699 6.99984 1.16699C10.2215 1.16699 12.8332 3.77866 12.8332 7.00033Z"
								stroke="#005893"
								strokeWidth="1.5"
								strokeLinecap="round"
								strokeLinejoin="round"
							/>
						</svg>
						<span className="leading-none">Add Description</span>
					</button>
				)}

				{/* Description Field */}
				{showDescription && showDescriptionField && (
					<div>
						<Label className="text-xs">Description</Label>
						<Textarea
							value={fieldDescription}
							onChange={(e) =>
								onFieldDescriptionChange(e.target.value)
							}
							placeholder="Add a description for this field"
							className="mt-2 text-xs placeholder:text-xs"
							disabled={disabled}
						/>
						<button
							type="button"
							className="!mt-2 ml-2.5 flex items-center gap-2 text-xs leading-none font-normal text-[#323539]"
							onClick={() => setShowDescriptionField(false)}
						>
							<Minus
								color="#fff"
								className="rounded-full bg-red-600"
								size={12}
							/>
							Hide Description
						</button>
					</div>
				)}

				{/* Field Type */}
				<div className="w-full border-t pt-6">
					<Label className="text-xs font-medium">
						Field Type <span className="text-red-500">*</span>
					</Label>
					<Select
						onValueChange={(value) => {
							if (typeof value === "string") {
								onFieldTypeChange(value);
							}
						}}
						value={fieldType}
						disabled={disabled}
					>
						<SelectTrigger className="mt-2 w-full text-xs placeholder:text-xs">
							<SelectValue placeholder="Select type" />
						</SelectTrigger>
						<SelectContent>
							{customFieldTypeOptions.map((option) => (
								<SelectItem
									key={option.value}
									value={option.value}
								>
									{option.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
			</div>

			{/* Options for dropdown, radio, checkbox */}
			{requiresOptions && (
				<div className="space-y-3 border-t pt-4">
					<Label className="text-xs font-medium">
						Options <span className="text-red-500">*</span>
					</Label>
					<div className="space-y-2">
						{options.map((option, index) => (
							<div
								key={option.id}
								className="flex items-center gap-2"
							>
								<Input
									value={option.value}
									onChange={(e) =>
										handleUpdateOption(
											option.id,
											e.target.value
										)
									}
									placeholder={`Option ${index + 1}`}
									className="flex-1 text-xs"
									disabled={disabled}
								/>
								<Button
									type="button"
									variant="outline"
									size="sm"
									onClick={() =>
										handleRemoveOption(option.id)
									}
									disabled={disabled || options.length <= 1}
									className="h-8 w-8 p-0"
								>
									<X className="h-4 w-4" />
								</Button>
								{index === options.length - 1 && (
									<Button
										type="button"
										variant="outline"
										size="sm"
										onClick={handleAddOption}
										disabled={disabled}
										className="h-8 w-8 p-0"
									>
										<Plus className="h-4 w-4" />
									</Button>
								)}
							</div>
						))}
						{options.length === 0 && (
							<Button
								type="button"
								variant="outline"
								onClick={handleAddOption}
								disabled={disabled}
								className="w-full text-xs"
							>
								<Plus className="mr-2 h-4 w-4" />
								Add Option
							</Button>
						)}
					</div>
				</div>
			)}

			{/* Required Toggle */}
			{showRequired && (
				<div className="flex items-center justify-end space-x-2 border-t pt-4">
					<Switch
						checked={required}
						onCheckedChange={onRequiredChange}
						disabled={fieldType === "info_image" || disabled}
						className="data-[state=checked]:bg-[#28C466]"
					/>
					<span className="text-sm font-medium">Required</span>
				</div>
			)}
		</div>
	);
};
