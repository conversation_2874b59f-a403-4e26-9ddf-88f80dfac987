import { apiClient } from "./clients";

export interface IntakeFieldOption {
	id: number;
	label: string;
	custom_intake_id: number;
}

export interface IntakeField {
	id: number;
	key: string;
	name: string;
	subtitle?: string;
	type: string;
	apply_to: string;
	field_requirement: "yes" | "no" | "optional";
	allow_multiple: boolean;
	long_text: boolean;
	numeric_unit_title?: string;
	info_text_value?: string;
	approved_formats?: string[];
	image?: string;
	order: number;
	is_visible: boolean;
	options?: IntakeFieldOption[];
	created_at: string;
	updated_at: string;
}

export interface CustomIntakeResponse {
	success: boolean;
	data: IntakeField[];
	meta: Record<string, any>;
	message: string;
}

export interface GetCustomIntakesParams {
	limit?: number;
	offset?: number;
	organizationId: string;
}

export interface CreateIntakeFieldData {
	name: string;
	subtitle?: string;
	type: string;
	apply_to: string;
	field_requirement: "yes" | "no" | "optional";
	allow_multiple?: boolean;
	long_text?: boolean;
	numeric_unit_title?: string;
	info_text_value?: string;
	approved_formats?: string[];
	image?: string;
	order?: number;
	is_visible?: boolean;
	options?: Omit<IntakeFieldOption, "id" | "custom_intake_id">[];
}

export interface UpdateIntakeFieldData extends Partial<CreateIntakeFieldData> {
	id: number;
}

const CUSTOM_INTAKES_ENDPOINTS = {
	base: "/api/v1/custom-intakes",
	byId: (id: number) => `/api/v1/custom-intakes/${id}`,
} as const;

export const customIntakesApi = {
	// Get all custom intake fields
	getCustomIntakes: async (
		params: GetCustomIntakesParams
	): Promise<CustomIntakeResponse> => {
		const { organizationId, limit = 15, offset = 0 } = params;

		const response = await apiClient.get(CUSTOM_INTAKES_ENDPOINTS.base, {
			params: { limit, offset },
			headers: {
				"X-organizationId": organizationId,
			},
		});
		return response.data;
	},

	// Get single custom intake field
	getCustomIntake: async (
		id: number,
		organizationId: string
	): Promise<{ success: boolean; data: IntakeField; message: string }> => {
		const response = await apiClient.get(
			CUSTOM_INTAKES_ENDPOINTS.byId(id),
			{
				headers: {
					"X-organizationId": organizationId,
				},
			}
		);
		return response.data;
	},

	// Create new custom intake field
	createCustomIntake: async (
		data: CreateIntakeFieldData,
		organizationId: string
	): Promise<{ success: boolean; data: IntakeField; message: string }> => {
		const response = await apiClient.post(
			CUSTOM_INTAKES_ENDPOINTS.base,
			data,
			{
				headers: {
					"X-organizationId": organizationId,
				},
			}
		);
		return response.data;
	},

	// Update custom intake field
	updateCustomIntake: async (
		data: UpdateIntakeFieldData,
		organizationId: string
	): Promise<{ success: boolean; data: IntakeField; message: string }> => {
		const { id, ...updateData } = data;
		const response = await apiClient.put(
			CUSTOM_INTAKES_ENDPOINTS.byId(id),
			updateData,
			{
				headers: {
					"X-organizationId": organizationId,
				},
			}
		);
		return response.data;
	},

	// Delete custom intake field
	deleteCustomIntake: async (
		id: number,
		organizationId: string
	): Promise<{ success: boolean; message: string }> => {
		const response = await apiClient.delete(
			CUSTOM_INTAKES_ENDPOINTS.byId(id),
			{
				headers: {
					"X-organizationId": organizationId,
				},
			}
		);
		return response.data;
	},

	// Toggle field requirement
	updateFieldRequirement: async (
		id: number,
		requirement: "yes" | "no" | "optional",
		organizationId: string
	): Promise<{ success: boolean; data: IntakeField; message: string }> => {
		const response = await apiClient.patch(
			CUSTOM_INTAKES_ENDPOINTS.byId(id),
			{ field_requirement: requirement },
			{
				headers: {
					"X-organizationId": organizationId,
				},
			}
		);
		return response.data;
	},

	// Toggle field visibility
	updateFieldVisibility: async (
		id: number,
		isVisible: boolean,
		organizationId: string
	): Promise<{ success: boolean; data: IntakeField; message: string }> => {
		const response = await apiClient.patch(
			CUSTOM_INTAKES_ENDPOINTS.byId(id),
			{ is_visible: isVisible },
			{
				headers: {
					"X-organizationId": organizationId,
				},
			}
		);
		return response.data;
	},
};
