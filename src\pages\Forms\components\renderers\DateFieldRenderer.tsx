import type { FormField } from "../../types/Form";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";

type DateFieldRendererProps = {
	field: FormField;
	value: any;
	mode: "preview" | "view" | "edit";
	onChange?: (value: any) => void;
};

export const DateFieldRenderer: React.FC<DateFieldRendererProps> = ({
	field,
	value,
	mode,
	onChange,
}) => {
	if (mode === "view") {
		return (
			<div>
				{value ?? <span className="text-slate-400">No response</span>}
			</div>
		);
	}
	return (
		<DatePicker
			variant="default"
			value={mode === "edit" ? value : undefined}
			onChange={mode === "edit" && onChange ? onChange : undefined}
			disabled={mode !== "edit"}
			className="h-9 w-full"
		/>
	);
};
