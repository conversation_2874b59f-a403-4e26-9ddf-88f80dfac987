import { apiClient } from "@/lib/api/clients";

export type CheckTypesData = {
    uuid: string;
    check_type: string;
    name: string;
    description: string;
    config_schema: {
        fields: {
            patient_id: {
                type: string;
                required: boolean;
            };
            response_type: {
                type: string;
                required: boolean;
            };
            timeframe: {
                type: string;
                required: boolean;
            };
        };
    };
    default_config: {
        timeframe: number;
    };
    required_fields: null;
    optional_fields: null;
    is_active: boolean;
    is_system: boolean;
    sort_order: number;
    display_name: string;
    created_at: string;
    updated_at: string;
};

export const checkApi = {
    getChecks: async (
        organizationId: number,
        business_id: string,
    ): Promise<{
        success: boolean;
        data: CheckTypesData[];
    }> => {
        const response = await apiClient.get(`/api/v1/automations/check-types`, {
            headers: {
                "X-Business-ID": business_id,
                "X-organizationId": organizationId,
            },
        })
        return response.data
    }
}
