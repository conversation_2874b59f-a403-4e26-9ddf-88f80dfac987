import { useCallback, useMemo } from "react";
import { useUIStore } from "@/stores/uiStore";
import type { UseModalReturn, UseDrawerReturn } from "@/types/ui";

// Optimized selectors to prevent unnecessary re-renders
const modalSelector = (state: any) => ({
	openModal: state.openModal,
	closeModal: state.closeModal,
	closeAllModals: state.closeAllModals,
	activeModal: state.activeModal,
	modalData: state.modalData,
});

const drawerSelector = (state: any) => ({
	openDrawer: state.openDrawer,
	closeDrawer: state.closeDrawer,
	closeAllDrawers: state.closeAllDrawers,
	activeDrawer: state.activeDrawer,
	drawerData: state.drawerData,
});

// Sheet selector (uses drawer state internally)
const sheetSelector = (state: any) => ({
	openSheet: state.openDrawer,
	closeSheet: state.closeDrawer,
	closeAllSheets: state.closeAllDrawers,
	activeSheet: state.activeDrawer,
	sheetData: state.drawerData,
});

/**
 * Optimized hook for modal management with performance optimizations
 *
 * Features:
 * - Selective state subscription to prevent unnecessary re-renders
 * - Memoized callbacks for better performance
 * - Type-safe modal operations
 *
 * @returns Modal management functions and state
 *
 * @example
 * ```tsx
 * const { openModal, closeModal, isOpen } = useModal();
 *
 * // Open a confirmation modal
 * openModal("confirmation", {
 *   size: "md",
 *   data: {
 *     title: "Delete Item",
 *     message: "Are you sure?",
 *     onConfirm: () => handleDelete()
 *   }
 * });
 *
 * // Check if modal is open
 * if (isOpen("confirmation")) {
 *   // Modal is currently active
 * }
 * ```
 */
export function useModal(): UseModalReturn {
	const openModal = useUIStore((state) => state.openModal);
	const closeModal = useUIStore((state) => state.closeModal);
	const closeAllModals = useUIStore((state) => state.closeAllModals);
	const activeModal = useUIStore((state) => state.activeModal);
	const modalData = useUIStore((state) => state.modalData);
	const updateModalData = useUIStore((state) => state.updateModalData);
	const modal = useMemo(
		() => ({
			openModal,
			closeModal,
			closeAllModals,
			activeModal,
			modalData,
			updateModalData,
		}),
		[
			openModal,
			closeModal,
			closeAllModals,
			activeModal,
			modalData,
			updateModalData,
		]
	);

	const isOpen = useCallback(
		(modalId: string) => modal.activeModal === modalId,
		[modal.activeModal]
	);

	return {
		...modal,
		isOpen,
	};
}

/**
 * Optimized hook for drawer management with performance optimizations
 *
 * Features:
 * - Selective state subscription to prevent unnecessary re-renders
 * - Memoized callbacks for better performance
 * - Type-safe drawer operations
 *
 * @returns Drawer management functions and state
 *
 * @example
 * ```tsx
 * const { openDrawer, closeDrawer, isOpen } = useDrawer();
 *
 * // Open a user profile drawer
 * openDrawer("user-profile", {
 *   direction: "right",
 *   size: "md",
 *   data: {
 *     userId: "123",
 *     mode: "view"
 *   }
 * });
 *
 * // Check if drawer is open
 * if (isOpen("user-profile")) {
 *   // Drawer is currently active
 * }
 * ```
 */
export function useDrawer(): UseDrawerReturn {
	const drawer = useUIStore(drawerSelector);

	const isOpen = useCallback(
		(drawerId: string) => drawer.activeDrawer === drawerId,
		[drawer.activeDrawer]
	);

	return {
		...drawer,
		isOpen,
	};
}

/**
 * Optimized hook for sheet management with performance optimizations
 *
 * Features:
 * - Selective state subscription to prevent unnecessary re-renders
 * - Memoized callbacks for better performance
 * - Type-safe sheet operations
 * - Uses the same underlying state as drawers but with sheet terminology
 *
 * @returns Sheet management functions and state
 *
 * @example
 * ```tsx
 * const { openSheet, closeSheet, isOpen } = useSheet();
 *
 * // Open a user profile sheet
 * openSheet("user-profile", {
 *   direction: "right", // maps to "side" prop on Sheet
 *   size: "md",
 *   data: {
 *     userId: "123",
 *     title: "User Profile",
 *     description: "View user information"
 *   }
 * });
 *
 * // Check if sheet is open
 * if (isOpen("user-profile")) {
 *   // Sheet is currently active
 * }
 * ```
 */
export function useSheet() {
	const sheet = useUIStore(sheetSelector);

	const isOpen = useCallback(
		(sheetId: string) => sheet.activeSheet === sheetId,
		[sheet.activeSheet]
	);

	return {
		...sheet,
		isOpen,
	};
}
