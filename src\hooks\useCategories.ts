import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
	categoriesApi,
	type CategoriesFilters,
	type CreateCategoryRequest,
	type UpdateCategoryRequest,
	type CategoryDetailExtendedResponse,
	type CategoryClientsFilters,
	type CategoryClientsResponse,
	type AddClientsToCategoryResponse,
} from "@/lib/api/categoriesApi";
import { queryKeys, mediumLivedQueryOptions } from "@/lib/query";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { toast } from "sonner";

export const useCategories = (
	filters: CategoriesFilters = {},
	options?: {
		enabled?: boolean;
	}
) => {
	const { organizationId } = useOrganizationContext();

	const filtersWithOrgId = {
		...filters,
		organization_id: organizationId || undefined,
	};

	return useQuery({
		queryKey: queryKeys.categories.list(filtersWithOrgId),
		queryFn: () => categoriesApi.getCategories(filtersWithOrgId),
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!organizationId,
	});
};

export const useCategoryDetail = (
	id: string | number,
	options?: {
		enabled?: boolean;
	}
) => {
	const { organizationId } = useOrganizationContext();

	return useQuery<CategoryDetailExtendedResponse>({
		queryKey: queryKeys.categories.detail(id.toString()),
		queryFn: () => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}
			return categoriesApi.getCategoryById(id, organizationId);
		},
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!id && !!organizationId,
	});
};

export const useCreateCategory = (options?: {
	onSuccess?: (data: any) => void;
	onError?: (error: any) => void;
}) => {
	const { organizationId } = useOrganizationContext();
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: CreateCategoryRequest) => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}
			return categoriesApi.createCategory(data, organizationId);
		},
		onSuccess: (data: any) => {
			toast.success(data.message || "Category created successfully");
			queryClient.invalidateQueries({
				queryKey: queryKeys.categories.lists(),
			});

			options?.onSuccess?.(data);
		},
		onError: (error: any) => {
			toast.error(
				error?.response?.data?.message ||
					error?.message ||
					"Failed to create category"
			);
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};

export const useUpdateCategory = (options?: {
	onSuccess?: (data: any) => void;
	onError?: (error: any) => void;
}) => {
	const { organizationId } = useOrganizationContext();
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: UpdateCategoryRequest) => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}
			return categoriesApi.updateCategory(data, organizationId);
		},
		onSuccess: (data: any) => {
			toast.success(data.message || "Category updated successfully");
			queryClient.invalidateQueries({
				queryKey: queryKeys.categories.lists(),
			});
			queryClient.invalidateQueries({
				queryKey: queryKeys.categories.detail(
					data.data?.id?.toString()
				),
			});

			options?.onSuccess?.(data);
		},
		onError: (error: any) => {
			toast.error(
				error?.response?.data?.message ||
					error?.message ||
					"Failed to update category"
			);
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};

export const useDeleteCategory = (options?: {
	onSuccess?: () => void;
	onError?: (error: any) => void;
}) => {
	const { organizationId } = useOrganizationContext();
	const queryClient = useQueryClient();

	return useMutation<void, any, string | number>({
		mutationFn: (id: string | number) => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}
			return categoriesApi.deleteCategory(id, organizationId);
		},
		onSuccess: () => {
			toast.success("Category deleted successfully");
			queryClient.invalidateQueries({
				queryKey: queryKeys.categories.lists(),
			});

			options?.onSuccess?.();
		},
		onError: (error: any) => {
			toast.error(
				error?.response?.data?.message ||
					error?.message ||
					"Failed to delete category"
			);
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};

export const useCategoryClients = (
	categoryId: string | number,
	filters: CategoryClientsFilters = {},
	options?: {
		enabled?: boolean;
	}
) => {
	const { organizationId } = useOrganizationContext();

	return useQuery<CategoryClientsResponse>({
		queryKey: queryKeys.categories.clients(categoryId.toString(), filters),
		queryFn: () => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}
			return categoriesApi.getCategoryClients(categoryId, {
				...filters,
				organization_id: organizationId,
			});
		},
		...mediumLivedQueryOptions,
		enabled: options?.enabled !== false && !!categoryId && !!organizationId,
	});
};

export const useAddClientsToCategory = (options?: {
	onSuccess?: (data: AddClientsToCategoryResponse) => void;
	onError?: (error: any) => void;
}) => {
	const queryClient = useQueryClient();
	const { organizationId } = useOrganizationContext();

	return useMutation({
		mutationFn: ({
			categoryId,
			clientIds,
		}: {
			categoryId: string | number;
			clientIds: number[];
		}) => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}
			return categoriesApi.addClientsToCategory(
				categoryId,
				clientIds,
				organizationId
			);
		},
		onSuccess: (data: AddClientsToCategoryResponse) => {
			toast.success(
				data.message || "Patients added to category successfully"
			);
			queryClient.invalidateQueries({
				queryKey: queryKeys.categories.clientsAll(),
			});
			queryClient.invalidateQueries({
				queryKey: queryKeys.categories.lists(),
			});

			options?.onSuccess?.(data);
		},
		onError: (error: any) => {
			toast.error(
				error?.response?.data?.message ||
					error?.message ||
					"Failed to add patients to category"
			);
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};

export const useDetachClientsFromCategory = (options?: {
	onSuccess?: () => void;
	onError?: (error: any) => void;
}) => {
	const queryClient = useQueryClient();
	const { organizationId } = useOrganizationContext();

	return useMutation({
		mutationFn: ({
			categoryId,
			clientIds,
		}: {
			categoryId: string | number;
			clientIds: number[];
		}) => {
			if (!organizationId) {
				throw new Error("Organization ID is required");
			}
			return categoriesApi.detachClientsFromCategory(
				categoryId,
				clientIds,
				organizationId
			);
		},
		onSuccess: () => {
			toast.success("Patient removed from category successfully");
			queryClient.invalidateQueries({
				queryKey: queryKeys.categories.clientsAll(),
			});
			queryClient.invalidateQueries({
				queryKey: queryKeys.categories.lists(),
			});

			options?.onSuccess?.();
		},
		onError: (error: any) => {
			toast.error(
				error?.response?.data?.message ||
					error?.message ||
					"Failed to remove patient from category"
			);
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};
