// CategoryFilter.tsx - Pure UI component with dummy data
import MultiSelect from "@/components/ui-extended/multi-select";
import React from "react";

interface Category {
  id: number;
  name: string;
}

interface CategoryFilterProps {
  // Data props
  categories?: Category[];
  selectedCategoryIds?: number[];
  
  // Event handlers
  onCategoryChange?: (categoryIds: number[]) => void;
  onSelect?: (selectedOptions: Array<{ value: string; label: string }>) => void;
  onUnselect?: (unselectedOption: { value: string; label: string }) => void;
  
  // UI props
  placeholder?: string;
  className?: string;
  width?: string;
  textContainerClassName?: string;
  disabled?: boolean;
}

// Default dummy categories data
const DEFAULT_CATEGORIES: Category[] = [
  { id: 1, name: "Adult" },
  { id: 2, name: "Pediatric" },
  { id: 3, name: "Senior" },
  { id: 4, name: "Special Needs" },
  { id: 5, name: "Emergency" },
  { id: 6, name: "Chronic Care" },
  { id: 7, name: "Preventive Care" }
];

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories = DEFAULT_CATEGORIES,
  selectedCategoryIds = [],
  onCategoryChange,
  onSelect,
  onUnselect,
  placeholder = "Categories",
  className = "h-9",
  width = "100%",
  textContainerClassName = "text-left justify-between",
  disabled = false,
}) => {
  const handleSelect = (selectedOptions: Array<{ value: string; label: string }>) => {
    const categoryIds = selectedOptions.map(item => parseInt(item.value));
    
    // Call both handlers
    onCategoryChange?.(categoryIds);
    onSelect?.(selectedOptions);
  };

  const handleUnselect = (unselectedOption: { value: string; label: string }) => {
    const updatedCategoryIds = selectedCategoryIds.filter(
      id => id !== parseInt(unselectedOption.value)
    );
    
    // Call both handlers
    onCategoryChange?.(updatedCategoryIds);
    onUnselect?.(unselectedOption);
  };

  // Convert categories to options format
  const options = categories.map(category => ({
    value: category.id.toString(),
    label: category.name,
  }));

  // Convert selected category IDs to selected options format
  const selectedOptions = selectedCategoryIds.map(id => {
    const category = categories.find(c => c.id === id);
    return {
      value: id.toString(),
      label: category?.name || `Category ${id}`,
    };
  }).filter(option => option.label !== `Category ${option.value}`); // Filter out invalid categories

  return (
    <MultiSelect
      width={width}
      options={options}
      placeholder={placeholder}
      selected={selectedOptions}
      onSelect={handleSelect}
      onUnselect={handleUnselect}
      className={className}
      textContainerClassName={textContainerClassName}
      // disabled={disabled}
    />
  );
};

export default CategoryFilter;