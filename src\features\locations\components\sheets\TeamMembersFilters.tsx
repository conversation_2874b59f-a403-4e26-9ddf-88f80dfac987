import { useState } from "react";
import {
	<PERSON>,
	Fi<PERSON>,
	<PERSON>r,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>,
	<PERSON>,
	Star,
} from "lucide-react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ooter,
	<PERSON><PERSON><PERSON>eader,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import MultiAsyncSelect from "@/components/common/MultiAsyncSelect";
import { Checkbox } from "@/components/ui/checkbox";
import { ToggleButton } from "@/components/common/ToggleButton";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useLocations } from "@/features/locations/hooks/useLocations";
import { useAllStations } from "@/features/locations/hooks/useStations";

interface FilterData {
	locations: string[];
	stations: string[];
	roles: string[];
	status: string[];
	dateFrom?: string;
	dateTo?: string;
	sortBy?: string;
}

interface FilterSheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onApplyFilters?: (filters: FilterData) => void;
}

export function TeamMembersFilters({
	open,
	onOpenChange,
	onApplyFilters,
}: FilterSheetProps) {
	const { organizationId } = useOrganizationContext();

	const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
	const [selectedStatus, setSelectedStatus] = useState<string[]>(["active"]);
	const [selectedLocations, setSelectedLocations] = useState<string[]>([
		"all",
	]);
	const [selectedStations, setSelectedStations] = useState<string[]>(["all"]);
	const [dateFrom, setDateFrom] = useState<string>("");
	const [dateTo, setDateTo] = useState<string>("");
	const [sortBy, setSortBy] = useState<string>("name");

	// Fetch real data from APIs
	const { data: locationsData, isLoading: isLoadingLocations } = useLocations(
		{},
		organizationId || undefined
	);

	const { data: stationsData, isLoading: isLoadingStations } = useAllStations(
		{
			organizationId: organizationId || undefined,
			enabled: !!organizationId,
		}
	);

	const [filters, setFilters] = useState<FilterData>({
		locations: [],
		stations: [],
		roles: [],
		status: ["active", "inactive"],
	});

	const handleLocationRemove = (location: string) => {
		setFilters((prev) => ({
			...prev,
			locations: prev.locations.filter((l) => l !== location),
		}));
	};

	const handleStationRemove = (station: string) => {
		setFilters((prev) => ({
			...prev,
			stations: prev.stations.filter((s) => s !== station),
		}));
	};

	const handleRoleChange = (roles: string[]) => {
		setFilters((prev) => ({
			...prev,
			roles: roles,
		}));
	};

	const handleStatusChange = (statuses: string[]) => {
		setFilters((prev) => ({
			...prev,
			status: statuses,
		}));
	};

	const handleReset = () => {
		setSelectedLocations(["all"]);
		setSelectedStations(["all"]);
		setSelectedRoles([]);
		setSelectedStatus([]);
		setDateFrom("");
		setDateTo("");
		setSortBy("name");
		setFilters({
			locations: [],
			stations: [],
			roles: [],
			status: [],
		});
	};

	const handleApply = () => {
		// Transform selected values to actual data for filtering
		const appliedFilters: FilterData = {
			locations: selectedLocations.includes("all")
				? []
				: selectedLocations.filter((id) => id !== "all"),
			stations: selectedStations.includes("all")
				? []
				: selectedStations.filter((id) => id !== "all"),
			roles: selectedRoles,
			status: selectedStatus,
			dateFrom: dateFrom || undefined,
			dateTo: dateTo || undefined,
			sortBy: sortBy,
		};

		onApplyFilters?.(appliedFilters);
		onOpenChange(false);
	};

	const handleCancel = () => {
		onOpenChange(false);
	};

	// Transform API data to options format
	const locationOptions = [
		{ value: "all", label: "All Locations" },
		...(locationsData?.map((location) => ({
			value: location.id.toString(),
			label: location.name,
		})) || []),
	];

	const stationOptions = [
		{ value: "all", label: "All Stations" },
		...(stationsData?.data?.map((station) => ({
			value: station.id.toString(),
			label: station.name,
		})) || []),
	];

	// Define available roles with icons
	const roleOptions = [
		{
			value: "BUSINESS_MANAGER",
			label: "Business Manager",
		},
		{
			value: "LOCATION_MANAGER",
			label: "Location Manager",
		},
		{
			value: "STATION_MANAGER",
			label: "Station Manager",
		},
		{
			value: "SERVICE_MANAGER",
			label: "Service Manager",
		},
		{
			value: "TEAM_MEMBER",
			label: "Team Member",
		},
	];

	// Sort options
	const sortOptions = [
		{ value: "name", label: "Name" },
		{ value: "email", label: "Email" },
		{ value: "dateOnboarded", label: "Date Onboarded" },
		{ value: "role", label: "Role" },
	];

	return (
		<Sheet open={open} onOpenChange={onOpenChange}>
			<SheetContent className="z-[1003] w-full overflow-y-auto px-9 py-9 sm:w-[540px] sm:max-w-[525px] [&>button]:hidden">
				<SheetHeader className="p-0">
					<SheetTitle className="flex items-center justify-between">
						<span>Filter Team Members</span>
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="h-6 w-6"
						>
							<X className="h-4 w-4" />
						</Button>
					</SheetTitle>
					<p className="text-muted-foreground text-sm">
						Filter team members by location, station, role and more
					</p>
				</SheetHeader>

				<div className="space-y-4 py-6">
					{/* Locations Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Locations
						</label>
						<MultiAsyncSelect
							options={locationOptions}
							onValueChange={setSelectedLocations}
							defaultValue={selectedLocations}
							placeholder={
								isLoadingLocations
									? "Loading locations..."
									: "Select locations"
							}
							className="w-full"
							disabled={isLoadingLocations}
						/>
					</div>

					{/* Stations Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Stations
						</label>
						<MultiAsyncSelect
							options={stationOptions}
							onValueChange={setSelectedStations}
							defaultValue={selectedStations}
							placeholder={
								isLoadingStations
									? "Loading stations..."
									: "Select stations"
							}
							className="w-full"
							disabled={isLoadingStations}
						/>
					</div>

					{/* Roles Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Roles
						</label>
						<MultiAsyncSelect
							options={roleOptions}
							onValueChange={setSelectedRoles}
							defaultValue={selectedRoles}
							placeholder="Select roles"
							className="w-full"
						/>
					</div>
					{/* Sort By Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							Sort By
						</label>
						<Select value={sortBy} onValueChange={(value) => setSortBy(value as string)}>
							<SelectTrigger className="w-full">
								<SelectValue placeholder="Select sort option" />
							</SelectTrigger>
							<SelectContent>
								{sortOptions.map((option) => (
									<SelectItem
										key={option.value}
										value={option.value}
									>
										{option.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
					{/* Date Range Section */}
					<div className="space-y-2">
						<label className="text-sm font-medium text-zinc-900">
							By date added
						</label>
						<div className="grid grid-cols-2 gap-3">
							<div className="space-y-1">
								<DatePicker
									placeholder="From"
									value={
										dateFrom
											? new Date(dateFrom)
											: undefined
									}
									onChange={(date) =>
										setDateFrom(
											date
												? (date as Date)
														.toISOString()
														.split("T")[0]
												: ""
										)
									}
									className="w-full"
								/>
							</div>
							<div className="space-y-1">
								<DatePicker
									placeholder="To"
									value={
										dateTo ? new Date(dateTo) : undefined
									}
									onChange={(date) =>
										setDateTo(
											date
												? (date as Date)
														.toISOString()
														.split("T")[0]
												: ""
										)
									}
									className="w-full"
								/>
							</div>
						</div>
					</div>
				</div>

				<SheetFooter className="flex-row justify-between">
					<Button
						variant="ghost"
						onClick={handleReset}
						className="text-muted-foreground hover:text-foreground"
					>
						Reset
					</Button>
					<div className="flex gap-3">
						<Button variant="outline" onClick={handleCancel}>
							Cancel
						</Button>
						<Button onClick={handleApply}>Apply</Button>
					</div>
				</SheetFooter>
			</SheetContent>
		</Sheet>
	);
}
