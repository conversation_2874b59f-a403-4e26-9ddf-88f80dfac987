import type { ColumnDef } from "@tanstack/react-table";
import type { TableRowType } from "./schema/table";
import { Checkbox } from "@/components/common/Checkbox/Checkbox";
import { useMemo } from "react";
import { Switch } from "@/components/ui/switch";
import { formatDate } from "date-fns";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/Button/Button";
import { CgTrash } from "react-icons/cg";
import { LuCopy } from "react-icons/lu";
import { LiaAngleRightSolid } from "react-icons/lia";
import { Link } from "react-router";

export function getAutomationColumns() {
    return useMemo(() => {
        const columns: ColumnDef<TableRowType>[] = [
            {
                id: "select",
                header: ({ table }) => (
                    <Checkbox
                        checked={table.getIsAllPageRowsSelected() || false}
                        onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
                        aria-label="Select all"
                        className="size-5"
                    />
                ),
                cell: ({ row }) => (
                    <Checkbox
                        checked={row.getIsSelected()}
                        onCheckedChange={value => row.toggleSelected(!!value)}
                        aria-label="Select row"
                        className="size-5"
                    />
                ),
                enableSorting: false,
                enableHiding: false,
            },
            {
                id: "title",
                filterFn: "includesString",
                header: () => (
                    <p className="text-[#71717A] !font-light text-sm">Title</p>
                ),
                cell: ({ row }) => (
                    <p className="text-sm text-[#27272A]">{row.original.title}</p>
                ),
            },
            {
                id: "status",
                filterFn: "includesString",
                header: () => (
                    <p className="text-[#71717A] !font-light text-sm">Status</p>
                ),
                cell: ({ row }) => (
                    <p className="text-sm text-[#27272A]">
                        <Switch
                            checked={row.original.status === "Active"}
                            disabled
                        />
                    </p>
                ),
            },
            {
                id: "createDate",
                filterFn: "includesString",
                header: () => (
                    <p className="text-[#71717A] !font-light text-sm">Created</p>
                ),
                cell: ({ row }) => (
                    <p className="text-sm text-[#71717A] font-light">{formatDate(row.original.createDate, "MMM d, yyyy")}</p>
                ),
            },
            {
                id: "successfulRuns",
                filterFn: "includesString",
                header: () => (
                    <p className="text-[#71717A] font-light text-sm">Successful Runs</p>
                ),
                cell: ({ row }) => (
                    <p className="text-sm text-[#289144] font-normal">{row.original.successfulRuns}</p>
                ),
            },
            {
                id: "failedRuns",
                filterFn: "includesString",
                header: () => (
                    <p className="text-[#71717A] font-light text-sm">Failed Runs</p>
                ),
                cell: ({ row }) => (
                    <p className="text-sm text-[#DC2626] font-normal">{row.original.failedRuns}</p>
                ),
            },
            {
                id: "owner",
                filterFn: "includesString",
                header: () => (
                    <p className="text-[#71717A] font-light text-sm">Owner</p>
                ),
                cell: ({ row }) => (
                    <div>
                        <Avatar>
                            <AvatarImage src={row.original.profilePicture} style={{ objectFit: "cover" }} />
                            <AvatarFallback className="uppercase text-[#A1A1AA] bg-[#E4E4E7]">
                                {row.original.owner.slice(0, 2)}
                            </AvatarFallback>
                        </Avatar>
                    </div>
                ),
            },
            {
                id: "actions",
                enableHiding: false,
                header: () => (
                    <div className="grid grid-flow-col gap-3.5 w-fit">
                        <div className="flex items-center gap-2">
                            <Switch
                                checked={true}
                                onCheckedChange={() => {
                                    console.log("checked");
                                }}
                            />
                            <p className="text-sm text-[#71717A] font-light">On</p>
                        </div>
                        <Button variant="outline" size="icon" className="h-9 w-9 rounded-lg">
                            <CgTrash className="text-[#71717A] size-4" />
                        </Button>
                    </div>
                ),
                cell: ({ row }) => (
                    <div className="grid grid-flow-col gap-3 w-fit max-w-[5rem]">
                        <Button variant="outline" size="icon" className="h-9 w-9 rounded-lg cursor-pointer">
                            <LuCopy />
                        </Button>
                        <Button variant="outline" size="icon" className="h-9 w-9 rounded-lg cursor-pointer">
                            <CgTrash />
                        </Button>
                        <Button asChild variant="outline" size="icon" className="h-9 w-9 rounded-lg cursor-pointer">
                            <Link to={`/dashboard/automation/history/${row.original.id}`}>
                                <LiaAngleRightSolid />
                            </Link>
                        </Button>
                    </div>
                )
            }
        ];

        return columns;
    }, [])
}