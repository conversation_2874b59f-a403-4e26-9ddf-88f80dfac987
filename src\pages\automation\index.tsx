import { Button } from "@/components/ui/Button/Button";
import { useUIStore } from "@/stores/uiStore";
import { useEffect, useState } from "react";
import { FiSearch } from "react-icons/fi";
import { LuSplit } from "react-icons/lu";
import NoAutomation from "./empty";
import { useNavigate } from "react-router-dom";
import AutomationList from "./automation-list";

export default function Automation() {
    const navigate = useNavigate();
    const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
    const setPhContent = useUIStore(
        (state) => state.setPageHeaderContent
    );

    useEffect(() => {
        setBreadcrumbs([
            {
                label: "Automation",
                href: "/dashboard/automation",
                isCurrentPage: true,
            },
        ]);
        return () => {
            setBreadcrumbs([]);
        };
    }, [setBreadcrumbs]);

    useEffect(() => {
        const headerContent = (
            <div className="flex flex-1 items-center justify-between">
                <div>
                    <h1 className="text-foreground text-2xl font-semibold">
                        All Automations
                    </h1>
                </div>
                <div className="flex items-center gap-x-6">
                    <FiSearch size={24} />
                    <Button
                        className="py-5 cursor-pointer flex items-center gap-x-2"
                        type="button"
                        onClick={() => navigate("/dashboard/automation/create")}
                    >
                        <LuSplit /> Add Automation
                    </Button>
                </div>
            </div>
        );

        setPhContent(headerContent);

        return () => {
            setPhContent(null);
        };
    }, [setPhContent]);

    return (
        <div>
            {/* <NoAutomation /> */}
          <AutomationList />
        </div>
    );
}