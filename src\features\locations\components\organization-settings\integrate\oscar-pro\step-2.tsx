import { MoveDown } from "lucide-react";
import restClient from "../../images/rest-client.png"
import { FaArrowDownLong } from "react-icons/fa6";

export default function IntegrateOscarProStep2() {
    return (
        <div>
            <h1 className="text-[#09090B] font-medium text-lg mb-3">Step 2</h1>
            <div className="bg-[#F4F4F5] py-5 px-6 rounded-xl">
                <h1 className="text-[#27272A] font-medium text-base">Navigate to REST Clients</h1>
                <div className="grid grid-cols-2 gap-x-3">
                    <div className="mt-5">
                        <div className="flex items-start gap-2 mb-6">
                            <p className="text-[#71717A] font-normal text-base">A.</p>
                            <p className="text-[#71717A] font-light">Go to the
                                {" "}
                                <span className="text-[#27272A] text-base font-medium">Administrative Panel</span> Top middle of the navigation panel</p>
                        </div>
                        <MoveDown  className="size-10" />
                        <div className="flex items-start gap-2 mb-6 mt-5">
                            <p className="text-[#71717A] font-normal text-base">B.</p>
                            <p className="text-[#71717A] font-light">Click on
                                {" "}
                                <span className="text-[#27272A] text-base font-medium">Integrations</span>{" "} in the Left hand panel</p>
                        </div>
                        <MoveDown className="size-10" />
                        <div className="flex items-start gap-2 mb-6 mt-5">
                            <p className="text-[#71717A] font-normal text-base">C.</p>
                            <p className="text-[#71717A] font-light">Then, click on
                                {" "}
                                <span className="text-[#27272A] text-base font-medium">REST Clients</span>{" "}Right below Integrations</p>
                        </div>
                    </div>
                    <div>
                        <img
                            src={restClient}
                            alt="REST Client"
                            className="w-full max-h-[26rem]"
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}