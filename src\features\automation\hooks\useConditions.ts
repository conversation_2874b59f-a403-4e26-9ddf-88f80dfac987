import { useOrganizationContext } from "@/features/organizations/context"
import { appointmentStatusApi, businessCategoriesApi, conditionApi, providersApi, type AppointmentStatusData, type BusinessCategoryData, type ConditionTypesData, type GetProvidersData } from "../api/conditions"
import { useQuery } from "@tanstack/react-query"

export function useGetConditions({
    business_id,
    enabled,
}: {
    business_id: string,
    enabled: boolean,
}) {
    const organizationId = useOrganizationContext()?.organizationId
    return useQuery<{
        success: boolean,
        data: ConditionTypesData[],
    }
    >({
        queryKey: ["conditions", organizationId, business_id],
        queryFn: () => {
            if (!business_id || !organizationId) {
                throw new Error("Business ID and Organization ID are required")
            }
            return conditionApi.getConditions(organizationId, business_id)
        },
        enabled
    })
}

export function useGetBusinessCategories({
    enabled
}: { enabled: boolean }) {
    return useQuery<BusinessCategoryData>({
        queryKey: ["businessCategories"],
        queryFn: () => businessCategoriesApi.getCategories(),
        enabled
    })
}

export function useGetProviders({
    enabled
}: { enabled: boolean }) {
    const organizationId = useOrganizationContext()?.organizationId
    if (!organizationId) {
        throw new Error("Organization ID is required")
    }

    return useQuery<GetProvidersData>({
        queryKey: ["locations", organizationId],
        queryFn: () => providersApi.getLocations(organizationId),
        enabled
    })
}

export function useGetAppointmentStatus({
    enabled,
}: {
    enabled: boolean,
}) {
    const organizationId = useOrganizationContext()?.organizationId
    if (!organizationId) { 
        throw new Error("Organization ID is required")
    }
    return useQuery<AppointmentStatusData>({
        queryKey: ["appointmentStatus", organizationId],
        queryFn: () => appointmentStatusApi.getAppointmentStatus(organizationId),
        enabled
    })
}