export const getInitials = (word: string) => {
	const words = word.split(" ");
	if (words.length >= 2) {
		// Use first letter of first two words
		return words
			.slice(0, 2)
			.map((word) => word[0])
			.join("")
			.toUpperCase();
	} else {
		// Use first two letters of the first word
		return words[0].slice(0, 2).toUpperCase();
	}
};

export const toTitleCase = (string?: string): string => {
	if (!string || typeof string !== "string") {
		return "";
	}

	return string
		.toLocaleLowerCase()
		.split(" ")
		.map((item) => item[0].toUpperCase() + item.slice(1, item.length))
		.join(" ");
};
