import React, { useEffect, useCallback } from "react";
import { type Control, useWatch } from "react-hook-form";
import { FormField } from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

type UUID = `${string}-${string}-${string}-${string}-${string}`;

type FlowSubmit = {
	action: "submit";
	targetSection?: undefined;
};

type FlowContinue = {
	action: "continue";
	targetSection?: UUID;
};

type SectionFlow = FlowSubmit | FlowContinue;

interface FormFlowSelectProps {
	control: Control<any>;
	sectionIndex: number;
	sections: any;
	currentValue: any;
	onValueChange: (value: SectionFlow) => void;
}

export const FormFlowSelect = ({
	control,
	sectionIndex,
	currentValue,
	onValueChange,
}: FormFlowSelectProps) => {
	// Watch the sections array for changes
	const currentSections = useWatch({
		control,
		name: "sections",
	});

	const isLastSection = sectionIndex === currentSections.length - 1;
	const availableSections = currentSections.slice(sectionIndex + 1);
	const nextSection = currentSections[sectionIndex + 1];

	const getValue = useCallback((): string => {
		if (isLastSection || currentValue?.action === "submit") return "submit";
		if (currentValue?.action === "continue" && currentValue?.targetSection) {
			const targetExists = currentSections.some(
				(s: any) => s.id === currentValue.targetSection
			);
			if (targetExists) return currentValue.targetSection;
		}
		return nextSection?.id || "submit";
	}, [isLastSection, currentValue, currentSections, nextSection]);

	useEffect(() => {
		// Only update if necessary
		if (isLastSection && currentValue?.action !== "submit") {
			onValueChange({
				action: "submit",
				targetSection: undefined,
			});
		} else if (
			!isLastSection &&
			nextSection &&
			(!currentValue ||
				!currentSections.some(
					(s: any) => s.id === currentValue?.targetSection
				))
		) {
			onValueChange({
				action: "continue",
				targetSection: nextSection.id,
			});
		}
	}, [isLastSection, nextSection?.id, currentValue?.action, currentSections]);

	const handleChange = useCallback(
		(value: string) => {
			console.log("Selected value:", value);
			console.log(
				"Available sections:",
				currentSections.map((s: any) => s.id)
			);

			if (value === "submit") {
				onValueChange({
					action: "submit",
					targetSection: undefined,
				});
				return;
			}

			const targetSection = currentSections.find(
				(s: any) => s.id === value
			);
			if (targetSection) {
				onValueChange({
					action: "continue",
					targetSection: targetSection.id,
				});
			}
		},
		[currentSections, onValueChange]
	);

	const getDisplayText = useCallback((): string => {
		if (isLastSection || currentValue?.action === "submit") {
			return "Submit form";
		}

		if (currentValue?.action === "continue") {
			const targetSectionIndex = currentSections.findIndex(
				(s: any) => s.id === currentValue.targetSection
			);
			if (targetSectionIndex !== -1) {
				return `Continue to Section ${targetSectionIndex + 1}`;
			}
		}

		return nextSection
			? `Continue to Section ${sectionIndex + 2}`
			: "Submit form";
	}, [
		isLastSection,
		currentValue,
		currentSections,
		nextSection,
		sectionIndex,
	]);

	return (
		<FormField
			control={control}
			name={`sections.${sectionIndex}.flow`}
			render={() => (
				<div className="flex items-center gap-4">
					<span className="text-sm">
						After Section {sectionIndex + 1}
					</span>
					<Select
						value={getValue()}
						onValueChange={(value) => handleChange(value as string)}
						disabled={isLastSection}
					>
						<SelectTrigger className="w-64">
							<SelectValue placeholder="Select flow">
								{getDisplayText()}
							</SelectValue>
						</SelectTrigger>
						<SelectContent>
							{availableSections.map(
								(section: any, idx: number) => (
									<SelectItem
										key={section.id}
										value={section.id}
									>
										Continue to Section{" "}
										{sectionIndex + 2 + idx}
									</SelectItem>
								)
							)}
							<SelectItem value="submit">Submit form</SelectItem>
						</SelectContent>
					</Select>
				</div>
			)}
		/>
	);
};
