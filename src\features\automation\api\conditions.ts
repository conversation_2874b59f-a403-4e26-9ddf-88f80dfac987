import { apiClient } from "@/lib/api/clients";
export type ConditionTypesData = {
    uuid: string,
    condition_type: string,
    name: string,
    description: string,
    config_schema: {
        fields: {
            provider_id: {
                type: string,
                required: boolean
            },
            operator: {
                type: string,
                required: boolean
            }
        }
    },
    default_config: any[],
    required_fields: null,
    optional_fields: null,
    is_active: boolean,
    is_system: boolean,
    sort_order: number,
    display_name: string,
    created_at: string,
    updated_at: string
}
export type BusinessCategoryData = {
    success: boolean,
    message: string,
    data: {
        id: number,
        name: string
    }[],
    meta: {
        timestamp: string,
        request_id: string,
        version: string
    }
}
export type GetProvidersData = {
    success: true,
    message: string,
    data: {
        id: number,
        name: string,
        image: null,
        description: string | null,
        locations: {
            id: number,
            name: string
        }[],
        service_providers: {
            id: number,
            first_name: string,
            last_name: string,
            email: string,
            phone_number: string,
            role: string
        }[],
        average_rating: number
    }[]
}
export type AppointmentStatusData = {
    success: true,
    message: string,
    data: {
        value: string,
        label: string,
        description: string
    }[]
}
export const conditionApi = {
    getConditions: async (
        organizationId: number,
        business_id: string,
    ): Promise<{
        success: boolean;
        data: ConditionTypesData[];
    }> => {
        const response = await apiClient.get(`/api/v1/automations/condition-types`, {
            headers: {
                "X-Business-ID": business_id,
                "X-organizationId": organizationId,
            },
        })
        return response.data
    }
}

export const businessCategoriesApi = {
    getCategories: async (): Promise<BusinessCategoryData> => {
        const response = await apiClient.get(`/api/v1/business-categories`)
        return response.data
    }
}

export const providersApi = {
    getLocations: async (
        organizationId: number
    ): Promise<GetProvidersData> => {
        const response = await apiClient.get(`/api/v1/stations`, {
            headers: {
                "X-organizationId": organizationId,
            }
        })
        return response.data
    }
}

export const appointmentStatusApi = {
    getAppointmentStatus: async (
        organizationId: number,
    ): Promise<AppointmentStatusData> => {
        const response = await apiClient.get(`/schedule/api/v1/appointments/statuses`, {
            headers: {
                "X-organizationId": organizationId,
            }
        })
        return response.data
    }
}