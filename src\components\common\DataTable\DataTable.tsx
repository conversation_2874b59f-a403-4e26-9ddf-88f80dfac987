import React from "react";
import { Checkbox } from "@/components/common/Checkbox";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import {
	Table,
	TableHeader,
	TableBody,
	TableHead,
	TableRow,
	TableCell,
} from "@/components/ui/table";
import { Plus } from "lucide-react";

export interface Column<T> {
	key: string;
	label: string;
	width?: string;
	render?: (item: T) => React.ReactNode;
}

export interface DataTableProps<T> {
	columns: Column<T>[];
	data: T[];
	isLoading?: boolean;
	error?: Error | null;
	selectedItems?: string[];
	onSelectAll?: (checked: boolean) => void;
	onItemSelect?: (itemId: string, selected: boolean) => void;
	getItemId: (item: T) => string;
	renderItem: (
		item: T,
		isSelected: boolean,
		onSelect: (selected: boolean) => void
	) => React.ReactNode;
	emptyState?: {
		icon?: React.ReactNode;
		title: string;
		description: string;
		action?: {
			label: string;
			onClick: () => void;
		};
	};
	className?: string;
	loadingRowCount?: number;
	showCheckboxes?: boolean;
}

export function DataTable<T>({
	columns,
	data,
	isLoading = false,
	error = null,
	selectedItems = [],
	onSelectAll,
	onItemSelect,
	getItemId,
	renderItem,
	emptyState,
	className = "",
	loadingRowCount = 5,
	showCheckboxes = false,
}: DataTableProps<T>) {
	const handleSelectAll = () => {
		if (onSelectAll) {
			const isAllSelected =
				data.length > 0 && selectedItems.length === data.length;
			onSelectAll(!isAllSelected);
		}
	};

	const handleItemSelect = (item: T) => {
		if (onItemSelect) {
			const itemId = getItemId(item);
			const isSelected = selectedItems.includes(itemId);
			onItemSelect(itemId, !isSelected);
		}
	};

	return (
		<div className={`rounded-md border ${className}`}>
			<Table>
				<TableHeader>
					<TableRow>
						{showCheckboxes && onSelectAll && (
							<TableHead className="w-12">
								<Checkbox
									label=""
									checked={
										data.length > 0 &&
										selectedItems.length === data.length
									}
									className="cursor-pointer"
									onCheckedChange={handleSelectAll}
									disabled={isLoading}
								/>
							</TableHead>
						)}
						{columns.map((column) => (
							<TableHead
								key={column.key}
								className={column.width}
							>
								{column.label}
							</TableHead>
						))}
					</TableRow>
				</TableHeader>
				<TableBody>
					{isLoading ? (
						Array.from({ length: loadingRowCount }).map(
							(_, index) => (
								<TableRow key={index}>
									{showCheckboxes && onSelectAll && (
										<TableCell>
											<Skeleton className="h-4 w-4" />
										</TableCell>
									)}
									{columns.map((column) => (
										<TableCell key={column.key}>
											<Skeleton className="h-4 w-20" />
										</TableCell>
									))}
								</TableRow>
							)
						)
					) : error ? (
						<TableRow>
							<TableCell
								colSpan={
									columns.length + (showCheckboxes ? 1 : 0)
								}
								className="h-24 text-center"
							>
								<div className="text-red-500">
									Error loading data: {error.message}
								</div>
							</TableCell>
						</TableRow>
					) : data.length === 0 ? (
						<TableRow>
							<TableCell
								colSpan={
									columns.length + (showCheckboxes ? 1 : 0)
								}
								className="h-24 text-center"
							>
								<div className="flex flex-col items-center justify-center py-8">
									{emptyState?.icon && (
										<div className="mx-auto mb-4 h-12 w-12 text-gray-400">
											{emptyState.icon}
										</div>
									)}
									<h3 className="text-sm font-medium text-gray-900">
										{emptyState?.title || "No data found"}
									</h3>
									<p className="mt-1 text-sm text-gray-500">
										{emptyState?.description ||
											"No items to display."}
									</p>
									{emptyState?.action && (
										<Button
											className="mt-4"
											onClick={emptyState.action.onClick}
										>
											<Plus className="mr-2 h-4 w-4" />
											{emptyState.action.label}
										</Button>
									)}
								</div>
							</TableCell>
						</TableRow>
					) : (
						data.map((item) => {
							const itemId = getItemId(item);
							const isSelected = selectedItems.includes(itemId);
							return (
								<TableRow key={itemId}>
									{showCheckboxes && (
										<TableCell>
											<Checkbox
												label=""
												checked={isSelected}
												className="cursor-pointer"
												onCheckedChange={(checked) => {
													if (onItemSelect) {
														onItemSelect(
															itemId,
															checked
														);
													}
												}}
												disabled={isLoading}
											/>
										</TableCell>
									)}
									{renderItem(item, isSelected, (selected) =>
										handleItemSelect(item)
									)}
								</TableRow>
							);
						})
					)}
				</TableBody>
			</Table>
		</div>
	);
}
