// Modal and Drawer Types
export interface BaseModalProps {
	data: Record<string, any>;
	onClose: () => void;
	[key: string]: any;
}

export interface BaseDrawerProps {
	data: Record<string, any>;
	onClose: () => void;
	[key: string]: any;
}

export interface BaseSheetProps {
	data: Record<string, any>;
	onClose: () => void;
	[key: string]: any;
}

export type ModalSize = "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "full";
export type DrawerSize = "sm" | "md" | "lg" | "xl" | "full";
export type SheetSize = "sm" | "md" | "lg" | "xl" | "full";
export type DrawerDirection = "top" | "bottom" | "left" | "right";
export type SheetSide = "top" | "bottom" | "left" | "right";

export interface ModalConfig {
	id: string;
	size?: ModalSize;
	dismissible?: boolean;
	data: Record<string, any>;
}

export interface DrawerConfig {
	id: string;
	direction?: DrawerDirection;
	size?: DrawerSize;
	dismissible?: boolean;
	data: Record<string, any>;
}

export interface SheetConfig {
	id: string;
	direction?: SheetSide; // Maps to drawer direction internally
	size?: SheetSize;
	dismissible?: boolean;
	data: Record<string, any>;
}

// Predefined Modal and Drawer IDs for type safety
export type ModalId =
	| "confirmation"
	| "customer-create"
	| "customer-edit"
	| "customer-delete"
	| "settings-profile"
	| "settings-preferences";

export type DrawerId =
	| "user-profile"
	| "notifications"
	| "settings"
	| "customer-details";

export type SheetId =
	| "user-profile"
	| "notifications"
	| "settings"
	| "customer-details";

// Hook return types
export interface UseModalReturn {
	openModal: (modalId: string, options?: Partial<ModalConfig>) => void;
	closeModal: (modalId?: string) => void;
	closeAllModals: () => void;
	activeModal: string | null;
	modalData: Record<string, any>;
	isOpen: (modalId: string) => boolean;
	updateModalData: (data: Partial<Record<string, any>>) => void;
}

export interface UseDrawerReturn {
	openDrawer: (drawerId: string, options?: Partial<DrawerConfig>) => void;
	closeDrawer: (drawerId?: string) => void;
	closeAllDrawers: () => void;
	activeDrawer: string | null;
	drawerData: Record<string, any>;
	isOpen: (drawerId: string) => boolean;
}

export interface UseSheetReturn {
	openSheet: (sheetId: string, options?: Partial<SheetConfig>) => void;
	closeSheet: (sheetId?: string) => void;
	closeAllSheets: () => void;
	activeSheet: string | null;
	sheetData: Record<string, any>;
	isOpen: (sheetId: string) => boolean;
}
