import { useState } from "react";
import { cn } from "@/lib/utils";

type RatingOption = {
	value: number;
	label: string;
	emoji: string;
};

export default function SatisfactionRating({
	onChange,
}: {
	onChange?: (value: number) => void;
}) {
	const [selectedValue, setSelectedValue] = useState<number>(3);

	const options: RatingOption[] = [
		{ value: 1, label: "Very Dissatisfied", emoji: "😡" },
		{ value: 2, label: "Dissatisfied", emoji: "🙁" },
		{ value: 3, label: "Neutral", emoji: "🙂" },
		{ value: 4, label: "Satisfied", emoji: "😊" },
		{ value: 5, label: "Very Satisfied", emoji: "🤩" },
	];

	const handleSelect = (value: number) => {
		setSelectedValue(value);
		onChange?.(value);
	};

	return (
		<div className="mx-auto w-full px-4">
			<div className="flex w-full items-center justify-center">
				<div className="relative z-20 mb-2 flex w-full items-center justify-center">
					<div className="relative flex w-full items-center justify-between">
						{options.map((option) => (
							<div
								key={option.value}
								className="flex flex-col items-center justify-center space-y-2"
							>
								<div className="flex items-center">
									<span className="mb-2 text-2xl">
										{option.emoji}
									</span>
								</div>
								<button
									type="button"
									onClick={() => handleSelect(option.value)}
									className={cn(
										"relative z-20 h-4 w-4 rounded-full transition-all duration-200",
										selectedValue === option.value
											? "bg-primary border-2 border-gray-100 shadow-md"
											: "bg-gray-300"
									)}
									aria-label={option.label}
								/>

								<div
									className={cn(
										"flex w-auto flex-col items-center text-center text-xs transition-colors",
										selectedValue === option.value
											? "font-medium text-gray-900"
											: "text-gray-500"
									)}
								>
									{option.label}
								</div>
							</div>
						))}
					</div>

					{/* Line segments - we'll create segments between nodes */}
					<div className="absolute top-[54px] right-10 left-10 z-10 h-1 rounded-full bg-gray-300"></div>
				</div>
			</div>
		</div>
	);
}
