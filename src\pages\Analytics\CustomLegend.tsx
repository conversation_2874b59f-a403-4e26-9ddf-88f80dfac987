import React, { useEffect, useRef, useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface CustomLegendProps {
	items: Array<{
		color: string;
		label: string;
	}>;
	isFullScreen?: boolean;
	containerRef?: any;
}

const CustomLegend: React.FC<CustomLegendProps> = ({
	items,
	isFullScreen,
	containerRef,
}) => {
	console.log();

	const scrollContainerRef = useRef<HTMLDivElement>(null);
	const [hasOverflow, setHasOverflow] = useState(false);
	const [containerWidth, setContainerWidth] = useState<any>("280px");

	useEffect(() => {
		const updateWidth = () => {
			if (isFullScreen) {
				setContainerWidth("860px");
			} else {
				setContainerWidth(
					containerRef?.current?.getBoundingClientRect()?.width - 40
				);
			}
		};

		updateWidth();
		window.addEventListener("resize", updateWidth);
		return () => window.removeEventListener("resize", updateWidth);
	}, []);

	const checkForOverflow = () => {
		if (scrollContainerRef.current) {
			const { scrollWidth, clientWidth } = scrollContainerRef.current;
			setHasOverflow(scrollWidth > clientWidth);
		}
	};

	useEffect(() => {
		checkForOverflow();
		const resizeObserver = new ResizeObserver(checkForOverflow);
		if (scrollContainerRef.current) {
			resizeObserver.observe(scrollContainerRef.current);
		}
		return () => resizeObserver.disconnect();
	}, [items]);

	const scroll = (direction: "left" | "right") => {
		if (scrollContainerRef.current) {
			const scrollAmount = window.innerWidth >= 768 ? 100 : 60;
			const newScrollPosition =
				scrollContainerRef.current.scrollLeft +
				(direction === "left" ? -scrollAmount : scrollAmount);

			scrollContainerRef.current.scrollTo({
				left: newScrollPosition,
				behavior: "smooth",
			});
		}
	};

	return (
		<div className="flex w-full justify-end">
			<div className="relative mt-1 flex items-center">
				<div
					className={"absolute  flex h-full items-center ".concat(
						isFullScreen ? "left-[-3px]" : "left-[3px]"
					)}
				>
					{hasOverflow && (
						<button
							onClick={() => scroll("left")}
							className="z-10 flex items-center justify-start  bg-white hover:bg-gray-100"
							aria-label="Scroll left"
						>
							<ChevronLeft className="h-4 w-4 text-gray-600" />
						</button>
					)}
				</div>

				<div
					ref={scrollContainerRef}
					className={"!mx-0 flex overflow-x-auto ".concat(
						hasOverflow ? "px-8" : ""
					)}
					style={{
						maxWidth: containerWidth,
						scrollbarWidth: "none",
						msOverflowStyle: "none",
						WebkitOverflowScrolling: "touch",
					}}
					onScroll={checkForOverflow}
				>
					<div className="flex gap-2 pl-1 sm:gap-3 md:gap-5">
						{items.map((item, index) => (
							<div
								key={index}
								className="flex flex-shrink-0 items-center gap-1 sm:gap-2"
							>
								<div
									className="h-1.5 w-1.5 rounded-[2px] sm:h-2 sm:w-2"
									style={{ backgroundColor: item.color }}
								/>
								<p className="whitespace-nowrap text-[10px] text-[#09090B] sm:text-xs md:text-sm">
									{item.label}
								</p>
							</div>
						))}
					</div>
				</div>

				<div className="absolute right-0 flex h-full items-center">
					{hasOverflow && (
						<ChevronRight
							onClick={() => scroll("right")}
							className="h-4 w-4 cursor-pointer bg-white text-gray-600 hover:bg-gray-100"
						/>
					)}
				</div>
			</div>
		</div>
	);
};

export default CustomLegend;
