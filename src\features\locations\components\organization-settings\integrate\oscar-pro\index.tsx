import { useState } from "react";
import oscarProIcon from "../../images/oscar.png";
import { Button } from "@/components/ui/Button/Button";
import { cn } from "@/lib/utils";
import IntegrateOscarProStep1 from "./step-1";
import IntegrateOscarProStep2 from "./step-2";
import IntegrateOscarProStep3 from "./step-3";
import IntegrateOscarProStep4 from "./step-4";
import IntegrateOscarProConnected from "./connected";
import IntegrateOscarProDisconnected from "./disconnected";

export default function IntegrateOscarPro() {
    const [step, setStep] = useState(1);
    const [connected, setConnected] = useState(false);
    const [disconnected, setDisconnected] = useState(false);

    return (
        <div>
            <div className="flex items-center gap-2 border-b pb-3">
                <img src={oscarProIcon} alt="OSCAR Pro" className="size-12" />
                <h2 className="text-base font-medium">OSCAR Pro</h2>
            </div>
            {!connected ? (
                <>
                    <div className="mt-6 w-full grid grid-cols-[repeat(3,1fr)_auto] gap-3">
                        {Array.from({ length: 4 }).map((_, index) => (
                            <div key={index} className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className={cn("border-current rounded-full size-7 text-sm transition-all duration-300 cursor-pointer", step === index + 1 ? "text-[#005893] hover:border-[#005893] hover:text-[#005893]" : "text-[#000] border-[#E4E4E7] hover:border-[#E4E4E7] hover:text-[#000]")}
                                    onClick={() => setStep(index + 1)}
                                >
                                    {index + 1}
                                </Button>
                                <div className="bg-[#E4E4E7] w-full h-[1px]"></div>
                            </div>
                        ))}
                    </div>
                    <div className="mt-6">
                        {step === 1 && <IntegrateOscarProStep1 />}
                        {step === 2 && <IntegrateOscarProStep2 />}
                        {step === 3 && <IntegrateOscarProStep3 />}
                        {step === 4 && <IntegrateOscarProStep4
                            setConnected={() => {
                                setConnected(true)
                            }}
                        />}
                    </div></>
            ) : (
                disconnected ? (
                    <IntegrateOscarProDisconnected
                        setConnected={() => {
                            setConnected(true)
                            setDisconnected(false)
                        }}
                    />
                ) : (
                    <IntegrateOscarProConnected
                        setDisconnected={() => {
                            setDisconnected(true)
                        }}
                    />
                )
            )}
        </div>
    )
}