import type { FormField } from "../../types/Form";
import { DatePicker } from "@/components/common/Datepicker/DatePicker";

type DateRangeFieldRendererProps = {
	field: FormField;
	value: any;
	mode: "preview" | "view" | "edit";
	onChange?: (value: any) => void;
};

export const DateRangeFieldRenderer: React.FC<DateRangeFieldRendererProps> = ({
	field,
	value,
	mode,
	onChange,
}) => {
	if (mode === "view") {
		return (
			<div>
				{value ?? <span className="text-slate-400">No response</span>}
			</div>
		);
	}
	return (
		<div className="flex w-full items-center gap-2">
			<DatePicker
				variant="default"
				value={mode === "edit" ? value : undefined}
				onChange={mode === "edit" && onChange ? onChange : undefined}
				disabled={mode !== "edit"}
				className="h-9 w-auto flex-1"
			/>
			<span className="text-xs">To</span>
			<DatePicker
				variant="default"
				value={mode === "edit" ? value : undefined}
				onChange={mode === "edit" && onChange ? onChange : undefined}
				disabled={mode !== "edit"}
				className="h-9 w-auto flex-1"
			/>
		</div>
	);
};
