import { useState } from 'react';

interface AnalyticsQuery {
	start_date: string | null;
	end_date: string | null;
	service_ids: number[];
	statuses: string[];
	locations: any[];
	categories: string[];
}

export const useAnalyticsStore = () => {
	const [analyticsQuery, setAnalyticsQuery] = useState<AnalyticsQuery>({
		start_date: null,
		end_date: null,
		service_ids: [],
		statuses: [],
		locations: [],
		categories: []
	});

	return {
		analyticsQuery,
		setAnalyticsQuery: (query: Partial<AnalyticsQuery>) => 
			setAnalyticsQuery(prev => ({ ...prev, ...query }))
	};
}; 