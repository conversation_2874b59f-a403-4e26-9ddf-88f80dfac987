import { type FC } from "react";
import { Trash2, <PERSON><PERSON><PERSON>, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { TableCell } from "@/components/ui/table";
import type { Location } from "@/features/locations/types";
import type { ServiceData } from "../../api/servicesApi";
import { Badge } from "@/components/ui/badge";

export interface ServiceCardProps {
	location?: Location;
	service?: ServiceData;
	onEdit?: (data: Location | ServiceData) => void;
	onView?: (data: Location | ServiceData) => void;
	onDelete?: (data: Location | ServiceData) => void;
	isSelected?: boolean;
	onSelectionChange?: (selected: boolean) => void;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
	location,
	service,
	onEdit,
	onView,
	onDelete,
	isSelected = false,
	onSelectionChange,
}) => {
	const data = service || location;
	const name = service?.name || location?.name;

	return (
		<>
			{/* Service Name Section */}
			<TableCell className="min-w-[200px]">
				<h2 className="text-base leading-5 font-medium">{name}</h2>
			</TableCell>

			{/* Status Section */}
			<TableCell className="min-w-[120px]">
				<Badge
					variant="outline"
					className={`border-transparent ${
						service?.is_available
							? "bg-[#c3efce] text-[#0a2914]"
							: "bg-gray-200 text-gray-600"
					}`}
				>
					{service?.is_available ? "Active" : "Inactive"}
				</Badge>
			</TableCell>

			{/* Form Section */}
			<TableCell className="min-w-[100px]">
				<Badge
					variant="outline"
					className="bg-foreground-muted border-transparent text-[#0a2914]"
				>
					{service?.forms?.length || 0}
				</Badge>
			</TableCell>

			{/* Auto Approve Section */}
			<TableCell className="min-w-[120px]">
				<div className="flex items-center gap-1.5">
					<Badge
						variant="outline"
						className={`h-[7px] w-[7px] rounded-full border-transparent p-0 ${
							service?.auto_approve
								? "bg-[#c3efce]"
								: "bg-gray-400"
						}`}
					></Badge>
					<h3 className="text-base leading-5 font-normal">
						{service?.auto_approve ? "On" : "Off"}
					</h3>
				</div>
			</TableCell>

			{/* Time Section */}
			<TableCell className="min-w-[100px]">
				<h3 className="text-base leading-5 font-normal">
					{service?.time_in_minute
						? service.time_in_minute >= 60
							? `${Math.floor(service.time_in_minute / 60)}h ${service.time_in_minute % 60}m`.replace(
									" 0m",
									""
								)
							: `${service.time_in_minute} min`
						: "N/A"}
				</h3>
			</TableCell>

			{/* Actions Section */}
			<TableCell className="w-[140px]">
				<div className="flex items-center justify-end gap-2.5">
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							data && onEdit?.(data);
						}}
					>
						<SquarePen className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							data && onDelete?.(data);
						}}
					>
						<Trash2 className="h-4 w-4" />
					</Button>
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 cursor-pointer rounded-md border-zinc-200"
						onClick={(e) => {
							e.stopPropagation();
							data && onView?.(data);
						}}
					>
						<ChevronRight className="h-4 w-4" />
					</Button>
				</div>
			</TableCell>
		</>
	);
};
