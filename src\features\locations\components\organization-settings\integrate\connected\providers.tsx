import { Button } from "@/components/ui/Button/Button";
import { Input } from "@/components/ui/input";
import ReusableSelect from "../component/reusable-select";

const migraniumTitles = ["Name", "Phone number", "Email", "Address", "City", "State", "Zip", "Country", "Other"]

export default function Providers() {
    return (
        <div>
            <h1 className="text-sm mb-4">Providers</h1>
            <div className="grid grid-cols-2 gap-4">
                <div>
                    <label htmlFor="emr-providers" className="text-[#27272A] font-medium text-sm">EMR Providers</label>
                    <Input
                        type="text"
                        placeholder="Full Name"
                        className="bg-white py-5 mt-4"
                    />
                    <Input
                        type="text"
                        placeholder="Phone number"
                        className="bg-white py-5 mt-4"
                    />
                    <Button variant="secondary" className="mt-5 font-normal w-[7rem] cursor-pointer">
                        Add
                    </Button>
                </div>
                <div>
                    <label htmlFor="migranium-providers" className="text-[#27272A] font-medium text-sm">
                        Migranium Providers
                    </label>
                    <ReusableSelect
                        placeholder="Select a title"
                        options={migraniumTitles}
                    />
                    <ReusableSelect
                        placeholder="Select a title"
                        options={migraniumTitles}
                    />
                </div>
            </div>
            <div className="flex items-center justify-end mt-5">
                <Button>
                    Save & Next
                </Button>
            </div>
        </div>
    )
}