import { Button } from "@/components/ui/Button/Button";
import { Input } from "@/components/ui/input";
import ReusableSelect from "../component/reusable-select";

const migraniumStatus = ["Name", "Phone number", "Email", "Address", "City", "State", "Zip", "Country", "Other"]

export default function AppointmentStatus() {
    return (
        <div>
            <h1 className="text-sm mb-4">Appointment Status</h1>
            <div className="grid grid-cols-3 gap-4">
                <div>
                    <label htmlFor="name" className="text-[#27272A] font-medium text-sm">EMR Titles</label>
                    <Input
                        type="text"
                        placeholder="Full Name"
                        className="bg-white py-5 mt-4"
                    />
                    <Input
                        type="text"
                        placeholder="Phone number"
                        className="bg-white py-5 mt-4"
                    />
                    <Button variant="secondary" className="mt-5 font-normal w-[7rem] cursor-pointer">
                        Add
                    </Button>
                </div>
                <div>
                    <label htmlFor="type" className="text-[#27272A] font-medium text-sm">Type</label>
                    <Input
                        type="text"
                        placeholder="Full Name"
                        className="bg-white py-5 mt-4"
                    />
                    <Input
                        type="text"
                        placeholder="Phone number"
                        className="bg-white py-5 mt-4"
                    />
                </div>
                <div>
                    <label htmlFor="status" className="text-[#27272A] font-medium text-sm">
                        Status
                    </label>
                    <ReusableSelect
                        placeholder="Select a title"
                        options={migraniumStatus}
                    />
                    <ReusableSelect
                        placeholder="Select a title"
                        options={migraniumStatus}
                    />
                </div>
            </div>
            <div className="flex items-center justify-end mt-5">
                <Button>
                    Save & Next
                </Button>
            </div>
        </div>
    )
}