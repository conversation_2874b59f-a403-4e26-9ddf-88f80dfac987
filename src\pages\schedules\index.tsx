import { Button } from "@/components/ui/button";
import { LuCalendarPlus } from "react-icons/lu";
import { IoSettingsOutline } from "react-icons/io5";
// import CalendarView from "./calendar-view";
import Filter from "./filter";
import Calendar from "./calendar";
import FilterAppointment from "./filter-appointment";
import BookAppointment from "./book-appointment";
import { useEffect, useState } from "react";
import { useUIStore } from "@/stores/uiStore";
import { usePermissionStore } from "@/stores/permissionStore";
import { useLocations } from "@/features/locations/hooks/useLocations";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";

export default function SchedulePage() {
    const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);
    const setPhContent = useUIStore(
        (state) => state.setPageHeaderContent
    );
    const { organizationId } = useOrganizationContext();
    const currentLocation = usePermissionStore(state => state.currentLocation);

    const [appointmentOpen, setAppointmentOpen] = useState<boolean>(false);
    const [selectedLocationId, setSelectedLocationId] = useState<string | undefined>(undefined);
    const [activeFilterCount, setActiveFilterCount] = useState<number>(0);

    // Fetch all locations to get the current location's name
    const { data: locationsData, isLoading: isLoadingLocations } = useLocations(
        {},
        organizationId || undefined
    );

    // Find the selected location data
    const selectedLocationData = locationsData?.find(
        location => location.id.toString() === selectedLocationId
    );

    const locationName = isLoadingLocations
        ? "Loading..."
        : selectedLocationData?.name || "Location Name";

    // Handle location change from filter
    const handleLocationChange = (locationId: string | undefined) => {
        setSelectedLocationId(locationId);
    };

    // Handle active filter count change
    const handleActiveFiltersChange = (count: number) => {
        setActiveFilterCount(count);
    };

    useEffect(() => {
        setBreadcrumbs([
            {
                label: "Schedule",
                href: "/schedule",
            },
            {
                label: "Manage Schedule",
                isCurrentPage: true,
            },
            {
                label: locationName,
                isCurrentPage: true,
            },
        ]);
        // setPhContent("Manage Schedule");

        // Cleanup breadcrumbs when component unmounts
        return () => {
            setBreadcrumbs([]);
        };
    }, [setBreadcrumbs, locationName]);

    useEffect(() => {
        const headerContent = (
            <div className="flex flex-1 items-center justify-between">
                <div>
                    <h1 className="text-foreground text-xl font-bold">
                        Manage Schedule
                    </h1>
                    <p className="text-[#71717A] font-regular text-sm">{locationName}</p>
                </div>
                <div className="flex items-center gap-x-2">
                    <div className="relative">
                        <FilterAppointment
                            onLocationChange={handleLocationChange}
                            onActiveFiltersChange={handleActiveFiltersChange}
                        />
                        {activeFilterCount > 0 && (
                            <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                                {activeFilterCount}
                            </span>
                        )}
                    </div>
                    <Button variant="outline" className="py-2 px-2">
                        <IoSettingsOutline size={16} />
                    </Button>
                    <Button
                        className="py-2 px-3 cursor-pointer text-sm"
                        type="button"
                        onClick={() => setAppointmentOpen(true)}
                    >
                        <LuCalendarPlus size={16} />
                        Add Appointment
                    </Button>
                </div>
            </div>
        );

        setPhContent(headerContent);

        return () => {
            setPhContent(null);
        };
    }, [setPhContent, locationName]);

    return (
        <section>

            <BookAppointment
                open={appointmentOpen}
                onOpenChange={setAppointmentOpen}
                locationId={selectedLocationId}
            />


            <div className="grid w-full max-w-screen grid-cols-[300px_1fr] gap-x-4 mt-2 h-[calc(100vh-120px)] scrollbar-hide">

                <Filter />

                <div className="overflow-x-auto w-full scrollbar-hide">
                    <Calendar />
                </div>

            </div>

        </section>
    );
}