import React from 'react';
import { Plus, Trash2 } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { TimePicker } from './TimePicker';

interface TimeValue {
  hour: string;
  minute: string;
  period: 'AM' | 'PM';
}

interface TimeSlot {
  id: string;
  startTime: TimeValue;
  endTime: TimeValue;
}

interface DaySchedule {
  day: string;
  enabled: boolean;
  timeSlots: TimeSlot[];
  maxFrequency: string;
}

interface DayScheduleTableProps {
  daysSchedule: DaySchedule[];
  onDayToggle: (index: number) => void;
  onTimeSlotChange: (dayIndex: number, slotIndex: number, field: 'startTime' | 'endTime', time: TimeValue) => void;
  onAddTimeSlot: (dayIndex: number) => void;
  onRemoveTimeSlot: (dayIndex: number, slotIndex: number) => void;
  onFrequencyChange: (index: number, frequency: string) => void;
  showTimes: boolean;
  showFrequency: boolean;
}

export const DayScheduleTable: React.FC<DayScheduleTableProps> = ({
  daysSchedule,
  onDayToggle,
  onTimeSlotChange,
  onAddTimeSlot,
  onRemoveTimeSlot,
  onFrequencyChange,
  showTimes,
  showFrequency
}) => {
  const getGridCols = () => {
    if (!showTimes && !showFrequency) return 'grid-cols-1';
    if (!showTimes || !showFrequency) return 'grid-cols-2';
    return 'grid-cols-[minmax(100px,120px)_auto_minmax(200px,250px)]'; // Fixed day width, auto time slots, fixed frequency width
  };

  return (
    <div className='w-full'>
      <h3 className="font-medium text-sm mb-4">Select Days, Time and Frequency of a Service</h3>

      <div className="bg-white rounded-lg border">
        {/* Header */}
        <div className={`grid gap-4 p-4 border-b bg-gray-50 text-sm font-medium text-gray-700 ${getGridCols()}`}>
          <div className="min-w-[100px]">Day</div>
          {showTimes && <div>Time Slots</div>}
          {showFrequency && <div className="min-w-[200px]">Maximum Limit</div>}
        </div>

        {/* Days */}
        {daysSchedule.map((daySchedule, dayIndex) => (
          <div
            key={daySchedule.day}
            className={`grid gap-4 p-4 border-b last:border-b-0 items-start ${getGridCols()} ${!daySchedule.enabled ? 'opacity-50' : ''
              }`}
          >
            {/* Day Column */}
            <div className="flex items-center space-x-3 min-w-[100px]">
              <Switch
                checked={daySchedule.enabled}
                onCheckedChange={() => onDayToggle(dayIndex)}
              />
              <span className="text-sm font-medium">{daySchedule.day}</span>
            </div>

            {/* Time Slots Column */}
            {showTimes && (
              <div className="space-y-3">
                {/* Horizontal container for all time slots */}
                <div className="flex flex-wrap items-center gap-3">
                  {daySchedule.timeSlots.map((timeSlot, slotIndex) => (
                    <div key={timeSlot.id} className="flex items-center space-x-2">
                      {daySchedule.timeSlots.length > 1 && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                          disabled={!daySchedule.enabled}
                          onClick={() => onRemoveTimeSlot(dayIndex, slotIndex)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                      <TimePicker
                        value={timeSlot.startTime}
                        onChange={(time) => onTimeSlotChange(dayIndex, slotIndex, 'startTime', time)}
                        disabled={!daySchedule.enabled}
                        width="w-32"
                      />
                      <span className="text-gray-500">-</span>
                      <TimePicker
                        value={timeSlot.endTime}
                        onChange={(time) => onTimeSlotChange(dayIndex, slotIndex, 'endTime', time)}
                        disabled={!daySchedule.enabled}
                        width="w-32"
                      />

                      {/* Add button only after the last time slot */}
                      {slotIndex === daySchedule.timeSlots.length - 1 && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-fit"
                          disabled={!daySchedule.enabled}
                          onClick={() => onAddTimeSlot(dayIndex)}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Max Frequency Column */}
            {showFrequency && (
              <div className="min-w-[200px]">
                <Input
                  placeholder="Enter max frequency"
                  value={daySchedule.maxFrequency}
                  onChange={(e) => onFrequencyChange(dayIndex, e.target.value)}
                  className="text-sm"
                  disabled={!daySchedule.enabled}
                />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};