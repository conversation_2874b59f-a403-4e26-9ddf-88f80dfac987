import React from "react";
import {
	<PERSON><PERSON><PERSON>,
	Too<PERSON><PERSON><PERSON>ontent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";

const InfoTooltip = ({ children, trigger } : {children: any, trigger: any}) => {
	return (
		<TooltipProvider delayDuration={0}>
			<Tooltip>
				<TooltipTrigger asChild>{trigger}</TooltipTrigger>
				<TooltipContent
					className="max-w-[90vw] border border-gray-200 bg-white text-left shadow-lg sm:max-w-[85vw] md:max-w-[400px]"
					side="right"
					align="center"
					sideOffset={5}
					alignOffset={0}
					avoidCollisions={true}
					collisionPadding={16}
				>
					<p className="text-sm text-[#596574]">{children}</p>
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
};

export default InfoTooltip;
