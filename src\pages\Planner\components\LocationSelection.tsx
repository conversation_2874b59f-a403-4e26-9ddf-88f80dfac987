import React, { useCallback, useState, useEffect } from 'react';
import { ChevronLeft, Search, SlidersHorizontal, ChevronRight, Users, MapPin, QrCode, Wand2, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import {
	Pagination,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@/components/ui/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useLocations } from '@/features/locations/hooks';
import { useOrganizationContext } from '@/features/organizations/context';
import type { Location, LocationsFilters } from '@/features/locations/types';
import { LocationFilterSheet } from './LocationFilterSheet';


interface LocationSelectionProps {
	onBack: () => void;
	onSelectLocation: (location: Location) => void;
}

export const LocationSelection: React.FC<LocationSelectionProps> = ({ onBack, onSelectLocation }) => {
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
	const [currentPage, setCurrentPage] = useState(1);
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const [appliedFilters, setAppliedFilters] = useState<Partial<LocationsFilters>>({});

	const { organizationId } = useOrganizationContext();

	const {
		data: locationsData,
		isLoading,
		error,
		refetch,
	} = useLocations(
		{
			page: currentPage,
			search: searchTerm || undefined,
			limit: 7,
			sortBy: "name",
			sortOrder: "asc",
			...appliedFilters,
		},
		organizationId!
	);

	// Debounced search
	useEffect(() => {
		const timer = setTimeout(() => {
			setCurrentPage(1); // Reset to first page when searching
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// Use the data directly from the API
	const locations = locationsData || [];

	const handleLocationToggle = (locationId: string) => {
		setSelectedLocations(prev =>
			prev.includes(locationId)
				? prev.filter(id => id !== locationId)
				: [...prev, locationId]
		);
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked && locations?.length) {
			setSelectedLocations(locations.map((location) => location.id));
		} else {
			setSelectedLocations([]);
		}
	};

	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
	}, []);

	const handlePreviousPage = useCallback(() => {
		if (currentPage > 1) {
			handlePageChange(currentPage - 1);
		}
	}, [currentPage, handlePageChange]);

	const handleNextPage = useCallback(() => {
		// We'll need to determine total pages from API response or use a reasonable limit
		handlePageChange(currentPage + 1);
	}, [currentPage, handlePageChange]);

	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchTerm(e.target.value);
	};

	const handleApplyFilters = (filters: Partial<LocationsFilters>) => {
		setAppliedFilters(filters);
		setCurrentPage(1); // Reset to first page when filters change
		setShowFilterSheet(false);
	};

	const handleClearFilters = () => {
		setAppliedFilters({});
		setSearchTerm('');
		setCurrentPage(1);
	};

	const getLocationInitials = (name: string) => {
		return name
			.split(' ')
			.map(word => word.charAt(0))
			.join('')
			.toUpperCase()
			.slice(0, 2);
	};

	const hasActiveFilters = Object.keys(appliedFilters).length > 0 || searchTerm;

	return (
		<div className="min-h-screen">
			{/* Header */}
			<div className="bg-white py-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center space-x-4">
						<Button variant="ghost" size="sm" onClick={onBack} className="p-2">
							<ChevronLeft className="h-5 w-5" />
						</Button>
						<div>
							<h1 className="text-2xl font-semibold text-main-1">Select a Location</h1>
							<p className="text-[#6D748D] mt-1">Select a Location that you would like to set preferences for below.</p>
						</div>
					</div>
					<div className="flex items-center space-x-3">
						<Button variant="outline" size="sm" onClick={() => refetch()}>
							{isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => setShowFilterSheet(true)}
							className={hasActiveFilters ? "bg-blue-50 border-blue-200" : ""}
						>
							<SlidersHorizontal className="h-4 w-4" />
						</Button>
						{hasActiveFilters && (
							<Button variant="outline" size="sm" onClick={handleClearFilters}>
								Clear Filters
							</Button>
						)}
					</div>
				</div>
			</div>

			{/* Content */}
			<div className="">
				{/* Search bar */}
				<div className="mb-6">
					<div className="relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<Input
							placeholder="Search locations..."
							value={searchTerm}
							onChange={handleSearchChange}
							className="pl-10"
						/>
					</div>
				</div>

				{/* Locations List */}
				<div className='rounded-lg border border-[#E4E4E7] p-0.5'>
					<Table>
						<TableHeader className='hover:bg-transparent'>
							<TableRow className="bg-gray-50 hover:bg-transparent h-14 text-[#71717A]">
								<TableHead className="text-[#71717A]">
									<Checkbox
										checked={locations.length > 0 && selectedLocations.length === locations.length}
										onCheckedChange={handleSelectAll}
									/>
								</TableHead>
								<TableHead className="text-[#71717A]">Location</TableHead>
								<TableHead className="text-[#71717A]">Providers</TableHead>
								<TableHead className="text-[#71717A]">Services</TableHead>
								<TableHead className="text-[#71717A] text-center">Actions</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{isLoading ? (
								<TableRow>
									<TableCell colSpan={5} className="text-center py-8">
										<Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
										<p className="mt-2 text-gray-500">Loading locations...</p>
									</TableCell>
								</TableRow>
							) : error ? (
								<TableRow>
									<TableCell colSpan={5} className="text-center py-8 text-red-500">
										Error loading locations. Please try again.
									</TableCell>
								</TableRow>
							) : locations.length === 0 ? (
								<TableRow>
									<TableCell colSpan={5} className="text-center py-8 text-gray-500">
										No locations found.
									</TableCell>
								</TableRow>
							) : (
								locations.map((location) => (
									<TableRow
										key={location.id}
										className={`hover:bg-gray-50 h-16 cursor-pointer ${selectedLocations.includes(location.id) ? 'bg-blue-50' : ''
											}`}
										onClick={() => onSelectLocation(location)}
									>
										<TableCell>
											<Checkbox
												checked={selectedLocations.includes(location.id)}
												onCheckedChange={() => handleLocationToggle(location.id)}
												onClick={(e) => e.stopPropagation()}
											/>
										</TableCell>
										<TableCell className="font-medium">
											<div className="flex items-center space-x-3">
												<div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-sm font-medium text-gray-600 overflow-hidden">
													{location.image ? (
														<img
															src={location.image}
															alt={location.name}
															className="w-full h-full object-cover rounded-full"
															onError={(e) => {
																// Fallback to initials if image fails to load
																const target = e.target as HTMLImageElement;
																target.style.display = 'none';
																const sibling = target.nextElementSibling as HTMLElement | null;
																if (sibling) {
																	sibling.style.display = 'flex';
																}
															}}
														/>
													) : null}
													<div
														className={`w-full h-full flex items-center justify-center text-sm font-medium text-gray-600 ${location.image ? 'hidden' : 'flex'}`}
													>
														{getLocationInitials(location.name)}
													</div>
												</div>
												<div>
													<h3 className="font-medium text-gray-900">{location.name}</h3>
													<p className="text-sm text-gray-500 flex items-center">
														<MapPin className="h-3 w-3 mr-1" />
														{location.address
															? [
																location.address,
																location.city,
																location.state,
																location.country,
															]
																.filter(Boolean)
																.join(", ")
															: ""}
													</p>
												</div>
											</div>
										</TableCell>
										<TableCell>
											<div className="flex items-center space-x-2 text-sm text-gray-600">
												<Users className="h-4 w-4" />
												<span>Providers</span>
												<span className="font-medium">{location.stations?.length || 0}</span>
											</div>
										</TableCell>
										<TableCell>
											<div className="flex items-center space-x-2 text-sm text-gray-600">
												<Wand2 className="h-4 w-4" />
												<span>Services</span>
												<span className="font-medium">{location.service_count || 0}</span>
											</div>
										</TableCell>
										<TableCell className="text-right">
											<div className="flex items-center justify-end space-x-2">
												<Button variant="outline" className="px-2 py-1 text-xs font-medium">
													<QrCode className="h-4 w-4 text-gray-400" />
												</Button>
												<Button variant="outline" className="px-2 py-1 text-xs font-medium">
													<ChevronRight className="h-4 w-4 text-gray-400" />
												</Button>
											</div>
										</TableCell>
									</TableRow>
								))
							)}
						</TableBody>
					</Table>
				</div>

				{/* Pagination */}
				{locations.length > 0 && (
					<div className="flex justify-end pt-4">
						<div>
							<Pagination>
								<PaginationContent>
									<PaginationItem>
										<PaginationPrevious
											onClick={handlePreviousPage}
											className={
												currentPage === 1
													? "pointer-events-none opacity-50"
													: "cursor-pointer"
											}
										/>
									</PaginationItem>

									{/* Show current page and a few around it */}
									{[currentPage - 1, currentPage, currentPage + 1]
										.filter(page => page > 0)
										.map((page) => (
											<PaginationItem key={page}>
												<PaginationLink
													onClick={() => handlePageChange(page)}
													isActive={currentPage === page}
													className="cursor-pointer"
												>
													{page}
												</PaginationLink>
											</PaginationItem>
										))}

									<PaginationItem>
										<PaginationNext
											onClick={handleNextPage}
											className="cursor-pointer"
										/>
									</PaginationItem>
								</PaginationContent>
							</Pagination>
						</div>
					</div>
				)}
			</div>

			{/* Filter Sheet */}
			<LocationFilterSheet
				open={showFilterSheet}
				onOpenChange={setShowFilterSheet}
				onApplyFilters={handleApplyFilters}
				currentFilters={appliedFilters}
			/>
		</div>
	);
};