import { Badge } from "@/components/ui/badge";
import {
	Command,
	CommandGroup,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import { Command as CommandPrimitive } from "cmdk";
import { X, ChevronUp, ChevronDown } from "lucide-react";
import React, { type KeyboardEvent } from "react";
import type { FieldError, FieldErrorsImpl, Merge } from "react-hook-form";
// import { Option } from "../form/CustomSelect";
// import CustomCheckbox2 from "../form/CustomCheckbox2";
import { cn } from "@/lib/utils";
import { Checkbox } from "../common/Checkbox";

export type Option = {
	label: string;
	value: string;
	disabled?: boolean;
	metadata?: any;
};

interface MultiSelectProps {
	options: Option[];
	placeholder: string;
	selected: Option[];
	onSelect: (option: Option[]) => void;
	onUnselect: (option: Option) => void;
	maxSelections?: number;
	checkBoxed?: boolean;
	width?: string | number;
	error?:
		| FieldError
		| Merge<FieldError, FieldErrorsImpl<any>>
		| undefined
		| "";
	className?: string;
	textContainerClassName?: string;
}

const MultiSelect: React.FC<MultiSelectProps> = ({
	options,
	placeholder,
	selected,
	onSelect,
	onUnselect,
	maxSelections,
	error,
	width,
	checkBoxed = true,
	className,
	textContainerClassName,
}) => {
	const inputRef = React.useRef<HTMLInputElement>(null);
	const containerRef = React.useRef<HTMLDivElement>(null);
	const [open, setOpen] = React.useState(false);
	const [inputValue, setInputValue] = React.useState("");
	const [isOverflowing, setIsOverflowing] = React.useState(false);

	React.useEffect(() => {
		const checkOverflow = () => {
			if (containerRef.current) {
				const container = containerRef.current;
				setIsOverflowing(container.scrollWidth > container.clientWidth);
			}
		};

		checkOverflow();
		window.addEventListener("resize", checkOverflow);
		return () => window.removeEventListener("resize", checkOverflow);
	}, [selected]);

	const containerStyle = React.useMemo(() => {
		if (!width) return {};
		return {
			width: typeof width === "number" ? `${width}px` : width,
		};
	}, [width]);

	const handleUnselect = React.useCallback(
		(option: Option) => onUnselect(option),
		[onUnselect]
	);

	const handleKeyDown = React.useCallback(
		(e: KeyboardEvent<HTMLDivElement>) => {
			const input = inputRef.current;
			if (input) {
				if (e.key === "Delete" || e.key === "Backspace") {
					if (input.value === "") {
						const newSelected = [...selected];
						newSelected.pop();
						onSelect(newSelected);
					}
				}
				if (e.key === "Escape") {
					input.blur();
				}
			}
		},
		[selected, onSelect]
	);

	const selectables =
		[
			...options,
			{ label: "Disabled", value: "Disabled", disabled: true },
		].filter((option) => !selected.some((s) => s.value === option.value)) ??
		[];

	const optionsToMap = checkBoxed ? options : selectables;

	const handleUnselectAll = () => {
		onSelect([]);
	};

	const toggleOpen = () => {
		setOpen(!open);
		if (!open) {
			inputRef.current?.focus();
		}
	};

	return (
		<div
			style={containerStyle}
			className={cn("inline-block w-full", className)}
		>
			<Command
				onKeyDown={handleKeyDown}
				className="relative max-h-10 w-full overflow-visible text-ellipsis bg-transparent"
			>
				<div
					className={cn(
						"group flex h-10 items-center rounded-md border border-input px-3 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
						textContainerClassName
					)}
				>
					<div
						ref={containerRef}
						className="flex w-full items-center gap-2"
						onClick={() => inputRef.current?.focus()}
					>
						{!!selected.length && (
							<div className="flex flex-wrap items-center gap-1.5">
								{selected.length > 1 ? (
									<Badge
										variant="outline"
										className="flex cursor-pointer items-center space-x-1 rounded-sm border-primary bg-[#043B6D0A] px-2 py-0.5 text-[#323539]"
										onClick={(e) => {
											e.stopPropagation();
											handleUnselectAll();
										}}
									>
										<span>{selected.length}</span>
										<X className="h-3 w-3 duration-200 ease-in-out hover:text-muted-foreground" />
									</Badge>
								) : (
									selected.map((option) => (
										<Badge
											key={option.value}
											variant="outline"
											className="relative cursor-pointer items-center rounded-sm border-[#E5E5E7] bg-white px-2 py-0.5 text-[#323539]"
											onClick={(e) => {
												e.stopPropagation();
												handleUnselect(option);
											}}
										>
											<span className="max-w-[200px] truncate">
												{option.label}
											</span>
											<button
												className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
												type="button"
												onKeyDown={(e) => {
													if (e.key === "Enter") {
														handleUnselect(option);
													}
												}}
												onMouseDown={(e) => {
													e.preventDefault();
													e.stopPropagation();
												}}
												onClick={(e) => {
													e.preventDefault();
													e.stopPropagation();
													handleUnselect(option);
												}}
											>
												<X className="h-3 w-3 text-[#323539] duration-200 ease-in-out hover:text-muted-foreground" />
											</button>
										</Badge>
									))
								)}
							</div>
						)}
						<div className="flex min-w-[60px] flex-1 items-center">
							<CommandPrimitive.Input
								ref={inputRef}
								value={inputValue}
								onValueChange={setInputValue}
								onBlur={() => setOpen(false)}
								onFocus={() => setOpen(true)}
								placeholder={
									maxSelections === selected.length ||
									isOverflowing
										? ""
										: placeholder
								}
								className="w-full bg-transparent outline-none placeholder:text-muted-foreground"
							/>
							<button
								type="button"
								onClick={toggleOpen}
								className="ml-2 flex items-center text-muted-foreground hover:text-foreground"
							>
								{open ? (
									<ChevronUp className="h-4 w-4" />
								) : (
									<ChevronDown className="h-4 w-4" />
								)}
							</button>
						</div>
					</div>
				</div>
				{open && selectables.length > 0 ? (
					<div className="relative mt-0">
						{/* Add a max width to the div below to set the max width */}
						<div className="absolute top-2 z-10 w-full min-w-fit rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in">
							<CommandList>
								<CommandGroup className="h-full overflow-auto">
									{selectables.length > 1 ? (
										optionsToMap.map((option) => {
											const onCommandSelect = () => {
												if (
													selected.find(
														(item) =>
															item.value ===
															option.value
													)
												)
													return handleUnselect(
														option
													);
												const newSelected = [
													...selected,
													option,
												];
												onSelect(newSelected);
												setInputValue("");
											};
											return (
												<CommandItem
													key={option.value}
													onMouseDown={(e) => {
														e.preventDefault();
														e.stopPropagation();
													}}
													disabled={option.disabled}
													onSelect={onCommandSelect}
													className={cn(
														"cursor-pointer",
														{
															hidden: option.disabled,
														}
													)}
												>
													<span className="hidden">
														{option.value}
													</span>
													<div className="flex items-center gap-x-3">
														{checkBoxed && (
															<Checkbox
																onCheckedChange={
																	onCommandSelect
																}
																checked={
																	!!selected.find(
																		(
																			item
																		) =>
																			item.value ===
																			option.value
																	)
																}
																id={
																	option.label +
																	option.value
																}
																className="size-4 rounded-[2px] border-[2.5px]"
															/>
														)}
														<label
															htmlFor={
																option.label +
																option.value
															}
															// className="max-w-[calc(100%-2rem)] truncate"
														>
															{option.label}
														</label>
													</div>
												</CommandItem>
											);
										})
									) : (
										<CommandItem disabled>
											No options available
										</CommandItem>
									)}
								</CommandGroup>
							</CommandList>
						</div>
					</div>
				) : null}
			</Command>
			{error && (
				<small className="mt-1 text-xs text-red-500">
					{(error.message || error.type)?.toString()}
				</small>
			)}
		</div>
	);
};

export default MultiSelect;
