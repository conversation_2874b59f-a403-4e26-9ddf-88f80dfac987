import { apiClient } from "@/lib/api/clients";
import type { CreateProviderStationRequest } from "../types";

// Import operating hours, schedule settings, and schedule optimizer types from organizations API
import type {
	OrganizationOperatingHour,
	UpdateOperatingHoursRequest,
	LocationOperatingHoursResponse,
	ScheduleSettings,
	UpdateScheduleSettingsRequest,
	LocationScheduleSettingsResponse,
	ScheduleOptimizer,
	UpdateScheduleOptimizerRequest,
	LocationScheduleOptimizerResponse,
} from "@/features/organizations/api/organizationsApi";

// Define response types locally since they're not exported
export interface CreateStationResponse {
	success: boolean;
	message: string;
	data: {
		id: number;
		name: string;
		image?: string;
		description?: string;
		service_provider_first_name: string;
		service_provider_last_name: string;
		service_provider_email: string;
		service_provider_phone: string;
		location_id: number;
		is_active: boolean;
		created_at: string;
		updated_at: string;
	};
}

export interface StationData {
	id: number;
	name: string;
	image?: string;
	description?: string;
	locations: Array<{
		id: number;
		name: string;
	}>;
	service_providers: Array<{
		id: number;
		first_name: string;
		last_name?: string;
		email: string;
		role: string;
	}>;
}

export interface GetStationsResponse {
	success: boolean;
	message: string;
	data: StationData[];
}

// Upload helper function (if image upload is needed)
async function uploadImage(file: File): Promise<string> {
	const formData = new FormData();
	formData.append("image", file);
	formData.append("folder", "stations");

	try {
		const response = await apiClient.post("/api/v1/upload", formData, {
			headers: {
				"Content-Type": "multipart/form-data",
			},
		});
		return response.data.url;
	} catch (error) {
		console.error("Image upload failed:", error);
		throw new Error("Failed to upload image");
	}
}

const STATIONS_ENDPOINTS = {
	base: "/api/v1/stations",
	byId: (id: string) => `/api/v1/stations/${id}`,
	operatingHours: (locationId: string, stationId: string) =>
		`/schedule/api/v1/location/${locationId}/stations/${stationId}/operating-hours`,
	scheduleSettings: (locationId: string, stationId: string) =>
		`/schedule/api/v1/location/${locationId}/stations/${stationId}/schedule-settings`,
	scheduleOptimizer: (locationId: string, stationId: string) =>
		`/schedule/api/v1/location/${locationId}/stations/${stationId}/schedule-optimizer`,
} as const;

export const stationsApi = {
	// Get stations for an organization and location
	getStations: async (
		locationId: string,
		orgId: number
	): Promise<GetStationsResponse> => {
		const response = await apiClient.get(STATIONS_ENDPOINTS.base, {
			headers: {
				"X-organizationId": orgId,
				"X-locationId": locationId,
			},
		});
		return response.data;
	},

	// Get all stations for an organization
	getAllStations: async (orgId: number): Promise<GetStationsResponse> => {
		const response = await apiClient.get(STATIONS_ENDPOINTS.base, {
			headers: {
				"X-organizationId": orgId,
			},
		});
		return response.data;
	},

	// Get a specific station by ID
	getStationById: async (
		stationId: string,
		orgId: number
	): Promise<{ success: boolean; message: string; data: StationData }> => {
		const response = await apiClient.get(
			STATIONS_ENDPOINTS.byId(stationId),
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		return response.data;
	},

	// Create new station with image upload support
	createStation: async (
		data: CreateProviderStationRequest & { imageFile?: File },
		locationId: string,
		orgId: number
	): Promise<CreateStationResponse> => {
		let imageUrl = data.image;

		// Upload image if file is provided
		if (data.imageFile) {
			imageUrl = await uploadImage(data.imageFile);
		}

		const apiPayload = {
			name: data.name,
			image: imageUrl,
			description: data.description,
			service_provider_first_name: data.service_provider_first_name,
			service_provider_last_name: data.service_provider_last_name,
			service_provider_email: data.service_provider_email,
			service_provider_phone: data.service_provider_phone,
		};

		const response = await apiClient.post(
			STATIONS_ENDPOINTS.base,
			apiPayload,
			{
				headers: {
					"X-organizationId": orgId,
					"X-locationId": locationId,
				},
			}
		);
		return response.data;
	},

	// Delete a station
	deleteStation: async (stationId: string, orgId: number): Promise<void> => {
		await apiClient.delete(STATIONS_ENDPOINTS.byId(stationId), {
			headers: {
				"X-organizationId": orgId,
			},
		});
	},

	// Station Operating Hours API methods
	getStationOperatingHours: async (
		locationId: string,
		stationId: string,
		orgId: number
	): Promise<LocationOperatingHoursResponse["data"]> => {
		const response = await apiClient.get(
			STATIONS_ENDPOINTS.operatingHours(locationId, stationId),
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object including hours and inheritance_info
		return response.data.data;
	},

	updateStationOperatingHours: async (
		locationId: string,
		stationId: string,
		orgId: number,
		data: UpdateOperatingHoursRequest
	): Promise<LocationOperatingHoursResponse["data"]> => {
		const response = await apiClient.put(
			STATIONS_ENDPOINTS.operatingHours(locationId, stationId),
			data, // Send the array directly, not wrapped in an object
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object including hours and inheritance_info
		return response.data.data;
	},

	// Station Schedule Settings API methods
	getStationScheduleSettings: async (
		locationId: string,
		stationId: string,
		orgId: number
	): Promise<LocationScheduleSettingsResponse["data"]> => {
		const response = await apiClient.get(
			STATIONS_ENDPOINTS.scheduleSettings(locationId, stationId),
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object with settings and inheritance_info at same level
		return response.data.data;
	},

	updateStationScheduleSettings: async (
		locationId: string,
		stationId: string,
		orgId: number,
		data: UpdateScheduleSettingsRequest
	): Promise<LocationScheduleSettingsResponse["data"]> => {
		const response = await apiClient.patch(
			STATIONS_ENDPOINTS.scheduleSettings(locationId, stationId),
			data,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object with settings and inheritance_info at same level
		return response.data.data;
	},

	// Station Schedule Optimizer API methods
	getStationScheduleOptimizer: async (
		locationId: string,
		stationId: string,
		orgId: number
	): Promise<LocationScheduleOptimizerResponse["data"]> => {
		const response = await apiClient.get(
			STATIONS_ENDPOINTS.scheduleOptimizer(locationId, stationId),
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object with optimizer and inheritance_info at same level
		return response.data.data;
	},

	updateStationScheduleOptimizer: async (
		locationId: string,
		stationId: string,
		orgId: number,
		data: UpdateScheduleOptimizerRequest
	): Promise<LocationScheduleOptimizerResponse["data"]> => {
		const response = await apiClient.patch(
			STATIONS_ENDPOINTS.scheduleOptimizer(locationId, stationId),
			data,
			{
				headers: {
					"X-organizationId": orgId,
				},
			}
		);
		// Return full data object with optimizer and inheritance_info at same level
		return response.data.data;
	},
};
