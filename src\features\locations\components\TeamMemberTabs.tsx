import { useEffect, useState } from "react";
import {
	Search,
	Plus,
	Users,
	Upload,
	Settings2,
	Phone,
	Mail,
	MoreHorizontal,
	Edit,
	Eye,
	Trash2,
	Pen,
	Info,
	Funnel,
	Settings,
	X,
	Loader2,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/common/Checkbox";
import { InputText } from "@/components/common/InputText";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AddMemberSheet } from "./sheets/AddMemberSheet";
import { ViewMemberSheet } from "./sheets/ViewMemberSheet";
import { EditMemberSheet } from "./sheets/EditMemberSheet";
import { TeamMembersFilters } from "./sheets/TeamMembersFilters";
import { useMembers } from "../hooks/useMembers";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useDeleteActions } from "@/hooks/useDeleteActions";
import type { MemberData } from "../api/membersApi";
import { membersApi } from "../api/membersApi";
import { useUIStore } from "@/stores/uiStore";
import { useModal } from "@/lib/hooks/useModal";
import { useQueryClient } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useDebounce } from "@/hooks/useDebounce";

interface TeamMember {
	id: number;
	name: string;
	email: string;
	phone_number: string;
	roles: Array<{
		role: string;
		stations?: Array<{ id: number; name: string }>;
		locations?: Array<{ id: number; name: string }>;
	}>;
	has_accepted: boolean;
	// Additional computed fields for UI
	displayRole?: string;
	status?: "Active" | "Unverified" | "Pending";
	dateOnboarded?: string;
	avatar?: string;
}

interface TeamMembersTabProps {
	className?: string;
	locationId?: number;
	onRefetchReady?: (refetchFn: () => void) => void;
}

interface TeamMembersFilters {
	page: number;
	limit: number;
	search?: string;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
	status?: string;
	role?: string | string[];
}

interface TeamMembersResponse {
	data: TeamMember[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}

export function TeamMembersTab({
	className,
	locationId,
	onRefetchReady,
}: TeamMembersTabProps) {
	const { organizationId } = useOrganizationContext();
	const { deleteMember } = useDeleteActions();
	const { addToast } = useUIStore();
	const { openModal, closeModal } = useModal();
	const queryClient = useQueryClient();

	const [selectedMembers, setSelectedMembers] = useState<number[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [appliedFilters, setAppliedFilters] = useState<Record<string, any>>(
		{}
	);
	const [showAddMemberForm, setShowAddMemberForm] = useState(false);
	const [showFilterSheet, setShowFilterSheet] = useState(false);
	const [showMemberDetails, setShowMemberDetails] = useState(false);
	const [showEditMember, setShowEditMember] = useState(false);
	const [selectedMember, setSelectedMember] = useState<TeamMember | null>(
		null
	);

	// Debounce search term to avoid too many API calls
	const debouncedSearchTerm = useDebounce(searchTerm, 300);

	// Combine search and filters for API call - try multiple search parameter names
	const apiFilters = {
		...appliedFilters,
		...(debouncedSearchTerm && {
			search: debouncedSearchTerm,
			q: debouncedSearchTerm,
			query: debouncedSearchTerm,
			term: debouncedSearchTerm
		}),
	};

	// Debug: Log search parameters
	console.log("Team member search filters:", apiFilters);
	console.log("Search term:", debouncedSearchTerm);

	// Fetch members using the real API with search and filters
	const {
		data: membersResponse,
		isLoading,
		error,
		refetch,
		isFetching,
	} = useMembers({
		organizationId: organizationId ? String(organizationId) : "",
		enabled: !!organizationId,
		locationId: locationId,
		filters: apiFilters,
	});

	// Debug: Log API response
	console.log("Members API response:", membersResponse);

	// Pass refetch function to parent
	useEffect(() => {
		if (onRefetchReady && refetch) {
			onRefetchReady(refetch);
		}
	}, [onRefetchReady, refetch]);

	// Handle keyboard shortcuts
	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			// Escape key clears search
			if (event.key === "Escape" && searchTerm) {
				handleClearSearch();
			}
			// Ctrl/Cmd + K focuses search
			if ((event.ctrlKey || event.metaKey) && event.key === "k") {
				event.preventDefault();
				const searchInput = document.getElementById("search-field");
				searchInput?.focus();
			}
		};

		document.addEventListener("keydown", handleKeyDown);
		return () => {
			document.removeEventListener("keydown", handleKeyDown);
		};
	}, [searchTerm]);

	// Clear selections when search or filters change
	useEffect(() => {
		setSelectedMembers([]);
	}, [debouncedSearchTerm, appliedFilters]);

	// Transform API data to match UI expectations
	const transformMemberData = (apiMembers: MemberData[]): TeamMember[] => {
		return apiMembers.map((member) => {
			// Get primary role for display
			const primaryRole = member.roles[0]?.role || "Team Member";
			const displayRole = primaryRole
				.toLowerCase()
				.replace(/_/g, " ")
				.replace(/\b\w/g, (l) => l.toUpperCase());

			return {
				id: member.id,
				name: member.name,
				email: member.email,
				phone_number: member.phone_number,
				roles: member.roles,
				has_accepted: member.has_accepted,
				displayRole,
				status: member.has_accepted ? "Active" : "Unverified",
				dateOnboarded: member.on_boarded_date
					? new Date(member.on_boarded_date).toLocaleDateString("en-US", {
						year: "numeric",
						month: "short",
						day: "numeric",
					})
					: "-",
				avatar: undefined, // API doesn't provide avatar yet
			};
		});
	};

	// Transform API data to match UI expectations
	const allTransformedMembers = membersResponse?.data
		? transformMemberData(membersResponse.data)
		: [];

	// Add client-side filtering as backup to ensure only matching results are shown
	const filteredMembers = debouncedSearchTerm
		? allTransformedMembers.filter((member) => {
			const searchLower = debouncedSearchTerm.toLowerCase();
			return (
				member.name.toLowerCase().includes(searchLower) ||
				member.email.toLowerCase().includes(searchLower) ||
				(member.phone_number &&
					member.phone_number
						.toLowerCase()
						.includes(searchLower)) ||
				(member.displayRole &&
					member.displayRole
						.toLowerCase()
						.includes(searchLower))
			);
		})
		: allTransformedMembers;

	const teamMembersData: TeamMembersResponse = {
		data: filteredMembers,
		pagination: {
			page: 1,
			limit: 12,
			total: filteredMembers.length,
			totalPages: 1,
		},
	};

	const handleSelectAll = (checked: boolean) => {
		if (checked && teamMembersData?.data) {
			setSelectedMembers(teamMembersData.data.map((member) => member.id));
		} else {
			setSelectedMembers([]);
		}
	};

	const handleMemberSelection = (memberId: number, selected: boolean) => {
		if (selected) {
			setSelectedMembers((prev) => [...prev, memberId]);
		} else {
			setSelectedMembers((prev) => prev.filter((id) => id !== memberId));
		}
	};

	const handleAddMember = async (data: any) => {
		console.log("Adding new member:", data);
		// Invalidate and refetch members data
		if (organizationId) {
			await queryClient.invalidateQueries({
				queryKey: ["members", String(organizationId)],
			});
		}
		setShowAddMemberForm(false);
	};

	const handleViewMember = (member: TeamMember) => {
		setSelectedMember(member);
		setShowMemberDetails(true);
	};

	const handleEditMember = (member: TeamMember) => {
		setSelectedMember(member);
		setShowEditMember(true);
	};

	const handleEditFromView = (member: TeamMember) => {
		setShowMemberDetails(false);
		setSelectedMember(member);
		setShowEditMember(true);
	};

	const handleUpdateMember = async (data: any) => {
		console.log("Updating member:", data);
		setShowEditMember(false);
		// You would typically make an API call here to update the member
	};

	const handleDeleteMember = (member: TeamMember) => {
		deleteMember(member, {
			refetch: async () => {
				await refetch();
			},
		});
	};

	const handleResendInvite = (member: TeamMember) => {
		openModal("confirmation", {
			size: "md",
			data: {
				title: "Resend Invitation",
				message: `Are you sure you want to resend the invitation to ${member.name} (${member.email})?`,
				confirmText: "Resend Invitation",
				cancelText: "Cancel",
				variant: "default",
				onConfirm: () => performResendInvite(member),
			},
		});
	};

	const performResendInvite = async (member: TeamMember) => {
		closeModal();
		try {
			await membersApi.resendInvitation(organizationId?.toString() || "", member.id);
			addToast({
				type: "success",
				title: "Success",
				message: "Invitation sent again successfully",
			});
		} catch (error) {
			addToast({
				type: "error",
				title: "Error",
				message: "Failed to resend invitation. Please try again.",
			});
		}
	};

	const handleApplyFilters = (filterData: any) => {
		console.log("Applying filters:", filterData);

		// Transform filter data to API query parameters
		const apiFilters: Record<string, any> = {};

		// Add status filters
		if (filterData.status && filterData.status.length > 0) {
			apiFilters.status = filterData.status;
		}

		// Add role filters
		if (filterData.roles && filterData.roles.length > 0) {
			apiFilters.roles = filterData.roles;
		}

		// Add sorting (default to name)
		apiFilters.sort_by = "name";

		console.log("Transformed API filters:", apiFilters);
		setAppliedFilters(apiFilters);
	};

	const handleClearFilters = () => {
		setAppliedFilters({});
		setSearchTerm("");
	};

	const handleClearSearch = () => {
		setSearchTerm("");
	};

	// Check if any filters or search are applied
	const hasActiveFilters =
		Object.keys(appliedFilters).length > 0 || debouncedSearchTerm;

	const getStatusBadgeVariant = (status: string) => {
		switch (status) {
			case "Active":
				return "default";
			case "Unverified":
				return "secondary";
			case "Pending":
				return "outline";
			default:
				return "default";
		}
	};

	// Helper function to highlight search terms
	const highlightSearchTerm = (text: string, searchTerm: string) => {
		if (!searchTerm || !text) return text;

		// Handle multiple search terms
		const searchTerms = searchTerm
			.toLowerCase()
			.split(/\s+/)
			.filter((term) => term.length > 0);

		// Create a single regex that matches any of the search terms
		const escapedTerms = searchTerms.map((term) =>
			term.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
		);
		const regex = new RegExp(`(${escapedTerms.join("|")})`, "gi");
		const parts = text.split(regex);

		return parts.map((part, index) => {
			const isMatch = searchTerms.some(
				(term) => part.toLowerCase() === term.toLowerCase()
			);
			return isMatch ? (
				<span key={index} className="bg-yellow-200 font-medium">
					{part}
				</span>
			) : (
				part
			);
		});
	};

	return (
		<div className={className}>
			{/* Header */}
			<div className="flex items-center justify-between py-3 pl-4">
				<div>
					<h1 className="text-2xl font-bold">Team Members</h1>
					{debouncedSearchTerm && (
						<p className="mt-1 text-sm text-gray-500">
							{isFetching ? (
								"Searching..."
							) : (
								<>
									Showing results for "
									<span className="font-medium">
										{debouncedSearchTerm}
									</span>
									"
									{teamMembersData?.data?.length > 0 && (
										<span className="ml-1">
											({teamMembersData.data.length}{" "}
											found)
										</span>
									)}
								</>
							)}
						</p>
					)}
				</div>
				<div className="flex items-center gap-3">
					<div className="relative max-w-md flex-1">
						<InputText
							placeholder="Search members by name, email, or phone... (⌘K)"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pr-10 pl-10 focus-visible:ring-0"
							id="search-field"
							variant="with-icon"
							icon={
								isFetching && debouncedSearchTerm ? (
									<Loader2 className="h-4 w-4 animate-spin" />
								) : (
									<Search className="h-4 w-4" />
								)
							}
							iconPosition="left"
						/>
						{searchTerm && (
							<button
								onClick={handleClearSearch}
								className="absolute top-1/2 right-3 -translate-y-1/2 text-gray-400 hover:text-gray-600"
								title="Clear search (ESC)"
							>
								<X className="h-4 w-4" />
							</button>
						)}
					</div>
					<Button
						variant="outline"
						className={`cursor-pointer ${hasActiveFilters
							? "border-primary bg-primary/10 text-primary"
							: ""
							}`}
						size="icon"
						onClick={() => setShowFilterSheet(true)}
					>
						<Settings2 className="h-4 w-4" />
					</Button>
					{hasActiveFilters && (
						<Button
							variant="ghost"
							size="sm"
							onClick={handleClearFilters}
							className="text-gray-500 hover:text-gray-700"
						>
							Clear All (
							{Object.keys(appliedFilters).length +
								(debouncedSearchTerm ? 1 : 0)}{" "}
							active)
						</Button>
					)}
					<Button
						variant="outline"
						className="hover:bg-primary/90 cursor-pointer text-black hover:text-white"
						onClick={() => setShowAddMemberForm(true)}
					>
						<Plus className="mr-2 h-4 w-4" />
						Add a Member
					</Button>
				</div>
			</div>

			{/* Table */}
			<div className="flex w-full flex-col overflow-hidden rounded-lg border border-zinc-200">
				<div className="text-muted flex h-12 items-center justify-between border-b bg-gray-50 px-4 py-1">
					<div className="items-centerpr-4 flex w-5">
						<Checkbox
							label=""
							checked={
								selectedMembers.length ===
								teamMembersData?.data?.length &&
								teamMembersData?.data?.length > 0
							}
							className="cursor-pointer"
							onCheckedChange={handleSelectAll}
							disabled={isLoading}
						/>
					</div>
					<div className="flex w-64 items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Team Member
							</p>
						</div>
					</div>
					<div className="flex w-32 items-center px-3">
						<p className="text-sm text-gray-600">Phone Number</p>
					</div>
					<div className="flex w-72 items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Role
							</p>
						</div>
					</div>
					<div className="flex w-20 items-center px-3 text-left">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Status
							</p>
						</div>
					</div>
					<div className="flex w-44 items-center px-3">
						<div className="flex items-center gap-3">
							<p className="text-sm font-medium text-gray-600">
								Date Onboarded
							</p>
						</div>
					</div>
					<div className="flex w-24 items-center px-3">
						<div className="flex items-center gap-3">
							<p></p>
						</div>
					</div>
				</div>

				{/* Team Members Content */}
				{isLoading ? (
					<div className="flex flex-col">
						{Array.from({ length: 5 }).map((_, index) => (
							<div
								key={index}
								className="flex h-16 items-center justify-between border-b border-gray-100 px-4"
							>
								<div className="flex w-5 items-center pr-4">
									<Skeleton className="h-4 w-4" />
								</div>
								<div className="flex w-64 items-center px-3">
									<div className="flex items-center gap-3">
										<Skeleton className="h-10 w-10 rounded-full" />
										<div className="space-y-1">
											<Skeleton className="h-4 w-32" />
											<Skeleton className="h-3 w-48" />
										</div>
									</div>
								</div>
								<div className="flex w-28 items-center px-3">
									<Skeleton className="h-4 w-20" />
								</div>
								<div className="flex w-72 items-center px-3">
									<div className="flex w-full flex-col gap-1">
										<Skeleton className="h-6 w-20 rounded-sm" />
										<Skeleton className="h-6 w-16 rounded-sm" />
									</div>
								</div>
								<div className="flex w-20 items-center px-3 text-left">
									<Skeleton className="h-6 w-16 rounded-full" />
								</div>
								<div className="flex w-44 items-center px-3">
									<Skeleton className="h-4 w-32" />
								</div>
								<div className="flex w-24 items-center justify-end px-3">
									<div className="flex items-center gap-2">
										<Skeleton className="h-8 w-8" />
										<Skeleton className="h-8 w-8" />
										<Skeleton className="h-8 w-8" />
									</div>
								</div>
							</div>
						))}
					</div>
				) : error ? (
					<div className="py-12 text-center">
						<Users className="mx-auto h-12 w-12 text-gray-400" />
						<h3 className="mt-2 text-sm font-medium text-gray-900">
							Error loading team members
						</h3>
						<p className="mt-1 text-sm text-gray-500">
							{(error as any)?.message ||
								"Failed to fetch team members"}
						</p>
					</div>
				) : teamMembersData?.data?.length === 0 ? (
					<div className="py-12 text-center">
						<Users className="mx-auto h-12 w-12 text-gray-400" />
						<h3 className="mt-2 text-sm font-medium text-gray-900">
							{debouncedSearchTerm
								? "No team members found"
								: "No team members found"}
						</h3>
						<p className="mt-1 text-sm text-gray-500">
							{debouncedSearchTerm ? (
								<>
									No members match your search for "
									<span className="font-medium">
										{debouncedSearchTerm}
									</span>
									". Try adjusting your search terms.
								</>
							) : (
								"Get started by adding your first team member."
							)}
						</p>
						{debouncedSearchTerm ? (
							<div className="mt-4 flex justify-center gap-2">
								<Button
									variant="outline"
									onClick={handleClearSearch}
								>
									Clear Search
								</Button>
								<Button
									onClick={() => setShowAddMemberForm(true)}
								>
									<Plus className="mr-2 h-4 w-4" />
									Add Member
								</Button>
							</div>
						) : (
							<Button
								className="mt-4"
								onClick={() => setShowAddMemberForm(true)}
							>
								<Plus className="mr-2 h-4 w-4" />
								Add Member
							</Button>
						)}
					</div>
				) : (
					<>
						<div className="flex flex-col">
							{teamMembersData?.data?.map(
								(member: TeamMember) => (
									<div
										onClick={() => handleViewMember(member)}
										key={member.id}
										className="flex h-16 cursor-pointer items-center justify-between border-b border-gray-100 px-4 hover:bg-gray-50"
									>
										<div className="flex w-5 items-center pr-4">
											<Checkbox
												label=""
												checked={selectedMembers.includes(
													member.id
												)}
												className="cursor-pointer"
												onCheckedChange={(selected) =>
													handleMemberSelection(
														member.id,
														selected
													)
												}
											/>
										</div>
										<div className="flex w-64 px-3">
											<div className="flex items-center gap-3">
												<Avatar className="h-10 w-10">
													<AvatarImage
														src={
															member.avatar ||
															undefined
														}
														alt={member.name}
													/>
													<AvatarFallback className="bg-gray-200 text-sm text-gray-600">
														{member.name
															.split(" ")
															.map((n) => n[0])
															.join("")}
													</AvatarFallback>
												</Avatar>
												<div>
													<p className="text-sm font-medium text-gray-900">
														{highlightSearchTerm(
															member.name,
															debouncedSearchTerm
														)}
													</p>
													<p className="text-xs text-gray-500">
														{highlightSearchTerm(
															member.email,
															debouncedSearchTerm
														)}
													</p>
												</div>
											</div>
										</div>
										<div className="flex w-32 items-center px-2">
											<p className="text-sm text-gray-600">
												{highlightSearchTerm(
													member.phone_number,
													debouncedSearchTerm
												)}
											</p>
										</div>
										<div className="flex w-72 items-center px-3">
											<div className="flex w-full flex-col gap-1">
												{/* Always show at least the first role */}
												<p className="w-fit rounded-sm bg-[#F4F4F5] px-2 py-1 text-xs font-medium text-gray-900">
													{member.displayRole}
												</p>
												{/* Show second role if available */}
												{member.roles.length > 1 && (
													<p className="w-fit rounded-sm bg-[#E5E7EB] px-2 py-1 text-xs font-medium text-gray-700">
														{member.roles[1].role
															.toLowerCase()
															.replace(/_/g, " ")
															.replace(
																/\b\w/g,
																(l) =>
																	l.toUpperCase()
															)}
													</p>
												)}
												{/* Show additional roles count if more than 2 */}
												{member.roles.length > 2 && (
													<p className="text-xs text-gray-500">
														+
														{member.roles.length -
															2}{" "}
														more role
														{member.roles.length > 3
															? "s"
															: ""}
													</p>
												)}
											</div>
										</div>
										<div className="flex w-20 items-center px-3 text-left">
											<Badge
												variant={getStatusBadgeVariant(
													member.status || "Pending"
												)}
												className={
													((member.status ||
														"Pending") === "Active"
														? "border-green-200 bg-green-100 text-green-800 hover:bg-green-100"
														: (member.status ||
															"Pending") ===
															"Unverified"
															? "border-yellow-200 bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
															: "bg-gray-100 text-gray-800 hover:bg-gray-100") +
													" h-6 min-w-[48px] px-2 py-0.5 text-xs"
												}
											>
												{member.status || "Pending"}
											</Badge>
										</div>
										<div className="flex w-44 items-center px-3">
											<p className="truncate text-sm text-gray-600">
												{member.dateOnboarded}
											</p>
										</div>
										<div className="flex w-24 items-center justify-end px-3">
											<div className="flex items-center gap-2">
												<Button
													variant="ghost"
													size="sm"
													className="h-8 w-8 border p-0"
													onClick={(e) => {
														e.stopPropagation();
														handleDeleteMember(
															member
														);
													}}
												>
													<Trash2 className="h-4 w-4 text-gray-400" />
												</Button>
												<Button
													variant="ghost"
													size="sm"
													className="h-8 w-8 border p-0"
													onClick={(e) => {
														e.stopPropagation();
														handleEditMember(
															member
														);
													}}
												>
													<Pen className="h-4 w-4 text-gray-400" />
												</Button>
												{!member.has_accepted && (
													<Button
														variant="ghost"
														size="sm"
														className="h-8 w-8 border p-0"
														onClick={(e) => {
															e.stopPropagation();
															handleResendInvite(
																member
															);
														}}
														title="Resend Invitation"
													>
														<Mail className="h-4 w-4 text-gray-400" />
													</Button>
												)}
												<Button
													variant="ghost"
													size="sm"
													className="h-8 w-8 border p-0"
													onClick={(e) => {
														e.stopPropagation();
														handleViewMember(
															member
														);
													}}
												>
													<Info className="h-4 w-4 text-gray-400" />
												</Button>
											</div>
										</div>
									</div>
								)
							)}
						</div>

						{/* Note: Pagination removed to match ServicesTab pattern */}
					</>
				)}
			</div>

			{/* Add Member Sheet */}
			<AddMemberSheet
				open={showAddMemberForm}
				onOpenChange={setShowAddMemberForm}
				onSubmit={handleAddMember}
			/>

			{/* View Member Sheet */}
			<ViewMemberSheet
				open={showMemberDetails}
				onOpenChange={setShowMemberDetails}
				member={selectedMember}
				onEdit={handleEditFromView}
			/>

			{/* Edit Member Sheet */}
			<EditMemberSheet
				open={showEditMember}
				onOpenChange={setShowEditMember}
				member={selectedMember}
				onSubmit={handleUpdateMember}
			/>

			{/* Team Members Filters Sheet */}
			<TeamMembersFilters
				open={showFilterSheet}
				onOpenChange={setShowFilterSheet}
				onApplyFilters={handleApplyFilters}
			/>
		</div>
	);
}
