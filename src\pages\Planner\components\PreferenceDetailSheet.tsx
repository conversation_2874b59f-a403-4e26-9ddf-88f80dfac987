import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
} from '@/components/ui/sheet';

interface TimeSlot {
  date: string;
  startTime?: string;
  endTime?: string;
  isAllDay?: boolean;
}

interface PreferenceDetailData {
  id: string;
  title: string;
  type: 'Availability' | 'Frequency' | 'Unavailability';
  timeSlots?: TimeSlot[];
  occurrence?: string;
  duration?: {
    startDate: string;
    endDate: string;
  };
  frequency?: string;
  apply?: string;
  taggedProviders?: string;
}

interface PreferenceDetailSheetProps {
  isOpen: boolean;
  onClose: () => void;
  preference: PreferenceDetailData | null;
  onEdit: () => void;
}

export const PreferenceDetailSheet: React.FC<PreferenceDetailSheetProps> = ({
  isOpen,
  onClose,
  preference,
  onEdit
}) => {
  if (!preference) return null;

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'availability':
        return 'bg-green-100 text-green-800';
      case 'frequency':
        return 'bg-blue-100 text-blue-800';
      case 'unavailability':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-full max-w-2xl px-8 sm:max-w-2xl">
        <SheetHeader className="flex flex-col gap-y-6 px-0 pb-6">
          <SheetTitle className="text-2xl   font-semibold text-gray-900">
            {preference.title}
          </SheetTitle>
          <SheetDescription className="text-gray-500">
            Your Custom Preferences
          </SheetDescription>
        </SheetHeader>

        {/* Content */}
        <div className="flex-1 overflow-y-auto space-y-8">
          {/* Preference Type Badge */}
          <div>
            <Badge className={`${getTypeColor(preference.type)} text-sm px-3 py-1 rounded-full`}>
              {preference.type}
            </Badge>
          </div>

          {/* Time Slots Section */}
          {preference.timeSlots && preference.timeSlots.length > 0 && (
            <div className="space-y-4">
              {/* Header */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-sm font-medium text-gray-700">Date</div>
                  <div className="text-sm font-medium text-gray-700">Time Slots</div>
                </div>
              </div>
              
              {/* Time Slot Rows */}
              <div className="space-y-0 px-4">
                {preference.timeSlots.map((slot, index) => (
                  <div key={index} className="grid grid-cols-2 gap-4 py-4 border-b border-gray-200 last:border-b-0">
                    <div className="text-sm text-gray-900 font-medium">{slot.date}</div>
                    <div className="text-sm text-gray-900">
                      {slot.isAllDay ? (
                        'All Day'
                      ) : (
                        <>
                          {slot.startTime}
                          <span className="mx-3 text-gray-400">-</span>
                          {slot.endTime}
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Occurrence Section */}
          {preference.occurrence && (
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700">Occurrence</h3>
              </div>
              <div className="py-2 px-4">
                <div className="text-sm text-gray-900">{preference.occurrence}</div>
              </div>
            </div>
          )}

          {/* Duration Section */}
          {preference.duration && (
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700">Duration</h3>
              </div>
              <div className="py-2 px-4">
                <div className="flex items-center text-sm text-gray-900">
                  <span>{preference.duration.startDate}</span>
                  <span className="mx-4 text-gray-400">-</span>
                  <span>{preference.duration.endDate}</span>
                </div>
              </div>
            </div>
          )}

          {/* Additional Details */}
          {(preference.frequency || preference.apply || preference.taggedProviders) && (
            <div className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700">Additional Details</h3>
              </div>
              
              <div className="space-y-4">
                {preference.frequency && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-200">
                    <span className="text-sm text-gray-600">Frequency</span>
                    <span className="text-sm text-gray-900">{preference.frequency}</span>
                  </div>
                )}
                
                {preference.apply && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-200">
                    <span className="text-sm text-gray-600">Apply</span>
                    <Badge variant="secondary" className="text-xs">
                      {preference.apply}
                    </Badge>
                  </div>
                )}
                
                {preference.taggedProviders && (
                  <div className="flex justify-between items-center py-2">
                    <span className="text-sm text-gray-600">Tagged Providers</span>
                    <Badge variant="secondary" className="text-xs">
                      {preference.taggedProviders}
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <SheetFooter className="pt-8 pb-4 px-4">
          <div className="flex justify-between w-full gap-4">
            <Button 
              variant="outline"
              onClick={onClose}
              className="flex-1 h-12 text-base"
            >
              Back
            </Button>
            <Button 
              onClick={onEdit}
              className="bg-blue-600 hover:bg-blue-700 flex-1 h-12 text-base"
            >
              Edit
            </Button>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};