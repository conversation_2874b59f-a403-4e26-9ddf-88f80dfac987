import { useEffect, useState } from "react";
import clsx from "clsx";
import { X } from "lucide-react";
import { Button } from "@/components/ui/Button/Button";
import { useForm, type UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { AddAutomationSchema, type AddAutomationSchemaType } from "../schema/add-automation";
import { Form } from "@/components/ui/form";
import FormInputField from "../components/form/FormInputField";
import FormDatePickerField from "../components/form/FormDatePickerField";

type AddAutomationProps = {
    open: boolean;
    onClose?: () => void;
    addAutomationForm: UseFormReturn<AddAutomationSchemaType>;
};

export default function AddAutomation({ open, onClose, addAutomationForm }: AddAutomationProps) {
    const [fullyOpen, setFullyOpen] = useState(false);

    useEffect(() => {
        if (open) {
            setTimeout(() => {
                setFullyOpen(open);
            }, 10);
        }
    }, [open]);



    const onSubmit = (data: AddAutomationSchemaType) => {

    };

    return (
        <div className={clsx("z-50 fixed top-5 right-[4%] w-[30rem] bg-white border border-[#00589340] shadow-[0px_2px_4px_-1px_#0000000F,0px_0px_6px_-1px_#0000001A] transition-transform duration-300 ease-in-out will-change-transform rounded-xl py-5", fullyOpen ? "translate-x-0" : "translate-x-[115%]")}>
            <div className="flex items-center justify-between">
                <h1 className="text-[#27272A] text-xl font-semibold ml-6">Automation Details</h1>
                <Button
                    variant="ghost"
                    className="!px-0 w-11 h-10.5 rounded-lg cursor-pointer mr-3" onClick={onClose}>
                    <X className="text-base" color="#27272A" />
                </Button>
            </div>

            <div className="min-h-[85vh] h-full flex flex-col justify-between">
                <Form {...addAutomationForm}>
                    <form onSubmit={addAutomationForm.handleSubmit(onSubmit)} className="px-5 mt-9 flex flex-col gap-y-6">
                        <FormInputField
                            form={addAutomationForm}
                            name="title"
                            label="Automation title *"
                            inputStyles=""
                            labelStyles="text-[#27272A] font-medium text-sm"
                            placeholder="Enter automation title"
                        />
                        <div>
                            <FormInputField
                                form={addAutomationForm}
                                name="description"
                                label={(
                                    <p className="text-[#27272A] font-medium text-sm">
                                        Description{" "}
                                        <span className="text-[#71717A] font-normal text-sm">
                                            (Optional)
                                        </span>
                                    </p>
                                )}
                                placeholder="Enter a description..."
                                inputStyles="!min-h-[130px] resize-none border border-[#E4E4E7] rounded-lg"
                                textarea
                                maxLength={500}
                            />
                            <p className="text-[#71717A] font-normal text-sm mt-1">
                                <span className="font-medium text-[#27272A]">{addAutomationForm.watch("description")?.length || 0}</span>/500
                            </p>
                        </div>
                        <div>
                            <FormDatePickerField
                                form={addAutomationForm}
                                name="startDate"
                                label="Start date *"
                                placeholder="Select start date"
                                labelStyles=" text-[#27272A] font-medium text-sm"
                            />
                            <p className="font-normal text-sm text-[#71717A] mt-1.5">Start date should be current or future date.</p>
                        </div>
                        <div>
                            <FormDatePickerField
                                form={addAutomationForm}
                                name="endDate"
                                label="End date *"
                                placeholder="Select end date"
                                labelStyles=" text-[#27272A] font-medium text-sm"
                            />
                            <p className="font-normal text-sm text-[#71717A] mt-1.5">End date should be current or future date.</p>
                        </div>
                    </form>
                </Form>
                <div className="flex justify-end px-4">
                    <Button className="w-[6rem] cursor-pointer" onClick={() => {
                        addAutomationForm.handleSubmit(onSubmit);
                    }}>
                        Save
                    </Button>
                </div>
            </div>

        </div>
    );
}