import { useDeleteConfirmation } from "@/contexts/DeleteConfirmationContext";
import { useDeleteService } from "@/features/locations/hooks/useServiceMutations";
import { useDeleteMember } from "@/features/locations/hooks/useMembers";
import { useDeleteStation } from "@/features/locations/hooks/useStations";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import type { ServiceData } from "@/features/locations/api/servicesApi";
import type { StationData } from "@/features/locations/api/stationsApi";

interface TeamMember {
	id: number;
	name: string;
	email: string;
}

export function useDeleteActions() {
	const { showDeleteConfirmation } = useDeleteConfirmation();
	const { organizationId } = useOrganizationContext();
	const deleteServiceMutation = useDeleteService();
	const deleteMemberMutation = useDeleteMember({
		organizationId: organizationId ? String(organizationId) : "",
	});
	const deleteStationMutation = useDeleteStation();

	const deleteService = (
		service: ServiceData,
		options?: {
			onSuccess?: () => void;
			refetch?: () => void | Promise<void>;
		}
	) => {
		showDeleteConfirmation({
			title: "Delete Service",
			description: `Are you sure you want to delete "${service.name}"? This action cannot be undone and will permanently remove the service and all associated data.`,
			confirmText: "Delete Service",
			cancelText: "Cancel",
			onConfirm: async () => {
				await deleteServiceMutation.mutateAsync(service.id.toString());
				// Call refetch if provided
				if (options?.refetch) {
					await options.refetch();
				}
				// Call onSuccess callback if provided
				options?.onSuccess?.();
			},
		});
	};

	const deleteMember = (
		member: TeamMember,
		options?: {
			onSuccess?: () => void;
			refetch?: () => void | Promise<void>;
		}
	) => {
		showDeleteConfirmation({
			title: "Delete Team Member",
			description: `Are you sure you want to delete "${member.name}"? This action cannot be undone and will permanently remove the team member and revoke their access.`,
			confirmText: "Delete Member",
			cancelText: "Cancel",
			onConfirm: async () => {
				await deleteMemberMutation.mutateAsync(member.id);
				// Call refetch if provided
				if (options?.refetch) {
					await options.refetch();
				}
				// Call onSuccess callback if provided
				options?.onSuccess?.();
			},
		});
	};

	const deleteLocation = (
		locationId: string,
		locationName: string,
		onSuccess?: () => void
	) => {
		showDeleteConfirmation({
			title: "Delete Location",
			description: `Are you sure you want to delete "${locationName}"? This will permanently remove all location details and history.`,
			confirmText: "Delete Location",
			cancelText: "Cancel",
			onConfirm: async () => {
				// TODO: Implement delete location API call
				// await deleteLocationAPI(locationId);
				console.log("Delete location:", locationId);
				onSuccess?.();
			},
		});
	};

	const deleteProvider = (
		providerId: string,
		providerName: string,
		onSuccess?: () => void
	) => {
		showDeleteConfirmation({
			title: "Delete Provider",
			description: `Are you sure you want to delete "${providerName}"? This action cannot be undone.`,
			confirmText: "Delete Provider",
			cancelText: "Cancel",
			onConfirm: async () => {
				// TODO: Implement delete provider API call
				// await deleteProviderAPI(providerId);
				console.log("Delete provider:", providerId);
				onSuccess?.();
			},
		});
	};

	const deleteStation = (
		station: StationData,
		options?: {
			onSuccess?: () => void;
			refetch?: () => void | Promise<void>;
		}
	) => {
		showDeleteConfirmation({
			title: "Delete Station",
			description: `Are you sure you want to delete "${station.name}"? This action cannot be undone and will permanently remove the station and all associated data.`,
			confirmText: "Delete Station",
			cancelText: "Cancel",
			onConfirm: async () => {
				if (!organizationId) {
					throw new Error("Organization ID is required");
				}
				await deleteStationMutation.mutateAsync({
					stationId: station.id.toString(),
					organizationId,
				});
				// Call refetch if provided
				if (options?.refetch) {
					await options.refetch();
				}
				// Call onSuccess callback if provided
				options?.onSuccess?.();
			},
		});
	};

	const deleteStationProvider = (
		provider: { id: string; name: string },
		options?: {
			onSuccess?: () => void;
			refetch?: () => void | Promise<void>;
		}
	) => {
		showDeleteConfirmation({
			title: "Remove Provider",
			description: `Are you sure you want to remove "${provider.name}" from this station? This will revoke their access to provide services at this location.`,
			confirmText: "Remove Provider",
			cancelText: "Cancel",
			onConfirm: async () => {
				try {
					// TODO: Implement delete station provider API call
					// await deleteStationProviderAPI(provider.id);
					console.log("Removing provider from station:", provider.id);

					// Call refetch if provided
					if (options?.refetch) {
						await options.refetch();
					}
					// Call onSuccess callback if provided
					options?.onSuccess?.();
				} catch (error) {
					console.error(
						"Failed to remove provider from station:",
						error
					);
					throw error;
				}
			},
		});
	};

	return {
		deleteService,
		deleteMember,
		deleteLocation,
		deleteProvider,
		deleteStation,
		deleteStationProvider,
	};
}
