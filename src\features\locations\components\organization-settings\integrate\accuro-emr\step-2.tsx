import { Button } from "@/components/ui/Button/Button";
import { Input } from "@/components/ui/input";

export default function IntegrateAccuroEHRStep2() {
    return (
        <div>
            <h1 className="text-[#09090B] font-medium text-lg mb-3">Step 2</h1>
            <div className="bg-[#F4F4F5] py-5 px-6 rounded-xl">
                <h1 className="text-[#27272A] font-medium text-base">
                    Receive Username and Unique ID from Accuro
                </h1>
                <div className="mt-6 py-4 px-3 bg-white rounded-md">
                    <h1 className="">EMR Integration Information </h1>
                    <p className="text-[#71717A] font-light mt-1">Click on
                        Enter your details from Accuro below
                    </p>
                    <div className="mt-5">
                        <label htmlFor="uuid">
                            Business UUID
                        </label>
                        <Input
                            type="text"
                            className="bg-white py-3 mt-2"
                        />
                    </div>
                    <div className="flex items-center justify-end mt-3 gap-x-4">
                        <Button variant="secondary" className="w-[8rem]">
                            Cancel
                        </Button>
                        <Button className="w-[8rem] mr-2">
                            Add
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
}