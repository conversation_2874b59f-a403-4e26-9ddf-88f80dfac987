import { useQuery } from "@tanstack/react-query";
import { servicesApi } from "../api/servicesApi";
import type { ServiceData } from "../api/servicesApi";
import { queryKeys } from "@/lib/query/keys";

interface UseServiceParams {
	serviceId?: number;
	organizationId?: number;
	enabled?: boolean;
	options?: {
		basic?: boolean;
		include?: string[];
	};
}

export function useService({
	serviceId,
	organizationId,
	enabled = true,
	options = { basic: false, include: ["forms", "locations", "stations"] },
}: UseServiceParams) {
	return useQuery<
		{ success: boolean; message: string; data: ServiceData },
		Error
	>({
		queryKey: queryKeys.services?.detail
			? [
					...queryKeys.services.detail(serviceId!?.toString()),
					organizationId,
					options,
				]
			: ["service", serviceId, organizationId, options],
		queryFn: () => {
			if (!serviceId || !organizationId) {
				throw new Error("Service ID and Organization ID are required");
			}
			return servicesApi.getService(serviceId, organizationId, options);
		},
		enabled: enabled && !!serviceId && !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false,
	});
}
