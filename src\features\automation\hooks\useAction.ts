import { useOrganizationContext } from "@/features/organizations/context";
import { actionApi, type GetActionTypesData } from "../api/action";
import { useQuery } from "@tanstack/react-query";

export function useGetActionTypes({
    business_id,
    enabled
}: {
    business_id: string,
    enabled: boolean,
}) {
    const organizationId = useOrganizationContext()?.organizationId
    return useQuery<{
        success: boolean,
        data: GetActionTypesData[],
    }>({
        queryKey: ["actions", organizationId, business_id],
        queryFn: () => {
            if (!business_id || !organizationId) {
                throw new Error("Business ID and Organization ID are required")
            }
            return actionApi.getActionTypes(organizationId, business_id)
        },
        enabled
    })

}